#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة استيراد عدة شحنات من ملف إكسيل واحد
"""

import sys
import pandas as pd
from datetime import datetime, timedelta
import os

def create_multiple_shipments_sample():
    """إنشاء ملف إكسيل يحتوي على عدة شحنات للاختبار"""
    try:
        print("🔄 جاري إنشاء ملف إكسيل متعدد الشحنات...")
        
        # بيانات عدة شحنات
        shipments_data = []
        
        # شحنة 1
        shipments_data.append({
            'التاريخ': '2025-07-06',
            'المورد': 'شركة الإمارات للتجارة',
            'بوليصة الشحن': 'BOL-2025-001',
            'ملاحظات': 'شحنة عاجلة - أولوية عالية',
            'حالة الشحنة': 'تم الشحن',
            'حالة الإفراج': 'مع الافراج',
            'شركة الشحن': 'الخطوط الإماراتية',
            'رقم DHL': 'DHL-001-2025',
            'ميناء الوصول': 'ميناء جبل علي',
            'تاريخ الوصول المتوقع': '2025-07-15',
            'رقم الحاوية': 'EMAU1234567, EMAU1234568, EMAU1234569'
        })
        
        # شحنة 2
        shipments_data.append({
            'التاريخ': '2025-07-07',
            'المورد': 'مؤسسة الكويت التجارية',
            'بوليصة الشحن': 'BOL-2025-002',
            'ملاحظات': 'شحنة منتجات غذائية - تحتاج تبريد',
            'حالة الشحنة': 'في الطريق',
            'حالة الإفراج': 'بدون الافراج',
            'شركة الشحن': 'الخطوط الكويتية',
            'رقم DHL': 'DHL-002-2025',
            'ميناء الوصول': 'ميناء الشويخ',
            'تاريخ الوصول المتوقع': '2025-07-20',
            'رقم الحاوية': 'KWTU9876543; KWTU9876544'
        })
        
        # شحنة 3
        shipments_data.append({
            'التاريخ': '2025-07-08',
            'المورد': 'شركة قطر للاستيراد',
            'بوليصة الشحن': 'BOL-2025-003',
            'ملاحظات': 'شحنة إلكترونيات - حساسة للرطوبة',
            'حالة الشحنة': 'قيد التحضير',
            'حالة الإفراج': 'مع الافراج',
            'شركة الشحن': 'الخطوط القطرية',
            'رقم DHL': 'DHL-003-2025',
            'ميناء الوصول': 'ميناء حمد',
            'تاريخ الوصول المتوقع': '2025-07-25',
            'رقم الحاوية': 'QATU5555555|QATU5555556|QATU5555557|QATU5555558'
        })
        
        # شحنة 4
        shipments_data.append({
            'التاريخ': '2025-07-09',
            'المورد': 'مجموعة البحرين التجارية',
            'بوليصة الشحن': 'BOL-2025-004',
            'ملاحظات': 'شحنة مواد بناء - ثقيلة',
            'حالة الشحنة': 'مؤكدة',
            'حالة الإفراج': 'بدون الافراج',
            'شركة الشحن': 'خطوط البحرين',
            'رقم DHL': 'DHL-004-2025',
            'ميناء الوصول': 'ميناء خليفة بن سلمان',
            'تاريخ الوصول المتوقع': '2025-07-30',
            'رقم الحاوية': 'BHTU1111111/BHTU1111112'
        })
        
        # شحنة 5
        shipments_data.append({
            'التاريخ': '2025-07-10',
            'المورد': 'شركة عُمان للتصدير',
            'بوليصة الشحن': 'BOL-2025-005',
            'ملاحظات': 'شحنة منتجات زراعية طازجة',
            'حالة الشحنة': 'تحت المراجعة',
            'حالة الإفراج': 'مع الافراج',
            'شركة الشحن': 'الخطوط العُمانية',
            'رقم DHL': 'DHL-005-2025',
            'ميناء الوصول': 'ميناء صلالة',
            'تاريخ الوصول المتوقع': '2025-08-05',
            'رقم الحاوية': 'OMTU7777777-OMTU7777778-OMTU7777779'
        })
        
        # شحنة 6 - بدون حاويات لاختبار الحالات الخاصة
        shipments_data.append({
            'التاريخ': '2025-07-11',
            'المورد': 'شركة السعودية للشحن',
            'بوليصة الشحن': 'BOL-2025-006',
            'ملاحظات': 'شحنة جوية - بدون حاويات',
            'حالة الشحنة': 'جاهزة للشحن',
            'حالة الإفراج': 'بدون الافراج',
            'شركة الشحن': 'الخطوط السعودية',
            'رقم DHL': 'DHL-006-2025',
            'ميناء الوصول': 'مطار الملك عبدالعزيز',
            'تاريخ الوصول المتوقع': '2025-07-12',
            'رقم الحاوية': ''  # بدون حاويات
        })
        
        # إنشاء DataFrame
        df = pd.DataFrame(shipments_data)
        
        # حفظ الملف
        filename = 'multiple_shipments_sample.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف الإكسيل: {filename}")
        print(f"📊 عدد الشحنات: {len(shipments_data)}")
        print("\n📋 معاينة البيانات:")
        print("=" * 80)
        
        for i, shipment in enumerate(shipments_data, 1):
            print(f"\n🚢 الشحنة {i}:")
            print(f"   📅 التاريخ: {shipment['التاريخ']}")
            print(f"   🏢 المورد: {shipment['المورد']}")
            print(f"   📄 بوليصة الشحن: {shipment['بوليصة الشحن']}")
            print(f"   📦 الحاويات: {shipment['رقم الحاوية'] if shipment['رقم الحاوية'] else 'بدون حاويات'}")
            print(f"   🚛 شركة الشحن: {shipment['شركة الشحن']}")
            print(f"   🏁 حالة الشحنة: {shipment['حالة الشحنة']}")
        
        print("\n" + "=" * 80)
        print("🎯 الملف جاهز للاستيراد!")
        print("📝 يمكنك الآن:")
        print("   1. فتح شاشة الشحنة الجديدة")
        print("   2. النقر على زر 'استيراد إكسيل'")
        print("   3. اختيار 'استيراد جميع الأسطر (6 شحنة)'")
        print("   4. مشاهدة النتائج في قاعدة البيانات")
        
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الإكسيل: {e}")
        return None

def test_container_parsing():
    """اختبار تحليل الحاويات المتعددة"""
    print("\n🧪 اختبار تحليل الحاويات المتعددة:")
    print("=" * 50)
    
    test_cases = [
        "EMAU1234567, EMAU1234568, EMAU1234569",  # فاصلة إنجليزية
        "KWTU9876543; KWTU9876544",               # فاصلة منقوطة إنجليزية
        "QATU5555555|QATU5555556|QATU5555557",    # خط عمودي
        "BHTU1111111/BHTU1111112",                # شرطة مائلة
        "OMTU7777777-OMTU7777778-OMTU7777779",    # شرطة
        "SATU9999999",                            # حاوية واحدة
        ""                                        # فارغ
    ]
    
    def parse_containers_test(container_data):
        """نسخة مبسطة من دالة تحليل الحاويات للاختبار"""
        try:
            containers = []
            separators = [',', '،', ';', '؛', '\n', '\r\n', '|', '-', '/']
            
            container_data = str(container_data).strip()
            if not container_data:
                return []
            
            used_separator = None
            for sep in separators:
                if sep in container_data:
                    used_separator = sep
                    break
            
            if used_separator:
                parts = container_data.split(used_separator)
                for part in parts:
                    cleaned_part = part.strip()
                    if cleaned_part:
                        containers.append(cleaned_part)
            else:
                containers.append(container_data)
            
            return containers
            
        except Exception as e:
            print(f"خطأ في تحليل الحاويات: {e}")
            return [container_data] if container_data else []
    
    for i, test_case in enumerate(test_cases, 1):
        result = parse_containers_test(test_case)
        print(f"{i}. المدخل: '{test_case}'")
        print(f"   النتيجة: {result} ({len(result)} حاوية)")
        print()

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار ميزة استيراد عدة شحنات من الإكسيل")
    print("=" * 60)
    
    # إنشاء ملف الإكسيل
    filename = create_multiple_shipments_sample()
    
    if filename:
        # اختبار تحليل الحاويات
        test_container_parsing()
        
        print("\n🎉 تم إنشاء جميع ملفات الاختبار بنجاح!")
        print(f"📁 الملف الرئيسي: {filename}")
        print("\n🔧 لاختبار الميزة:")
        print("   1. شغل التطبيق")
        print("   2. افتح شاشة الشحنة الجديدة")
        print("   3. انقر على زر 'استيراد إكسيل'")
        print("   4. اختر الملف المنشأ")
        print("   5. اختر نوع الاستيراد (فردي أو متعدد)")
        
        # فتح النافذة للاختبار المباشر
        try:
            print("\n🖥️ فتح نافذة الاختبار...")
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import Qt
            
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
            
            # استيراد النافذة
            sys.path.append('src')
            from ui.shipments.new_shipment_window import NewShipmentWindow
            
            window = NewShipmentWindow()
            window.show()
            
            print("✅ تم فتح نافذة الشحنة الجديدة")
            print("🎯 جرب الآن زر 'استيراد إكسيل' لاختبار الميزة الجديدة!")
            
            sys.exit(app.exec())
            
        except ImportError as e:
            print(f"⚠️ لا يمكن فتح النافذة: {e}")
            print("💡 يمكنك تشغيل التطبيق يدوياً واختبار الميزة")
        except Exception as e:
            print(f"❌ خطأ في فتح النافذة: {e}")
    
    else:
        print("❌ فشل في إنشاء ملف الاختبار")

if __name__ == "__main__":
    main()
