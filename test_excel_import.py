#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة استيراد البيانات من الإكسيل في شاشة الشحنة الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_excel_import():
    """اختبار ميزة الاستيراد من الإكسيل"""
    try:
        # إنشاء ملف الإكسيل النموذجي أولاً
        print("🔄 إنشاء ملف الإكسيل النموذجي...")
        
        import pandas as pd
        from datetime import datetime, timedelta
        
        # إنشاء بيانات نموذجية
        data = {
            'التاريخ': [datetime.now().strftime('%Y-%m-%d')],
            'المورد': ['شركة الإمارات للتجارة'],
            'بوليصة الشحن': ['BOL-2025-001'],
            'ملاحظات': ['شحنة مستوردة من ملف إكسيل - تحتوي على مواد إلكترونية'],
            'شركة الشحن': ['شركة الخليج للشحن'],
            'رقم DHL': ['DHL123456789'],
            'ميناء الوصول': ['ميناء الملك عبدالعزيز'],
            'تاريخ الوصول المتوقع': [(datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')],
            'رقم الحاوية': ['MSKU1234567']
        }
        
        df = pd.DataFrame(data)
        filename = 'sample_shipment_data.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف الإكسيل: {filename}")
        print("\n📋 محتويات الملف:")
        print(df.to_string(index=False))
        
        # تشغيل نافذة الشحنة الجديدة
        print("\n🚀 تشغيل نافذة الشحنة الجديدة...")
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        window = NewShipmentWindow()
        window.show()
        
        print("✅ تم فتح نافذة الشحنة الجديدة")
        print("\n📝 تعليمات الاختبار:")
        print("1. انقر على زر 'استيراد إكسيل' في شريط الأدوات")
        print("2. اختر الملف: sample_shipment_data.xlsx")
        print("3. تحقق من ملء الحقول التالية:")
        print("   • التاريخ")
        print("   • المورد")
        print("   • بوليصة الشحن")
        print("   • ملاحظات")
        print("   • شركة الشحن")
        print("   • رقم DHL")
        print("   • ميناء الوصول")
        print("   • تاريخ الوصول المتوقع")
        print("   • رقم الحاوية (في تبويب الحاويات)")
        print("\n🔍 تحقق من:")
        print("   • ظهور رسالة نجاح الاستيراد")
        print("   • ملء جميع الحقول بالبيانات الصحيحة")
        print("   • إضافة الحاوية في تبويب الحاويات")
        
        sys.exit(app.exec())
        
    except ImportError as e:
        print("❌ خطأ: مكتبات مطلوبة غير مثبتة")
        print("يرجى تثبيت المكتبات المطلوبة:")
        print("pip install pandas openpyxl PySide6")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_excel_import()
