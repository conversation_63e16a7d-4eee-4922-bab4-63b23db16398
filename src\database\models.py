# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Date, Boolean, Text, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Company(Base):
    """جدول بيانات الشركة"""
    __tablename__ = 'companies'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, comment='اسم الشركة')
    name_en = Column(String(200), comment='اسم الشركة بالإنجليزية')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    email = Column(String(100), comment='البريد الإلكتروني')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    commercial_register = Column(String(50), comment='السجل التجاري')
    logo_path = Column(String(500), comment='مسار الشعار')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Branch(Base):
    """جدول الفروع"""
    __tablename__ = 'branches'
    
    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'), nullable=False)
    name = Column(String(200), nullable=False, comment='اسم الفرع')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    manager_name = Column(String(100), comment='اسم المدير')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    
    company = relationship("Company", back_populates="branches")

Company.branches = relationship("Branch", back_populates="company")

class User(Base):
    """جدول المستخدمين"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False, comment='اسم المستخدم')
    password_hash = Column(String(255), nullable=False, comment='كلمة المرور المشفرة')
    full_name = Column(String(100), nullable=False, comment='الاسم الكامل')
    email = Column(String(100), comment='البريد الإلكتروني')
    phone = Column(String(50), comment='الهاتف')
    role = Column(String(50), default='user', comment='الدور')
    is_active = Column(Boolean, default=True, comment='نشط')
    last_login = Column(DateTime, comment='آخر تسجيل دخول')
    created_at = Column(DateTime, default=datetime.now)

class Currency(Base):
    """جدول العملات"""
    __tablename__ = 'currencies'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(3), unique=True, nullable=False, comment='رمز العملة')
    name = Column(String(100), nullable=False, comment='اسم العملة')
    name_en = Column(String(100), comment='اسم العملة بالإنجليزية')
    symbol = Column(String(10), comment='رمز العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    is_base = Column(Boolean, default=False, comment='العملة الأساسية')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    suppliers = relationship("SupplierCurrency", back_populates="currency")


class FiscalYear(Base):
    """جدول السنوات المالية"""
    __tablename__ = 'fiscal_years'
    
    id = Column(Integer, primary_key=True)
    year = Column(Integer, nullable=False, comment='السنة')
    start_date = Column(DateTime, nullable=False, comment='تاريخ البداية')
    end_date = Column(DateTime, nullable=False, comment='تاريخ النهاية')
    is_current = Column(Boolean, default=False, comment='السنة الحالية')
    is_closed = Column(Boolean, default=False, comment='مغلقة')
    created_at = Column(DateTime, default=datetime.now)

class UnitOfMeasure(Base):
    """جدول وحدات القياس"""
    __tablename__ = 'units_of_measure'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, comment='اسم الوحدة')
    name_en = Column(String(100), comment='اسم الوحدة بالإنجليزية')
    symbol = Column(String(10), comment='رمز الوحدة')
    symbol_en = Column(String(10), comment='رمز الوحدة بالإنجليزية')
    description = Column(Text, comment='وصف الوحدة')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class ItemGroup(Base):
    """جدول مجموعات الأصناف"""
    __tablename__ = 'item_groups'

    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, comment='اسم المجموعة')
    name_en = Column(String(200), comment='اسم المجموعة بالإنجليزية')
    code = Column(String(50), unique=True, comment='كود المجموعة')
    description = Column(Text, comment='الوصف')
    parent_id = Column(Integer, ForeignKey('item_groups.id'), comment='المجموعة الأب')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    parent = relationship("ItemGroup", remote_side=[id])
    children = relationship("ItemGroup")

class Item(Base):
    """جدول الأصناف"""
    __tablename__ = 'items'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, nullable=False, comment='كود الصنف')
    name = Column(String(200), nullable=False, comment='اسم الصنف')
    name_en = Column(String(200), comment='اسم الصنف بالإنجليزية')
    description = Column(Text, comment='الوصف')
    group_id = Column(Integer, ForeignKey('item_groups.id'), comment='مجموعة الصنف')
    unit_id = Column(Integer, ForeignKey('units_of_measure.id'), comment='وحدة القياس')
    cost_price = Column(Float, default=0.0, comment='سعر التكلفة')
    selling_price = Column(Float, default=0.0, comment='سعر البيع')
    weight = Column(Float, comment='الوزن')
    dimensions = Column(String(100), comment='الأبعاد')
    total_package = Column(Integer, default=0, comment='عبوة الكلي')
    partial_package = Column(Integer, default=0, comment='عبوة الجزئي')
    gram_weight = Column(Float, default=0.0, comment='الوزن بالجرام')
    item_weight = Column(Float, default=0.0, comment='وزن الصنف')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    group = relationship("ItemGroup")
    unit = relationship("UnitOfMeasure")

class Supplier(Base):
    """جدول الموردين"""
    __tablename__ = 'suppliers'

    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, comment='كود المورد')
    name = Column(String(200), nullable=False, comment='اسم المورد')
    name_en = Column(String(200), comment='اسم المورد بالإنجليزية')
    supplier_type = Column(String(50), comment='نوع المورد')
    contact_person = Column(String(100), comment='الشخص المسؤول')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    commercial_register = Column(String(50), comment='السجل التجاري')

    # بيانات الاتصال
    phone = Column(String(50), comment='الهاتف')
    mobile = Column(String(50), comment='الجوال')
    email = Column(String(100), comment='البريد الإلكتروني')
    website = Column(String(200), comment='الموقع الإلكتروني')

    # العنوان
    country = Column(String(100), comment='الدولة')
    city = Column(String(100), comment='المدينة')
    address = Column(Text, comment='العنوان')
    postal_code = Column(String(20), comment='الرمز البريدي')

    # الإعدادات المالية
    credit_limit = Column(Float, default=0.0, comment='حد الائتمان')
    payment_terms = Column(Integer, default=0, comment='مدة السداد بالأيام')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    currencies = relationship("SupplierCurrency", back_populates="supplier")
    representatives = relationship("SupplierRepresentative", back_populates="supplier")


class SupplierCurrency(Base):
    """جدول ربط الموردين بالعملات"""
    __tablename__ = 'supplier_currencies'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='معرف المورد')
    currency_id = Column(Integer, ForeignKey('currencies.id'), nullable=False, comment='معرف العملة')
    is_preferred = Column(Boolean, default=False, comment='العملة المفضلة للمورد')
    exchange_rate_override = Column(Float, comment='سعر صرف مخصص للمورد')
    notes = Column(Text, comment='ملاحظات')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier", back_populates="currencies")
    currency = relationship("Currency", back_populates="suppliers")

    # فهرس فريد لمنع تكرار ربط نفس المورد بنفس العملة
    __table_args__ = (
        Index('idx_supplier_currency_unique', 'supplier_id', 'currency_id', unique=True),
    )


class PurchaseRepresentative(Base):
    """جدول مندوبي المشتريات"""
    __tablename__ = 'purchase_representatives'

    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, comment='كود المندوب')
    name = Column(String(200), nullable=False, comment='اسم المندوب')
    name_en = Column(String(200), comment='اسم المندوب بالإنجليزية')

    # بيانات الاتصال
    phone = Column(String(50), comment='الهاتف')
    mobile = Column(String(50), comment='الجوال')
    email = Column(String(100), comment='البريد الإلكتروني')

    # بيانات العمل
    department = Column(String(100), comment='القسم')
    position = Column(String(100), comment='المنصب')
    hire_date = Column(Date, comment='تاريخ التوظيف')
    salary = Column(Float, comment='الراتب')
    commission_rate = Column(Float, default=0.0, comment='نسبة العمولة')
    currency = Column(String(50), default='ريال سعودي', comment='العملة')

    # العنوان
    address = Column(Text, comment='العنوان')
    city = Column(String(100), comment='المدينة')

    # بيانات إضافية
    notes = Column(Text, comment='ملاحظات')
    emergency_contact = Column(String(200), comment='جهة الاتصال في الطوارئ')
    emergency_phone = Column(String(50), comment='هاتف الطوارئ')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    suppliers = relationship("SupplierRepresentative", back_populates="representative")


class SupplierRepresentative(Base):
    """جدول ربط الموردين بمندوبي المشتريات"""
    __tablename__ = 'supplier_representatives'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='معرف المورد')
    representative_id = Column(Integer, ForeignKey('purchase_representatives.id'), nullable=False, comment='معرف المندوب')
    is_primary = Column(Boolean, default=False, comment='المندوب الرئيسي للمورد')
    assigned_date = Column(Date, default=datetime.now, comment='تاريخ التعيين')
    notes = Column(Text, comment='ملاحظات')

    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier", back_populates="representatives")
    representative = relationship("PurchaseRepresentative", back_populates="suppliers")
    commission_rules = relationship("ShipmentCommission", back_populates="supplier_representative", cascade="all, delete-orphan")

    # فهرس فريد لمنع تكرار ربط نفس المورد بنفس المندوب
    __table_args__ = (
        Index('idx_supplier_representative_unique', 'supplier_id', 'representative_id', unique=True),
    )


class ShipmentCommission(Base):
    """نموذج عمولة الشحنات"""
    __tablename__ = 'shipment_commissions'

    id = Column(Integer, primary_key=True)
    supplier_representative_id = Column(Integer, ForeignKey('supplier_representatives.id'), nullable=False, comment='معرف ربط المورد بالمندوب')

    # نوع احتساب العمولة
    calculation_type = Column(String(50), default='quantity', comment='نوع الاحتساب: quantity (حسب الكمية) أو value (حسب قيمة البضاعة)')

    # شروط العمولة حسب الكمية
    min_quantity = Column(Float, default=0, comment='الحد الأدنى للكمية')
    max_quantity = Column(Float, comment='الحد الأقصى للكمية (اختياري)')
    quantity_commission_rate = Column(Float, default=0, comment='معدل العمولة للكمية (مبلغ ثابت لكل وحدة)')

    # شروط العمولة حسب قيمة البضاعة
    min_value = Column(Float, default=0, comment='الحد الأدنى لقيمة البضاعة')
    max_value = Column(Float, comment='الحد الأقصى لقيمة البضاعة (اختياري)')
    value_commission_percentage = Column(Float, default=0, comment='نسبة العمولة من قيمة البضاعة (%)')

    # معلومات إضافية
    description = Column(Text, comment='وصف شروط العمولة')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier_representative = relationship("SupplierRepresentative", back_populates="commission_rules")

    def __repr__(self):
        return f"<ShipmentCommission(type='{self.calculation_type}', supplier_rep_id={self.supplier_representative_id})>"


class SystemSettings(Base):
    """جدول إعدادات النظام"""
    __tablename__ = 'system_settings'

    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False, comment='مفتاح الإعداد')
    value = Column(Text, comment='قيمة الإعداد')
    description = Column(Text, comment='وصف الإعداد')
    category = Column(String(50), comment='فئة الإعداد')
    data_type = Column(String(20), default='string', comment='نوع البيانات')
    is_system = Column(Boolean, default=False, comment='إعداد نظام')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Shipment(Base):
    """جدول الشحنات"""
    __tablename__ = 'shipments'

    id = Column(Integer, primary_key=True)
    shipment_number = Column(String(50), unique=True, nullable=False, comment='رقم الشحنة')
    shipment_date = Column(DateTime, comment='تاريخ الشحنة')
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='المورد')
    supplier_invoice_number = Column(String(100), comment='رقم فاتورة المورد')

    # حالة الشحنة
    shipment_status = Column(String(50), default='تحت الطلب', comment='حالة الشحنة')
    # الخيارات: تحت الطلب - مؤكدة - تم الشحن - في الطريق - وصلت الميناء - في الجمارك - تم التسليم - ملغية - متاخرة

    # حالة الإفراج
    clearance_status = Column(String(50), default='بدون الافراج', comment='حالة الافراج')
    # الخيارات: بدون الافراج - مع الافراج

    # البيانات المالية
    total_amount = Column(Float, default=0.0, comment='المبلغ الإجمالي')
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment='العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    insurance_amount = Column(Float, default=0.0, comment='مبلغ التأمين')
    freight_amount = Column(Float, default=0.0, comment='مبلغ الشحن')
    customs_amount = Column(Float, default=0.0, comment='مبلغ الجمارك')
    other_charges = Column(Float, default=0.0, comment='رسوم أخرى')

    # بيانات الشحن
    shipping_method = Column(String(100), comment='طريقة الشحن')
    port_of_loading = Column(String(200), comment='ميناء التحميل')
    port_of_discharge = Column(String(200), comment='ميناء التفريغ')
    port_of_arrival = Column(String(200), comment='ميناء الوصول')
    final_destination = Column(String(200), comment='الوجهة النهائية')
    estimated_departure_date = Column(DateTime, comment='تاريخ المغادرة المتوقع')
    actual_departure_date = Column(DateTime, comment='تاريخ المغادرة الفعلي')
    estimated_arrival_date = Column(DateTime, comment='تاريخ الوصول المتوقع')
    actual_arrival_date = Column(DateTime, comment='تاريخ الوصول الفعلي')
    shipping_company = Column(String(200), comment='شركة الشحن')
    vessel_name = Column(String(200), comment='اسم السفينة')
    voyage_number = Column(String(100), comment='رقم الرحلة')
    dhl_number = Column(String(100), comment='رقم DHL')

    # بيانات إضافية
    notes = Column(Text, comment='ملاحظات')
    tracking_number = Column(String(100), comment='رقم التتبع')
    bill_of_lading = Column(String(100), comment='بوليصة الشحن')
    shipping_policy = Column(String(200), comment='بوليصة الشحن')
    container_number = Column(String(100), comment='رقم الحاوية')

    # روابط المستندات
    initial_documents_url = Column(String(1000), comment='رابط المستندات الأولية')
    dn_documents_url = Column(String(1000), comment='رابط مستندات DN')
    customs_documents_url = Column(String(1000), comment='رابط المستندات المرسلة للجمارك')
    bill_of_lading_url = Column(String(1000), comment='رابط بوليصة الشحن')
    items_images_url = Column(String(1000), comment='رابط صور الأصناف')
    other_documents_url = Column(String(1000), comment='رابط مستندات أخرى')

    # مسارات المرفقات
    initial_documents_files = Column(Text, comment='مسارات ملفات المستندات الأولية (JSON)')
    dn_documents_files = Column(Text, comment='مسارات ملفات مستندات DN (JSON)')
    customs_documents_files = Column(Text, comment='مسارات ملفات المستندات الجمركية (JSON)')
    bill_of_lading_files = Column(Text, comment='مسارات ملفات بوليصة الشحن (JSON)')
    items_images_files = Column(Text, comment='مسارات ملفات صور الأصناف (JSON)')
    other_documents_files = Column(Text, comment='مسارات ملفات مستندات أخرى (JSON)')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    created_by_user = relationship("User")

class PurchaseOrder(Base):
    """جدول طلبات الشراء من الموردين"""
    __tablename__ = 'purchase_orders'

    id = Column(Integer, primary_key=True)
    order_number = Column(String(50), unique=True, nullable=False, comment='رقم طلب الشراء')
    order_date = Column(DateTime, default=datetime.now, comment='تاريخ الطلب')
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment='المورد')

    # حالة الطلب
    order_status = Column(String(50), default='مسودة', comment='حالة الطلب')
    # الخيارات: مسودة - مرسل - مؤكد - جزئي - مكتمل - ملغي

    # البيانات المالية
    total_amount = Column(Float, default=0.0, comment='المبلغ الإجمالي')
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment='العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    discount_amount = Column(Float, default=0.0, comment='مبلغ الخصم')
    tax_amount = Column(Float, default=0.0, comment='مبلغ الضريبة')

    # تواريخ مهمة
    expected_delivery_date = Column(DateTime, comment='تاريخ التسليم المتوقع')
    actual_delivery_date = Column(DateTime, comment='تاريخ التسليم الفعلي')

    # ملاحظات
    notes = Column(Text, comment='ملاحظات')
    terms_conditions = Column(Text, comment='الشروط والأحكام')

    # المستندات والمرفقات
    contract_url = Column(Text, comment='رابط العقد مع المورد')
    initial_designs_url = Column(Text, comment='رابط التصاميم الأولية')
    final_design_url = Column(Text, comment='رابط التصميم النهائي المعتمد')
    other_attachments_url = Column(Text, comment='رابط المرفقات الأخرى')

    # حقول النظام
    is_active = Column(Boolean, default=True, comment='نشط')
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    supplier = relationship("Supplier")
    currency = relationship("Currency")
    created_by_user = relationship("User")
    items = relationship("PurchaseOrderItem", back_populates="purchase_order", cascade="all, delete-orphan")

class PurchaseOrderItem(Base):
    """جدول أصناف طلبات الشراء"""
    __tablename__ = 'purchase_order_items'

    id = Column(Integer, primary_key=True)
    purchase_order_id = Column(Integer, ForeignKey('purchase_orders.id'), nullable=False, comment='طلب الشراء')
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, comment='الصنف')
    quantity = Column(Float, nullable=False, comment='الكمية المطلوبة')
    unit_price = Column(Float, default=0.0, comment='سعر الوحدة')
    total_price = Column(Float, default=0.0, comment='السعر الإجمالي')
    discount_percentage = Column(Float, default=0.0, comment='نسبة الخصم')
    discount_amount = Column(Float, default=0.0, comment='مبلغ الخصم')

    # كميات التسليم
    delivered_quantity = Column(Float, default=0.0, comment='الكمية المسلمة')
    remaining_quantity = Column(Float, default=0.0, comment='الكمية المتبقية')

    # تواريخ
    expected_delivery_date = Column(DateTime, comment='تاريخ التسليم المتوقع للصنف')
    production_date = Column(Date, comment='تاريخ الإنتاج')
    expiry_date = Column(Date, comment='تاريخ الانتهاء')

    # ملاحظات
    notes = Column(Text, comment='ملاحظات الصنف')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    purchase_order = relationship("PurchaseOrder", back_populates="items")
    item = relationship("Item")

class ShipmentItem(Base):
    """جدول أصناف الشحنة"""
    __tablename__ = 'shipment_items'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False, comment='الشحنة')
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, comment='الصنف')
    purchase_order_item_id = Column(Integer, ForeignKey('purchase_order_items.id'), nullable=True, comment='صنف طلب الشراء المرتبط')
    quantity = Column(Float, nullable=False, comment='الكمية')
    unit_price = Column(Float, default=0.0, comment='سعر الوحدة')
    total_price = Column(Float, default=0.0, comment='السعر الإجمالي')
    production_date = Column(DateTime, comment='تاريخ الإنتاج')
    expiry_date = Column(DateTime, comment='تاريخ الانتهاء')
    weight = Column(Float, comment='الوزن')
    volume = Column(Float, comment='الحجم')
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    shipment = relationship("Shipment")
    item = relationship("Item")
    purchase_order_item = relationship("PurchaseOrderItem")

class Container(Base):
    """جدول الحاويات"""
    __tablename__ = 'containers'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False, comment='الشحنة')
    container_number = Column(String(50), nullable=False, comment='رقم الحاوية')
    container_type = Column(String(50), comment='نوع الحاوية')
    container_size = Column(String(20), comment='حجم الحاوية')
    seal_number = Column(String(50), comment='رقم الختم')
    weight_empty = Column(Float, comment='الوزن فارغة')
    weight_loaded = Column(Float, comment='الوزن محملة')
    max_weight = Column(Float, comment='الحد الأقصى للوزن')
    status = Column(String(50), default='فارغة', comment='حالة الحاوية')
    notes = Column(Text, comment='ملاحظات')

    # حقول النظام
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    shipment = relationship("Shipment")

class ShipmentDocument(Base):
    """جدول مستندات الشحنة"""
    __tablename__ = 'shipment_documents'

    id = Column(Integer, primary_key=True)
    shipment_id = Column(Integer, ForeignKey('shipments.id'), nullable=False, comment='الشحنة')
    document_name = Column(String(200), nullable=False, comment='اسم المستند')
    document_type = Column(String(100), comment='نوع المستند')
    document_url = Column(String(500), comment='رابط المستند')
    file_path = Column(String(500), comment='مسار الملف')
    description = Column(Text, comment='وصف المستند')
    upload_date = Column(DateTime, default=datetime.now, comment='تاريخ الرفع')

    # حقول النظام
    created_by = Column(Integer, ForeignKey('users.id'), comment='أنشئ بواسطة')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # العلاقات
    shipment = relationship("Shipment")
    created_by_user = relationship("User")

# إضافة العلاقات العكسية
Shipment.items = relationship("ShipmentItem", back_populates="shipment")
Shipment.containers = relationship("Container", back_populates="shipment")
Shipment.documents = relationship("ShipmentDocument", back_populates="shipment")

ShipmentItem.shipment = relationship("Shipment", back_populates="items")
Container.shipment = relationship("Shipment", back_populates="containers")
ShipmentDocument.shipment = relationship("Shipment", back_populates="documents")
