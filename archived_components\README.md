# المكونات المؤرشفة - Archived Components

## 📋 نظرة عامة

هذا المجلد يحتوي على المكونات التي تم إزالتها من النظام الرئيسي لأسباب تقنية أو تشغيلية.

## 🗂️ المكونات المؤرشفة

### 1. نظام تعبئة البيانات المفقودة
**تاريخ الأرشفة:** 2025-07-07  
**السبب:** تجنب التداخل مع نظام الشحنات وحل مشكلة التعليق في وضع التعديل

#### الملفات المؤرشفة:
- `shipment_data_filler_dialog.py` - واجهة المستخدم لنظام تعبئة البيانات المفقودة
- `shipment_data_filler.py` - المنطق الأساسي لتعبئة البيانات المفقودة

#### الوظائف التي كانت متاحة:
1. **تحليل البيانات المفقودة**: تحليل الحقول الفارغة في الشحنات
2. **البحث الذكي**: البحث عن شحنات مشابهة لتعبئة البيانات
3. **التعبئة التلقائية**: تعبئة الحقول المفقودة بناءً على البيانات المشابهة
4. **البحث عبر الإنترنت**: استخراج البيانات من مواقع شركات الشحن
5. **التعبئة المجمعة**: تعبئة عدة شحنات في عملية واحدة

#### المشكلة التي أدت للأرشفة:
- **تداخل جلسات قاعدة البيانات** مع نظام الشحنات
- **تعليق التطبيق** عند استخدام وضع التعديل في الشحنات
- **تنافس على الموارد** بين النظامين

#### البديل المتاح:
- **نظام التعبئة التلقائية المدمج** في نافذة إدارة الشحنات (قائمة الزر الأيمن)
- **وظائف البحث والتعبئة** متاحة مباشرة من واجهة الشحنات

## 🔄 إعادة التفعيل (إذا لزم الأمر)

إذا كنت تريد إعادة تفعيل هذه المكونات في المستقبل:

### 1. نقل الملفات:
```bash
# نقل الملفات إلى مواقعها الأصلية
move "archived_components\shipment_data_filler_dialog.py" "src\ui\dialogs\"
move "archived_components\shipment_data_filler.py" "src\utils\"
```

### 2. إعادة تفعيل القائمة في main_window.py:
```python
# في دالة setup_menu_bar()
data_filler_action = QAction("📝 تعبئة البيانات المفقودة", self)
data_filler_action.setToolTip("البحث وتعبئة الحقول الفارغة في الشحنات بناءً على البيانات المشابهة")
data_filler_action.triggered.connect(self.open_data_filler)
tools_menu.addAction(data_filler_action)

# إعادة تفعيل الدالة
def open_data_filler(self):
    """فتح نافذة تعبئة البيانات المفقودة"""
    try:
        from .dialogs.shipment_data_filler_dialog import ShipmentDataFillerDialog
        dialog = ShipmentDataFillerDialog(parent=self)
        dialog.exec()
    except Exception as e:
        QMessageBox.critical(
            self,
            "خطأ",
            f"خطأ في فتح نافذة تعبئة البيانات المفقودة:\n{str(e)}"
        )
```

### 3. حل مشكلة التداخل:
إذا تم إعادة التفعيل، يجب تطبيق الحلول التالية:
- استخدام جلسات قاعدة بيانات معزولة
- تجنب التشغيل المتزامن مع عمليات تعديل الشحنات
- إضافة آليات للتحقق من حالة النظام قبل التشغيل

## ⚠️ تحذيرات مهمة

1. **لا تحذف هذا المجلد** - قد تحتاج هذه المكونات في المستقبل
2. **اختبر بعناية** إذا قررت إعادة التفعيل
3. **راجع الحلول المطبقة** في الملفات المؤرشفة قبل الاستخدام

## 📞 الدعم

إذا كنت بحاجة لإعادة تفعيل هذه المكونات أو لديك أسئلة:
- راجع ملف `EDIT_MODE_FIX_SUMMARY.md` لفهم المشكلة والحل
- اتصل بفريق التطوير للحصول على المساعدة

---
**آخر تحديث:** 2025-07-07  
**الحالة:** مؤرشف ومعطل
