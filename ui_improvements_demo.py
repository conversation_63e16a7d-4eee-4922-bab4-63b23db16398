#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف توضيحي للتحسينات الجديدة على الأيقونات والتسميات
في منطقة الأنظمة الرئيسية للواجهة الرئيسية
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QGridLayout, QPushButton, QLabel, 
                               QFrame, QScrollArea)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QColor

class UIImprovementsDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("عرض التحسينات الجديدة - الأيقونات والتسميات")
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان الرئيسي
        title = QLabel("🎨 التحسينات الجديدة على الأيقونات والتسميات")
        title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        main_layout.addWidget(title)
        
        # قسم المقارنة
        comparison_frame = QFrame()
        comparison_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                padding: 20px;
            }
        """)
        comparison_layout = QHBoxLayout(comparison_frame)
        comparison_layout.setSpacing(30)
        
        # الأزرار القديمة
        old_section = self.create_old_buttons_section()
        comparison_layout.addWidget(old_section)
        
        # خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #bdc3c7; }")
        comparison_layout.addWidget(separator)
        
        # الأزرار الجديدة
        new_section = self.create_new_buttons_section()
        comparison_layout.addWidget(new_section)
        
        main_layout.addWidget(comparison_frame)
        
        # قسم التحسينات
        improvements_frame = self.create_improvements_section()
        main_layout.addWidget(improvements_frame)
    
    def create_old_buttons_section(self):
        """إنشاء قسم الأزرار القديمة"""
        section = QFrame()
        layout = QVBoxLayout(section)
        
        # عنوان القسم
        title = QLabel("❌ التصميم القديم")
        title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet("QLabel { color: #e74c3c; margin-bottom: 15px; }")
        layout.addWidget(title)
        
        # شبكة الأزرار القديمة
        grid = QGridLayout()
        grid.setSpacing(15)
        
        old_buttons = [
            ("⚙️", "الإعدادات العامة", "#9b59b6", 0, 0),
            ("📦", "إدارة الأصناف", "#3498db", 0, 1),
            ("🚢", "إدارة الشحنات", "#e74c3c", 1, 0),
            ("🏛️", "الإدخالات الجمركية", "#f39c12", 1, 1),
        ]
        
        for icon, text, color, row, col in old_buttons:
            button = self.create_old_button(icon, text, color)
            grid.addWidget(button, row, col)
        
        layout.addLayout(grid)
        return section
    
    def create_new_buttons_section(self):
        """إنشاء قسم الأزرار الجديدة"""
        section = QFrame()
        layout = QVBoxLayout(section)
        
        # عنوان القسم
        title = QLabel("✅ التصميم الجديد المحسن")
        title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet("QLabel { color: #27ae60; margin-bottom: 15px; }")
        layout.addWidget(title)
        
        # شبكة الأزرار الجديدة
        grid = QGridLayout()
        grid.setSpacing(20)
        
        new_buttons = [
            ("⚙️ إعدادات", "الإعدادات العامة للنظام", "#9b59b6", 0, 0),
            ("📦 أصناف", "إدارة الأصناف والمخزون", "#3498db", 0, 1),
            ("🚢 شحنات", "إدارة الشحنات والتتبع", "#e74c3c", 1, 0),
            ("🏛️ جمارك", "الإدخالات الجمركية", "#f39c12", 1, 1),
        ]
        
        for icon, text, color, row, col in new_buttons:
            button = self.create_new_button(icon, text, color)
            grid.addWidget(button, row, col)
        
        layout.addLayout(grid)
        return section
    
    def create_old_button(self, icon, text, color):
        """إنشاء زر بالتصميم القديم"""
        button = QPushButton()
        button.setMinimumSize(200, 80)
        
        # تخطيط داخلي بسيط
        button_layout = QVBoxLayout()
        
        # أيقونة صغيرة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(20)
        icon_label.setFont(icon_font)
        
        # نص مختصر
        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        text_font = QFont()
        text_font.setPointSize(9)
        text_label.setFont(text_font)
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 10px;
                color: white;
                padding: 10px;
            }}
        """)
        
        return button
    
    def create_new_button(self, icon, text, color):
        """إنشاء زر بالتصميم الجديد المحسن"""
        button = QPushButton()
        button.setMinimumSize(250, 110)
        button.setCursor(Qt.PointingHandCursor)
        
        hover_color = self.get_hover_color(color)
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {hover_color});
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 18px;
                padding: 20px;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {hover_color}, stop:1 {color});
                border: 2px solid rgba(255, 255, 255, 0.4);
            }}
        """)
        
        button.setText(f"{icon}\n{text}")
        
        return button
    
    def get_hover_color(self, color):
        """الحصول على لون التمرير"""
        color_map = {
            "#9b59b6": "#8e44ad",
            "#3498db": "#2980b9",
            "#2ecc71": "#27ae60",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)
    
    def create_improvements_section(self):
        """إنشاء قسم التحسينات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان القسم
        title = QLabel("🔧 التحسينات المطبقة")
        title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet("QLabel { color: #2c3e50; margin-bottom: 15px; }")
        layout.addWidget(title)
        
        # قائمة التحسينات
        improvements = [
            "✨ إضافة نص مختصر مع الأيقونة لوضوح أكبر",
            "📏 زيادة حجم الأزرار من 280x120 إلى 320x140 بكسل",
            "🎨 تحسين التدرج اللوني والحدود للأزرار",
            "📝 تحسين النصوص التوضيحية لتكون أكثر وصفية",
            "🖱️ إضافة تأثيرات التمرير المحسنة",
            "📐 تحسين المسافات والتخطيط العام",
            "🔤 تحسين أحجام الخطوط ووضوح النصوص"
        ]
        
        for improvement in improvements:
            label = QLabel(improvement)
            label.setStyleSheet("""
                QLabel {
                    color: #34495e;
                    font-size: 14px;
                    padding: 8px;
                    margin: 3px;
                    background: rgba(52, 152, 219, 0.1);
                    border-radius: 8px;
                }
            """)
            layout.addWidget(label)
        
        return frame

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = UIImprovementsDemo()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
