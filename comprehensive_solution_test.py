#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للحلول
Comprehensive Solution Test - Testing all implemented solutions to ensure no hanging issues
"""

import sys
import os
import time
import threading
from datetime import datetime, date
from typing import Dict, List, Any, Tuple

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import (
        Shipment, ShipmentItem, Container, ShipmentDocument,
        Supplier, Item, Base
    )
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد النماذج: {e}")

class ComprehensiveSolutionTester:
    """مختبر الحلول الشامل"""
    
    def __init__(self, db_manager=None):
        """تهيئة مختبر الحلول"""
        self.db_manager = db_manager or DatabaseManager()
        self.test_log = []
        self.test_results = {}
        self.hanging_detected = False
        
    def log_test(self, test_name: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الاختبار"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {test_name}: {message}"
        self.test_log.append(log_entry)
        print(log_entry)
    
    def timeout_handler(self, test_name: str, timeout_seconds: int):
        """معالج انتهاء المهلة الزمنية"""
        time.sleep(timeout_seconds)
        if not self.test_results.get(test_name, {}).get('completed', False):
            self.hanging_detected = True
            self.log_test(test_name, f"تم اكتشاف تعليق - انتهت المهلة الزمنية ({timeout_seconds}s)", "ERROR")
    
    def run_test_with_timeout(self, test_func, test_name: str, timeout_seconds: int = 30) -> Tuple[bool, Any]:
        """تشغيل اختبار مع مهلة زمنية"""
        self.test_results[test_name] = {'completed': False, 'result': None}
        
        # بدء مؤقت انتهاء المهلة
        timeout_thread = threading.Thread(target=self.timeout_handler, args=(test_name, timeout_seconds))
        timeout_thread.daemon = True
        timeout_thread.start()
        
        try:
            start_time = time.time()
            result = test_func()
            execution_time = time.time() - start_time
            
            self.test_results[test_name] = {
                'completed': True,
                'result': result,
                'execution_time': execution_time,
                'success': True
            }
            
            self.log_test(test_name, f"اكتمل في {execution_time:.3f} ثانية", "SUCCESS")
            return True, result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.test_results[test_name] = {
                'completed': True,
                'result': None,
                'execution_time': execution_time,
                'success': False,
                'error': str(e)
            }
            
            self.log_test(test_name, f"فشل بعد {execution_time:.3f} ثانية: {str(e)}", "ERROR")
            return False, None
    
    def test_basic_database_operations(self) -> bool:
        """اختبار العمليات الأساسية لقاعدة البيانات"""
        self.log_test("عمليات قاعدة البيانات", "بدء اختبار العمليات الأساسية")
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار الاتصال
            session.execute("SELECT 1")
            self.log_test("عمليات قاعدة البيانات", "اختبار الاتصال نجح")
            
            # اختبار إنشاء جلسة متعددة
            sessions = []
            for i in range(5):
                s = self.db_manager.get_session()
                sessions.append(s)
            
            # إغلاق الجلسات
            for s in sessions:
                s.close()
            
            session.close()
            self.log_test("عمليات قاعدة البيانات", "اختبار الجلسات المتعددة نجح")
            return True
            
        except Exception as e:
            self.log_test("عمليات قاعدة البيانات", f"فشل: {str(e)}", "ERROR")
            return False
    
    def test_shipment_creation_no_hanging(self) -> bool:
        """اختبار إنشاء الشحنات بدون تعليق"""
        self.log_test("إنشاء الشحنات", "بدء اختبار إنشاء الشحنات")
        
        try:
            session = self.db_manager.get_session()
            
            # الحصول على مورد
            supplier = session.query(Supplier).first()
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار عدم التعليق",
                    contact_person="شخص الاتصال",
                    phone="*********"
                )
                session.add(supplier)
                session.commit()
            
            # إنشاء شحنة تجريبية
            test_shipment = Shipment(
                shipment_number="NO-HANG-TEST-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة اختبار عدم التعليق",
                notes="اختبار عدم التعليق في الحفظ"
            )
            
            session.add(test_shipment)
            session.commit()
            
            # التحقق من الحفظ
            saved_shipment = session.query(Shipment).filter(
                Shipment.shipment_number == "NO-HANG-TEST-001"
            ).first()
            
            if saved_shipment:
                self.log_test("إنشاء الشحنات", f"تم إنشاء الشحنة رقم {saved_shipment.id} بنجاح")
                
                # تنظيف البيانات التجريبية
                session.delete(saved_shipment)
                session.commit()
                
                session.close()
                return True
            else:
                session.close()
                return False
                
        except Exception as e:
            self.log_test("إنشاء الشحنات", f"فشل: {str(e)}", "ERROR")
            return False
    
    def test_complex_save_operations(self) -> bool:
        """اختبار عمليات الحفظ المعقدة"""
        self.log_test("الحفظ المعقد", "بدء اختبار عمليات الحفظ المعقدة")
        
        try:
            session = self.db_manager.get_session()
            
            # الحصول على البيانات المطلوبة
            supplier = session.query(Supplier).first()
            item = session.query(Item).first()
            
            if not supplier or not item:
                session.close()
                self.log_test("الحفظ المعقد", "لا توجد بيانات أساسية للاختبار", "WARNING")
                return False
            
            # إنشاء شحنة معقدة
            complex_shipment = Shipment(
                shipment_number="COMPLEX-NO-HANG-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة الاختبار المعقد",
                notes="اختبار الحفظ المعقد بدون تعليق"
            )
            
            session.add(complex_shipment)
            session.flush()  # للحصول على ID
            
            # إضافة أصناف متعددة
            items_to_add = []
            for i in range(20):  # عدد أكبر من الأصناف
                shipment_item = ShipmentItem(
                    shipment_id=complex_shipment.id,
                    item_id=item.id,
                    quantity=float(i + 1),
                    unit_price=100.0 + i,
                    notes=f"صنف معقد رقم {i + 1}"
                )
                items_to_add.append(shipment_item)
            
            session.add_all(items_to_add)
            
            # إضافة حاويات متعددة
            containers_to_add = []
            for i in range(5):  # عدد أكبر من الحاويات
                container = Container(
                    shipment_id=complex_shipment.id,
                    container_number=f"COMPLEX-CONT-{i+1:03d}",
                    container_type="عادية",
                    container_size="40 قدم",
                    status="فارغة",
                    weight_empty=3800.0,
                    weight_loaded=25000.0,
                    max_weight=30480.0
                )
                containers_to_add.append(container)
            
            session.add_all(containers_to_add)
            session.commit()
            
            # التحقق من الحفظ
            saved_items_count = session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == complex_shipment.id
            ).count()
            
            saved_containers_count = session.query(Container).filter(
                Container.shipment_id == complex_shipment.id
            ).count()
            
            if saved_items_count == 20 and saved_containers_count == 5:
                self.log_test("الحفظ المعقد", 
                             f"نجح الحفظ المعقد - {saved_items_count} صنف، {saved_containers_count} حاوية")
                
                # تنظيف البيانات التجريبية
                session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == complex_shipment.id
                ).delete()
                session.query(Container).filter(
                    Container.shipment_id == complex_shipment.id
                ).delete()
                session.delete(complex_shipment)
                session.commit()
                
                session.close()
                return True
            else:
                session.close()
                return False
                
        except Exception as e:
            self.log_test("الحفظ المعقد", f"فشل: {str(e)}", "ERROR")
            return False
    
    def test_concurrent_operations(self) -> bool:
        """اختبار العمليات المتزامنة"""
        self.log_test("العمليات المتزامنة", "بدء اختبار العمليات المتزامنة")
        
        try:
            # إنشاء جلسات متعددة
            session1 = self.db_manager.get_session()
            session2 = self.db_manager.get_session()
            
            supplier = session1.query(Supplier).first()
            if not supplier:
                session1.close()
                session2.close()
                return False
            
            # إنشاء شحنات متزامنة
            shipment1 = Shipment(
                shipment_number="CONCURRENT-TEST-001",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب"
            )
            
            shipment2 = Shipment(
                shipment_number="CONCURRENT-TEST-002",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب"
            )
            
            # حفظ متزامن
            session1.add(shipment1)
            session2.add(shipment2)
            
            session1.commit()
            session2.commit()
            
            # التحقق من الحفظ
            count = session1.query(Shipment).filter(
                Shipment.shipment_number.in_(["CONCURRENT-TEST-001", "CONCURRENT-TEST-002"])
            ).count()
            
            if count == 2:
                self.log_test("العمليات المتزامنة", "نجحت العمليات المتزامنة")
                
                # تنظيف البيانات
                session1.query(Shipment).filter(
                    Shipment.shipment_number.in_(["CONCURRENT-TEST-001", "CONCURRENT-TEST-002"])
                ).delete()
                session1.commit()
                
                session1.close()
                session2.close()
                return True
            else:
                session1.close()
                session2.close()
                return False
                
        except Exception as e:
            self.log_test("العمليات المتزامنة", f"فشل: {str(e)}", "ERROR")
            return False
    
    def test_memory_management(self) -> bool:
        """اختبار إدارة الذاكرة"""
        self.log_test("إدارة الذاكرة", "بدء اختبار إدارة الذاكرة")
        
        try:
            # إنشاء وإغلاق جلسات متعددة
            for i in range(100):
                session = self.db_manager.get_session()
                session.execute("SELECT 1")
                session.close()
            
            self.log_test("إدارة الذاكرة", "نجح اختبار إدارة الذاكرة - 100 جلسة")
            return True
            
        except Exception as e:
            self.log_test("إدارة الذاكرة", f"فشل: {str(e)}", "ERROR")
            return False
    
    def run_comprehensive_test(self) -> Dict:
        """تشغيل الاختبار الشامل"""
        self.log_test("اختبار شامل", "بدء الاختبار الشامل للحلول")
        
        tests = [
            ("عمليات قاعدة البيانات", self.test_basic_database_operations, 10),
            ("إنشاء الشحنات", self.test_shipment_creation_no_hanging, 15),
            ("الحفظ المعقد", self.test_complex_save_operations, 30),
            ("العمليات المتزامنة", self.test_concurrent_operations, 20),
            ("إدارة الذاكرة", self.test_memory_management, 15)
        ]
        
        results = {}
        total_tests = len(tests)
        passed_tests = 0
        
        for test_name, test_func, timeout in tests:
            self.log_test("اختبار شامل", f"تشغيل اختبار: {test_name}")
            success, result = self.run_test_with_timeout(test_func, test_name, timeout)
            results[test_name] = success
            if success:
                passed_tests += 1
        
        # تقرير النتائج
        success_rate = (passed_tests / total_tests) * 100
        
        self.log_test("اختبار شامل", f"النتائج: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if self.hanging_detected:
            self.log_test("اختبار شامل", "تم اكتشاف مشاكل تعليق!", "ERROR")
        else:
            self.log_test("اختبار شامل", "لم يتم اكتشاف مشاكل تعليق", "SUCCESS")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'hanging_detected': self.hanging_detected,
            'test_results': self.test_results,
            'detailed_results': results,
            'log': self.test_log
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("🧪 اختبار شامل للحلول")
        print("=" * 50)
        
        # إنشاء مختبر الحلول
        tester = ComprehensiveSolutionTester()
        
        # تشغيل الاختبار الشامل
        results = tester.run_comprehensive_test()
        
        print("\n" + "=" * 50)
        print("📊 تقرير الاختبار الشامل:")
        print(f"• إجمالي الاختبارات: {results['total_tests']}")
        print(f"• الاختبارات الناجحة: {results['passed_tests']}")
        print(f"• معدل النجاح: {results['success_rate']:.1f}%")
        print(f"• مشاكل التعليق: {'نعم' if results['hanging_detected'] else 'لا'}")
        
        print("\n" + "=" * 50)
        print("📋 تفاصيل النتائج:")
        for test_name, success in results['detailed_results'].items():
            status = "✅ نجح" if success else "❌ فشل"
            execution_time = results['test_results'].get(test_name, {}).get('execution_time', 0)
            print(f"• {test_name}: {status} ({execution_time:.3f}s)")
        
        print("\n" + "=" * 50)
        if results['success_rate'] >= 80 and not results['hanging_detected']:
            print("✅ الاختبار الشامل نجح - لا توجد مشاكل تعليق!")
        else:
            print("⚠️ هناك مشاكل تحتاج إلى إصلاح")
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار الشامل: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
