#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام البحث عبر الإنترنت
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.web_scraping_service import WebScrapingService, ShipmentData

async def test_web_scraping():
    """اختبار البحث عبر الإنترنت"""
    print("🌐 بدء اختبار نظام البحث عبر الإنترنت...")
    
    # إنشاء خدمة البحث
    scraper = WebScrapingService()
    
    # بيانات اختبار
    test_cases = [
        {
            'container_number': 'MSKU1234567',
            'bill_of_lading': None,
            'carrier_name': '<PERSON><PERSON>'
        },
        {
            'container_number': None,
            'bill_of_lading': 'MSK123456789',
            'carrier_name': 'MSC'
        },
        {
            'container_number': 'COSU9876543',
            'bill_of_lading': None,
            'carrier_name': 'COSCO'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📦 اختبار {i}:")
        print(f"   رقم الحاوية: {test_case['container_number']}")
        print(f"   رقم بوليصة الشحن: {test_case['bill_of_lading']}")
        print(f"   شركة الملاحة: {test_case['carrier_name']}")
        
        try:
            # تشغيل البحث
            results = await scraper.search_all_carriers(
                container_number=test_case['container_number'],
                bill_of_lading=test_case['bill_of_lading'],
                carrier_name=test_case['carrier_name']
            )
            
            print(f"   ✅ تم العثور على {len(results)} نتيجة")
            
            # عرض النتائج
            for j, result in enumerate(results, 1):
                print(f"      النتيجة {j}:")
                print(f"         الشركة: {result.carrier}")
                print(f"         الحالة: {result.status}")
                print(f"         السفينة: {result.vessel_name}")
                print(f"         الرحلة: {result.voyage_number}")
                print(f"         المصدر: {result.source_url}")
            
            # استخراج أفضل البيانات
            if results:
                best_data = scraper.extract_best_data(results)
                print(f"   📊 أفضل البيانات المستخرجة:")
                for key, value in best_data.items():
                    if value:
                        print(f"      {key}: {value}")
            
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
    
    print("\n✅ اكتمل اختبار النظام!")

def test_simple_scraping():
    """اختبار بسيط للتأكد من المكتبات"""
    print("🔧 اختبار المكتبات...")
    
    try:
        import requests
        import aiohttp
        from bs4 import BeautifulSoup
        from selenium import webdriver
        from fake_useragent import UserAgent
        
        print("✅ جميع المكتبات متوفرة")
        
        # اختبار User Agent
        ua = UserAgent()
        print(f"✅ User Agent: {ua.random[:50]}...")
        
        # اختبار BeautifulSoup
        html = "<html><body><h1>Test</h1></body></html>"
        soup = BeautifulSoup(html, 'html.parser')
        print(f"✅ BeautifulSoup: {soup.h1.text}")
        
        # اختبار Requests
        try:
            response = requests.get('https://httpbin.org/get', timeout=5)
            print(f"✅ Requests: {response.status_code}")
        except:
            print("⚠️ Requests: لا يمكن الاتصال بالإنترنت")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام البحث عبر الإنترنت في مواقع شركات الملاحة")
    print("=" * 60)
    
    # اختبار المكتبات أولاً
    if not test_simple_scraping():
        print("❌ فشل في اختبار المكتبات")
        return
    
    print("\n" + "=" * 60)
    
    # اختبار النظام الكامل
    try:
        asyncio.run(test_web_scraping())
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
