"""
اختبار نظام تعبئة البيانات المفقودة في الشحنات
"""

import sys
import os
sys.path.append('src')

from utils.shipment_data_filler import ShipmentDataFiller
from database.database_manager import DatabaseManager
from database.models import Shipment

def test_data_analysis():
    """اختبار تحليل البيانات"""
    print("🔍 اختبار تحليل البيانات المفقودة")
    print("=" * 50)
    
    filler = ShipmentDataFiller()
    
    # تحليل البيانات
    analysis = filler.analyze_missing_data()
    
    print(f"📊 إجمالي الشحنات: {analysis['total_shipments']}")
    print("\n📋 الحقول المفقودة:")
    
    for field, data in analysis['missing_fields'].items():
        if data['count'] > 0:
            print(f"   • {field}: {data['count']} شحنة ({data['percentage']:.1f}%)")
    
    print(f"\n🔍 الأنماط المكتشفة:")
    patterns = analysis['patterns']
    
    if patterns['shipping_company_containers']:
        print(f"   • شركات الشحن مع الحاويات: {len(patterns['shipping_company_containers'])}")
        for company, containers in list(patterns['shipping_company_containers'].items())[:3]:
            print(f"     - {company}: {len(containers)} حاوية")
    
    if patterns['supplier_shipping']:
        print(f"   • الموردين مع شركات الشحن: {len(patterns['supplier_shipping'])}")
    
    return analysis

def test_search_functionality():
    """اختبار وظيفة البحث"""
    print("\n\n🔍 اختبار وظيفة البحث")
    print("=" * 50)
    
    filler = ShipmentDataFiller()
    
    # اختبار البحث بشركة الشحن
    print("🚢 البحث بشركة الشحن 'MSC':")
    similar_shipments = filler.find_similar_shipments({
        'shipping_company': 'MSC'
    })
    
    print(f"   وجد {len(similar_shipments)} شحنة مشابهة")
    
    for shipment, similarity in similar_shipments[:3]:  # أول 3 نتائج
        print(f"   • الشحنة {shipment.shipment_number}: تشابه {similarity:.2f}")
        print(f"     - شركة الشحن: {shipment.shipping_company}")
        print(f"     - رقم الحاوية: {shipment.container_number}")
        print(f"     - بوليصة الشحن: {shipment.bill_of_lading}")
    
    # اختبار البحث برقم الحاوية
    print("\n📦 البحث برقم الحاوية:")
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن شحنة تحتوي على رقم حاوية
        sample_shipment = session.query(Shipment).filter(
            Shipment.container_number.isnot(None),
            Shipment.container_number != ""
        ).first()
        
        if sample_shipment and sample_shipment.container_number:
            container_num = sample_shipment.container_number
            print(f"   البحث برقم الحاوية: {container_num}")
            
            similar_by_container = filler.find_similar_shipments({
                'container_number': container_num
            })
            
            print(f"   وجد {len(similar_by_container)} شحنة مشابهة")
            
            for shipment, similarity in similar_by_container[:2]:
                print(f"   • الشحنة {shipment.shipment_number}: تشابه {similarity:.2f}")
        else:
            print("   لا توجد شحنات تحتوي على أرقام حاويات")
    
    finally:
        session.close()

def test_suggestions():
    """اختبار اقتراح القيم"""
    print("\n\n💡 اختبار اقتراح القيم")
    print("=" * 50)
    
    filler = ShipmentDataFiller()
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن شحنة تحتاج تعبئة
        shipments = session.query(Shipment).limit(5).all()
        
        for shipment in shipments:
            print(f"\n🚢 الشحنة {shipment.shipment_number}:")
            
            # اختبار اقتراح شركة الشحن
            if not shipment.shipping_company or shipment.shipping_company.strip() == "":
                suggestions = filler.suggest_field_values(shipment.id, 'shipping_company')
                if suggestions:
                    print(f"   💡 اقتراحات شركة الشحن:")
                    for suggestion in suggestions[:3]:
                        confidence_percent = int(suggestion['confidence'] * 100)
                        print(f"     - {suggestion['value']} (ثقة: {confidence_percent}%)")
                else:
                    print(f"   ❌ لا توجد اقتراحات لشركة الشحن")
            else:
                print(f"   ✅ شركة الشحن موجودة: {shipment.shipping_company}")
            
            # اختبار اقتراح رقم الحاوية
            if not shipment.container_number or shipment.container_number.strip() == "":
                suggestions = filler.suggest_field_values(shipment.id, 'container_number')
                if suggestions:
                    print(f"   💡 اقتراحات رقم الحاوية:")
                    for suggestion in suggestions[:2]:
                        confidence_percent = int(suggestion['confidence'] * 100)
                        print(f"     - {suggestion['value']} (ثقة: {confidence_percent}%)")
                else:
                    print(f"   ❌ لا توجد اقتراحات لرقم الحاوية")
            else:
                print(f"   ✅ رقم الحاوية موجود: {shipment.container_number}")
    
    finally:
        session.close()

def test_auto_fill():
    """اختبار التعبئة التلقائية"""
    print("\n\n🤖 اختبار التعبئة التلقائية")
    print("=" * 50)
    
    filler = ShipmentDataFiller()
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن شحنة للاختبار
        test_shipment = session.query(Shipment).first()
        
        if test_shipment:
            print(f"🚢 اختبار التعبئة للشحنة {test_shipment.shipment_number}")
            
            # حفظ القيم الأصلية
            original_shipping_company = test_shipment.shipping_company
            original_container_number = test_shipment.container_number
            
            # مسح بعض القيم للاختبار (بدون حفظ)
            test_shipment.shipping_company = None
            test_shipment.container_number = None
            
            print("   📝 تم مسح بعض القيم للاختبار")
            
            # تجربة التعبئة التلقائية
            result = filler.auto_fill_missing_fields(test_shipment.id, confidence_threshold=0.5)
            
            print(f"   📊 نتائج التعبئة:")
            print(f"     - حقول تم تعبئتها: {len(result['filled_fields'])}")
            print(f"     - حقول تم تخطيها: {len(result['skipped_fields'])}")
            
            if result['filled_fields']:
                print(f"   ✅ الحقول المعبأة:")
                for field, data in result['filled_fields'].items():
                    confidence_percent = int(data['confidence'] * 100)
                    print(f"     - {field}: {data['value']} (ثقة: {confidence_percent}%)")
            
            if result['skipped_fields']:
                print(f"   ⏭️ الحقول المتخطاة:")
                for field, data in result['skipped_fields'].items():
                    print(f"     - {field}: {data['reason']}")
            
            if result['errors']:
                print(f"   ❌ أخطاء: {result['errors']}")
            
            # استعادة القيم الأصلية (بدون حفظ)
            test_shipment.shipping_company = original_shipping_company
            test_shipment.container_number = original_container_number
            
        else:
            print("   ❌ لا توجد شحنات للاختبار")
    
    finally:
        session.close()

def test_fill_log():
    """اختبار سجل التعبئة"""
    print("\n\n📝 اختبار سجل التعبئة")
    print("=" * 50)
    
    filler = ShipmentDataFiller()
    
    # الحصول على سجل التعبئة
    log_entries = filler.get_fill_log()
    
    if log_entries:
        print(f"📋 سجل التعبئة ({len(log_entries)} إدخال):")
        for entry in log_entries[-5:]:  # آخر 5 إدخالات
            confidence_percent = int(entry['confidence'] * 100)
            print(f"   • الشحنة {entry['shipment_id']}: {entry['field']} = '{entry['value']}' ({confidence_percent}%)")
    else:
        print("   📋 سجل التعبئة فارغ")
    
    # إنشاء تقرير
    report = filler.generate_fill_report()
    print(f"\n📄 تقرير التعبئة:")
    print(report)

def main():
    """دالة رئيسية للاختبار"""
    print("🧪 اختبار نظام تعبئة البيانات المفقودة")
    print("=" * 60)
    
    try:
        # تحليل البيانات
        analysis = test_data_analysis()
        
        # اختبار البحث
        test_search_functionality()
        
        # اختبار الاقتراحات
        test_suggestions()
        
        # اختبار التعبئة التلقائية
        test_auto_fill()
        
        # اختبار سجل التعبئة
        test_fill_log()
        
        print("\n\n🎉 اكتمل الاختبار بنجاح!")
        print("💡 يمكنك الآن استخدام النظام من خلال:")
        print("   1. القائمة الرئيسية → أدوات → تعبئة البيانات المفقودة")
        print("   2. البحث عن الشحنات المشابهة")
        print("   3. تعبئة الحقول الفارغة تلقائياً")
        print("   4. مراجعة سجل التعبئة والتقارير")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
