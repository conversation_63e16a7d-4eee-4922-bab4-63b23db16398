# -*- coding: utf-8 -*-
"""
نماذج بيانات حوالات الموردين المتقدمة
Advanced Supplier Remittance Data Models
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum

Base = declarative_base()


class RemittanceStatus(enum.Enum):
    """حالات الحوالة"""
    DRAFT = "draft"                    # مسودة
    PENDING_APPROVAL = "pending"       # في انتظار الموافقة
    APPROVED = "approved"              # موافق عليها
    SENT_TO_BANK = "sent_to_bank"     # مرسلة للبنك
    BANK_PROCESSING = "bank_processing" # قيد المعالجة في البنك
    BANK_CONFIRMED = "bank_confirmed"   # مؤكدة من البنك
    RECEIVED_BY_SUPPLIERS = "received" # وصلت للموردين
    POSTED_TO_ACCOUNTS = "posted"      # مرحلة للحسابات
    CANCELLED = "cancelled"            # ملغية
    FAILED = "failed"                  # فشلت


class SupplierRemittance(Base):
    """نموذج الحوالة الرئيسية"""
    __tablename__ = 'supplier_remittances'
    
    id = Column(Integer, primary_key=True)
    remittance_number = Column(String(50), unique=True, nullable=False)
    
    # معلومات التاريخ والوقت
    created_date = Column(DateTime, default=datetime.now)
    remittance_date = Column(DateTime, nullable=False)
    approved_date = Column(DateTime)
    sent_to_bank_date = Column(DateTime)
    bank_confirmed_date = Column(DateTime)
    posted_to_accounts_date = Column(DateTime)
    
    # الحالة والموافقات
    status = Column(Enum(RemittanceStatus), default=RemittanceStatus.DRAFT)
    is_approved = Column(Boolean, default=False)
    approved_by = Column(String(100))
    
    # معلومات مالية
    total_amount = Column(Float, default=0.0)
    currency = Column(String(10), default='SAR')
    exchange_rate = Column(Float, default=1.0)
    total_amount_sar = Column(Float, default=0.0)  # المبلغ بالريال السعودي
    
    # رسوم ومصاريف
    transfer_fees = Column(Float, default=0.0)
    bank_charges = Column(Float, default=0.0)
    other_charges = Column(Float, default=0.0)
    total_charges = Column(Float, default=0.0)
    
    # معلومات البنك المرسل
    sender_bank_name = Column(String(200))
    sender_bank_code = Column(String(50))
    sender_account_number = Column(String(100))
    sender_account_name = Column(String(200))
    
    # معلومات البنك المستقبل
    receiver_bank_name = Column(String(200))
    receiver_bank_code = Column(String(50))
    receiver_bank_country = Column(String(100))
    swift_code = Column(String(20))
    correspondent_bank = Column(String(200))
    
    # معلومات إضافية
    purpose_of_transfer = Column(Text)
    notes = Column(Text)
    internal_notes = Column(Text)  # ملاحظات داخلية
    
    # معلومات التأكيد من البنك
    bank_reference_number = Column(String(100))
    bank_confirmation_document = Column(String(500))  # مسار الملف
    bank_confirmation_notes = Column(Text)
    
    # معلومات المستخدم
    created_by = Column(String(100))
    last_modified_by = Column(String(100))
    last_modified_date = Column(DateTime, default=datetime.now)
    
    # العلاقات
    suppliers = relationship("SupplierRemittanceItem", back_populates="remittance", cascade="all, delete-orphan")
    status_history = relationship("RemittanceStatusHistory", back_populates="remittance", cascade="all, delete-orphan")
    documents = relationship("RemittanceDocument", back_populates="remittance", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<SupplierRemittance(number='{self.remittance_number}', status='{self.status}', amount={self.total_amount})>"
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.total_amount = sum(item.amount for item in self.suppliers)
        self.total_amount_sar = self.total_amount * self.exchange_rate
        self.total_charges = self.transfer_fees + self.bank_charges + self.other_charges
    
    def can_be_approved(self):
        """التحقق من إمكانية الموافقة"""
        return (self.status == RemittanceStatus.DRAFT and 
                len(self.suppliers) > 0 and 
                self.total_amount > 0)
    
    def can_be_sent_to_bank(self):
        """التحقق من إمكانية الإرسال للبنك"""
        return (self.status == RemittanceStatus.APPROVED and 
                self.is_approved and 
                self.sender_bank_name and 
                self.receiver_bank_name)
    
    def can_be_posted_to_accounts(self):
        """التحقق من إمكانية الترحيل للحسابات"""
        return self.status == RemittanceStatus.BANK_CONFIRMED


class SupplierRemittanceItem(Base):
    """عناصر الحوالة - الموردين المشمولين"""
    __tablename__ = 'supplier_remittance_items'
    
    id = Column(Integer, primary_key=True)
    remittance_id = Column(Integer, ForeignKey('supplier_remittances.id'), nullable=False)
    
    # معلومات المورد
    supplier_id = Column(Integer, nullable=False)  # سيتم ربطه بجدول الموردين
    supplier_name = Column(String(200), nullable=False)
    supplier_code = Column(String(50))
    
    # معلومات مالية
    amount = Column(Float, nullable=False)
    currency = Column(String(10), default='SAR')
    amount_sar = Column(Float)  # المبلغ بالريال السعودي
    
    # معلومات الحساب البنكي للمورد
    supplier_bank_name = Column(String(200))
    supplier_account_number = Column(String(100))
    supplier_account_name = Column(String(200))
    supplier_iban = Column(String(50))
    
    # معلومات إضافية
    invoice_numbers = Column(Text)  # أرقام الفواتير
    description = Column(Text)
    notes = Column(Text)
    
    # حالة العنصر
    is_confirmed_received = Column(Boolean, default=False)
    received_date = Column(DateTime)
    is_posted_to_account = Column(Boolean, default=False)
    posted_date = Column(DateTime)
    
    # العلاقات
    remittance = relationship("SupplierRemittance", back_populates="suppliers")
    
    def __repr__(self):
        return f"<SupplierRemittanceItem(supplier='{self.supplier_name}', amount={self.amount})>"


class RemittanceStatusHistory(Base):
    """تاريخ تغيير حالات الحوالة"""
    __tablename__ = 'remittance_status_history'
    
    id = Column(Integer, primary_key=True)
    remittance_id = Column(Integer, ForeignKey('supplier_remittances.id'), nullable=False)
    
    # معلومات التغيير
    old_status = Column(Enum(RemittanceStatus))
    new_status = Column(Enum(RemittanceStatus), nullable=False)
    change_date = Column(DateTime, default=datetime.now)
    changed_by = Column(String(100))
    
    # سبب التغيير
    reason = Column(Text)
    notes = Column(Text)
    
    # العلاقات
    remittance = relationship("SupplierRemittance", back_populates="status_history")
    
    def __repr__(self):
        return f"<RemittanceStatusHistory(remittance_id={self.remittance_id}, status='{self.new_status}')>"


class RemittanceDocument(Base):
    """مستندات الحوالة"""
    __tablename__ = 'remittance_documents'
    
    id = Column(Integer, primary_key=True)
    remittance_id = Column(Integer, ForeignKey('supplier_remittances.id'), nullable=False)
    
    # معلومات المستند
    document_type = Column(String(50))  # bank_confirmation, approval_document, etc.
    document_name = Column(String(200))
    file_path = Column(String(500))
    file_size = Column(Integer)
    mime_type = Column(String(100))
    
    # معلومات إضافية
    uploaded_date = Column(DateTime, default=datetime.now)
    uploaded_by = Column(String(100))
    description = Column(Text)
    
    # العلاقات
    remittance = relationship("SupplierRemittance", back_populates="documents")
    
    def __repr__(self):
        return f"<RemittanceDocument(name='{self.document_name}', type='{self.document_type}')>"
