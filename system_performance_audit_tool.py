#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص ومراجعة أداء النظام
System Performance Audit Tool - Comprehensive performance analysis
"""

import sys
import os
import time
import psutil
import threading
from datetime import datetime
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.models.shipment import Shipment
    from src.models.supplier import Supplier
    from src.models.item import Item
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import text
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد بعض المكونات: {e}")

class SystemPerformanceAuditTool:
    """أداة فحص أداء النظام"""
    
    def __init__(self):
        """تهيئة الأداة"""
        self.audit_log = []
        self.issues_found = []
        self.performance_metrics = {}
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الفحص"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status in ["ERROR", "WARNING"]:
            self.issues_found.append({
                'category': category,
                'message': message,
                'status': status,
                'timestamp': timestamp
            })
    
    def check_system_resources(self):
        """فحص موارد النظام"""
        self.log_audit("موارد النظام", "بدء فحص موارد النظام")
        
        try:
            # فحص استخدام المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            self.performance_metrics['cpu_usage'] = cpu_percent
            
            if cpu_percent < 50:
                self.log_audit("موارد النظام", f"استخدام المعالج: {cpu_percent:.1f}% - ممتاز", "SUCCESS")
            elif cpu_percent < 80:
                self.log_audit("موارد النظام", f"استخدام المعالج: {cpu_percent:.1f}% - جيد", "INFO")
            else:
                self.log_audit("موارد النظام", f"استخدام المعالج: {cpu_percent:.1f}% - مرتفع", "WARNING")
            
            # فحص استخدام الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)
            
            self.performance_metrics['memory_usage'] = memory_percent
            self.performance_metrics['memory_available_gb'] = memory_available_gb
            
            if memory_percent < 60:
                self.log_audit("موارد النظام", f"استخدام الذاكرة: {memory_percent:.1f}% - ممتاز", "SUCCESS")
            elif memory_percent < 80:
                self.log_audit("موارد النظام", f"استخدام الذاكرة: {memory_percent:.1f}% - جيد", "INFO")
            else:
                self.log_audit("موارد النظام", f"استخدام الذاكرة: {memory_percent:.1f}% - مرتفع", "WARNING")
            
            self.log_audit("موارد النظام", f"الذاكرة المتاحة: {memory_available_gb:.1f} GB", "INFO")
            
            # فحص استخدام القرص الصلب
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024**3)
            
            self.performance_metrics['disk_usage'] = disk_percent
            self.performance_metrics['disk_free_gb'] = disk_free_gb
            
            if disk_percent < 70:
                self.log_audit("موارد النظام", f"استخدام القرص: {disk_percent:.1f}% - ممتاز", "SUCCESS")
            elif disk_percent < 85:
                self.log_audit("موارد النظام", f"استخدام القرص: {disk_percent:.1f}% - جيد", "INFO")
            else:
                self.log_audit("موارد النظام", f"استخدام القرص: {disk_percent:.1f}% - مرتفع", "WARNING")
            
            self.log_audit("موارد النظام", f"المساحة المتاحة: {disk_free_gb:.1f} GB", "INFO")
            
        except Exception as e:
            self.log_audit("موارد النظام", f"خطأ في فحص موارد النظام: {str(e)}", "ERROR")
    
    def check_database_performance(self):
        """فحص أداء قاعدة البيانات"""
        self.log_audit("أداء قاعدة البيانات", "بدء فحص أداء قاعدة البيانات")
        
        try:
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            
            # اختبار سرعة الاتصال
            start_time = time.time()
            session.execute(text("SELECT 1"))
            connection_time = time.time() - start_time
            
            self.performance_metrics['db_connection_time'] = connection_time
            
            if connection_time < 0.1:
                self.log_audit("أداء قاعدة البيانات", f"زمن الاتصال: {connection_time:.3f}s - ممتاز", "SUCCESS")
            elif connection_time < 0.5:
                self.log_audit("أداء قاعدة البيانات", f"زمن الاتصال: {connection_time:.3f}s - جيد", "INFO")
            else:
                self.log_audit("أداء قاعدة البيانات", f"زمن الاتصال: {connection_time:.3f}s - بطيء", "WARNING")
            
            # اختبار سرعة الاستعلامات
            start_time = time.time()
            shipment_count = session.query(Shipment).count()
            query_time = time.time() - start_time
            
            self.performance_metrics['db_query_time'] = query_time
            self.performance_metrics['shipment_count'] = shipment_count
            
            if query_time < 0.1:
                self.log_audit("أداء قاعدة البيانات", f"زمن الاستعلام: {query_time:.3f}s - ممتاز", "SUCCESS")
            elif query_time < 0.5:
                self.log_audit("أداء قاعدة البيانات", f"زمن الاستعلام: {query_time:.3f}s - جيد", "INFO")
            else:
                self.log_audit("أداء قاعدة البيانات", f"زمن الاستعلام: {query_time:.3f}s - بطيء", "WARNING")
            
            self.log_audit("أداء قاعدة البيانات", f"عدد الشحنات: {shipment_count}", "INFO")
            
            # اختبار سرعة الإدراج
            start_time = time.time()
            test_supplier = Supplier(
                name="مورد تجريبي للأداء",
                contact_info="<EMAIL>"
            )
            session.add(test_supplier)
            session.commit()
            insert_time = time.time() - start_time
            
            # حذف البيانات التجريبية
            session.delete(test_supplier)
            session.commit()
            
            self.performance_metrics['db_insert_time'] = insert_time
            
            if insert_time < 0.1:
                self.log_audit("أداء قاعدة البيانات", f"زمن الإدراج: {insert_time:.3f}s - ممتاز", "SUCCESS")
            elif insert_time < 0.5:
                self.log_audit("أداء قاعدة البيانات", f"زمن الإدراج: {insert_time:.3f}s - جيد", "INFO")
            else:
                self.log_audit("أداء قاعدة البيانات", f"زمن الإدراج: {insert_time:.3f}s - بطيء", "WARNING")
            
            session.close()
            
        except Exception as e:
            self.log_audit("أداء قاعدة البيانات", f"خطأ في فحص أداء قاعدة البيانات: {str(e)}", "ERROR")
    
    def check_application_startup_time(self):
        """فحص زمن بدء التطبيق"""
        self.log_audit("زمن بدء التطبيق", "بدء فحص زمن بدء التطبيق")
        
        try:
            # محاكاة بدء التطبيق
            start_time = time.time()
            
            # استيراد المكونات الأساسية
            from src.ui.main_window import MainWindow
            from src.database.database_manager import DatabaseManager
            
            # تهيئة قاعدة البيانات
            db_manager = DatabaseManager()
            
            startup_time = time.time() - start_time
            self.performance_metrics['startup_time'] = startup_time
            
            if startup_time < 2.0:
                self.log_audit("زمن بدء التطبيق", f"زمن البدء: {startup_time:.2f}s - ممتاز", "SUCCESS")
            elif startup_time < 5.0:
                self.log_audit("زمن بدء التطبيق", f"زمن البدء: {startup_time:.2f}s - جيد", "INFO")
            else:
                self.log_audit("زمن بدء التطبيق", f"زمن البدء: {startup_time:.2f}s - بطيء", "WARNING")
                
        except Exception as e:
            self.log_audit("زمن بدء التطبيق", f"خطأ في فحص زمن بدء التطبيق: {str(e)}", "ERROR")
    
    def check_memory_leaks(self):
        """فحص تسريب الذاكرة"""
        self.log_audit("تسريب الذاكرة", "بدء فحص تسريب الذاكرة")
        
        try:
            import gc
            
            # قياس الذاكرة قبل العمليات
            process = psutil.Process()
            memory_before = process.memory_info().rss / (1024**2)  # MB
            
            # تشغيل عمليات متكررة
            db_manager = DatabaseManager()
            for i in range(10):
                session = db_manager.get_session()
                session.query(Supplier).all()
                session.close()
            
            # تشغيل جامع القمامة
            gc.collect()
            
            # قياس الذاكرة بعد العمليات
            memory_after = process.memory_info().rss / (1024**2)  # MB
            memory_diff = memory_after - memory_before
            
            self.performance_metrics['memory_before'] = memory_before
            self.performance_metrics['memory_after'] = memory_after
            self.performance_metrics['memory_diff'] = memory_diff
            
            if memory_diff < 5:
                self.log_audit("تسريب الذاكرة", f"استهلاك إضافي: {memory_diff:.1f}MB - ممتاز", "SUCCESS")
            elif memory_diff < 20:
                self.log_audit("تسريب الذاكرة", f"استهلاك إضافي: {memory_diff:.1f}MB - مقبول", "INFO")
            else:
                self.log_audit("تسريب الذاكرة", f"استهلاك إضافي: {memory_diff:.1f}MB - مشكوك فيه", "WARNING")
                
        except Exception as e:
            self.log_audit("تسريب الذاكرة", f"خطأ في فحص تسريب الذاكرة: {str(e)}", "ERROR")
    
    def check_concurrent_operations(self):
        """فحص العمليات المتزامنة"""
        self.log_audit("العمليات المتزامنة", "بدء فحص العمليات المتزامنة")
        
        try:
            results = []
            
            def database_operation(thread_id):
                try:
                    db_manager = DatabaseManager()
                    session = db_manager.get_session()
                    
                    start_time = time.time()
                    session.query(Supplier).count()
                    end_time = time.time()
                    
                    session.close()
                    results.append({
                        'thread_id': thread_id,
                        'time': end_time - start_time,
                        'success': True
                    })
                except Exception as e:
                    results.append({
                        'thread_id': thread_id,
                        'error': str(e),
                        'success': False
                    })
            
            # تشغيل 5 عمليات متزامنة
            threads = []
            start_time = time.time()
            
            for i in range(5):
                thread = threading.Thread(target=database_operation, args=(i,))
                threads.append(thread)
                thread.start()
            
            # انتظار انتهاء جميع العمليات
            for thread in threads:
                thread.join()
            
            total_time = time.time() - start_time
            successful_operations = sum(1 for r in results if r['success'])
            
            self.performance_metrics['concurrent_operations_time'] = total_time
            self.performance_metrics['successful_concurrent_operations'] = successful_operations
            
            if successful_operations == 5:
                self.log_audit("العمليات المتزامنة", f"جميع العمليات نجحت في {total_time:.2f}s", "SUCCESS")
            else:
                self.log_audit("العمليات المتزامنة", f"نجح {successful_operations}/5 عمليات", "WARNING")
                
        except Exception as e:
            self.log_audit("العمليات المتزامنة", f"خطأ في فحص العمليات المتزامنة: {str(e)}", "ERROR")
    
    def run_complete_audit(self):
        """تشغيل الفحص الشامل"""
        self.log_audit("فحص شامل", "بدء الفحص الشامل لأداء النظام")
        
        # 1. فحص موارد النظام
        self.check_system_resources()
        
        # 2. فحص أداء قاعدة البيانات
        self.check_database_performance()
        
        # 3. فحص زمن بدء التطبيق
        self.check_application_startup_time()
        
        # 4. فحص تسريب الذاكرة
        self.check_memory_leaks()
        
        # 5. فحص العمليات المتزامنة
        self.check_concurrent_operations()
        
        # تقرير نهائي
        self.log_audit("تقرير نهائي", "=" * 50)
        
        if not self.issues_found:
            self.log_audit("تقرير نهائي", "✅ لم يتم العثور على مشاكل في أداء النظام", "SUCCESS")
        else:
            self.log_audit("تقرير نهائي", f"⚠️ تم العثور على {len(self.issues_found)} مشكلة", "WARNING")
            for issue in self.issues_found:
                print(f"[مشكلة مكتشفة] {issue['category']}: {issue['message']} [{issue['status']}]")
        
        # عرض المقاييس الأساسية
        self.log_audit("مقاييس الأداء", "المقاييس الأساسية:", "INFO")
        for metric, value in self.performance_metrics.items():
            if isinstance(value, float):
                self.log_audit("مقاييس الأداء", f"{metric}: {value:.3f}", "INFO")
            else:
                self.log_audit("مقاييس الأداء", f"{metric}: {value}", "INFO")
        
        return {
            'success': len(self.issues_found) == 0,
            'issues_count': len(self.issues_found),
            'issues': self.issues_found,
            'audit_log': self.audit_log,
            'performance_metrics': self.performance_metrics
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("⚡ أداة فحص أداء النظام")
        print("=" * 50)
        
        audit_tool = SystemPerformanceAuditTool()
        result = audit_tool.run_complete_audit()
        
        print("\n" + "=" * 50)
        if result['success']:
            print("✅ تم اجتياز جميع فحوصات أداء النظام!")
        else:
            print(f"⚠️ تم العثور على {result['issues_count']} مشكلة في أداء النظام")
        
        return result
    except Exception as e:
        print(f"❌ خطأ في تشغيل أداة الفحص: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
