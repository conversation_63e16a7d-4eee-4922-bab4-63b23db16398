#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تصميم تبويب المستندات الجديد في شاشة طلبات الشراء
Test for the new documents tab design in purchase orders screen
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
from src.database.database_manager import DatabaseManager

def test_documents_tab_design():
    """اختبار تصميم تبويب المستندات"""
    print("🧪 اختبار تصميم تبويب المستندات الجديد...")
    
    app = QApplication(sys.argv)
    
    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # إنشاء نافذة طلبات الشراء
        window = PurchaseOrdersWindow()
        
        # التحقق من وجود دالة get_input_style
        if hasattr(window, 'get_input_style'):
            print("✅ دالة get_input_style موجودة")
            
            # اختبار الدالة
            style = window.get_input_style()
            if "border: 2px solid #bdc3c7" in style:
                print("✅ نمط الحقول صحيح")
            else:
                print("❌ نمط الحقول غير صحيح")
                
            # اختبار النمط للحقول المقروءة فقط
            readonly_style = window.get_input_style(readonly=True)
            if "#f8f9fa" in readonly_style:
                print("✅ نمط الحقول المقروءة فقط صحيح")
            else:
                print("❌ نمط الحقول المقروءة فقط غير صحيح")
        else:
            print("❌ دالة get_input_style غير موجودة")
        
        # التحقق من عناصر تبويب المستندات الجديدة
        documents_elements = [
            'contract_label',
            'contract_url_edit', 
            'contract_add_link_btn',
            'contract_add_file_btn',
            'initial_designs_label',
            'initial_designs_url_edit',
            'initial_designs_add_link_btn', 
            'initial_designs_add_file_btn',
            'final_design_label',
            'final_design_url_edit',
            'final_design_add_link_btn',
            'final_design_add_file_btn',
            'other_attachments_label',
            'other_attachments_url_edit',
            'other_attachments_add_link_btn',
            'other_attachments_add_file_btn'
        ]
        
        missing_elements = []
        for element in documents_elements:
            if hasattr(window, element):
                print(f"✅ العنصر {element} موجود")
            else:
                print(f"❌ العنصر {element} غير موجود")
                missing_elements.append(element)
        
        # التحقق من أن الأزرار لها الألوان الصحيحة
        if hasattr(window, 'contract_add_link_btn'):
            link_btn_style = window.contract_add_link_btn.styleSheet()
            if "#3498db" in link_btn_style:
                print("✅ لون أزرار الروابط صحيح (#3498db)")
            else:
                print("❌ لون أزرار الروابط غير صحيح")
        
        if hasattr(window, 'contract_add_file_btn'):
            file_btn_style = window.contract_add_file_btn.styleSheet()
            if "#27ae60" in file_btn_style:
                print("✅ لون أزرار المرفقات صحيح (#27ae60)")
            else:
                print("❌ لون أزرار المرفقات غير صحيح")
        
        # التحقق من أن الحقول مقروءة فقط
        readonly_fields = [
            'contract_url_edit',
            'initial_designs_url_edit', 
            'final_design_url_edit',
            'other_attachments_url_edit'
        ]
        
        for field_name in readonly_fields:
            if hasattr(window, field_name):
                field = getattr(window, field_name)
                if field.isReadOnly():
                    print(f"✅ الحقل {field_name} مقروء فقط")
                else:
                    print(f"❌ الحقل {field_name} ليس مقروء فقط")
        
        # التحقق من أن الأزرار مربوطة بالدوال الصحيحة
        signal_connections = [
            ('contract_add_link_btn', 'add_link_dialog'),
            ('contract_add_file_btn', 'add_file_attachment'),
            ('initial_designs_add_link_btn', 'add_link_dialog'),
            ('initial_designs_add_file_btn', 'add_file_attachment'),
            ('final_design_add_link_btn', 'add_link_dialog'),
            ('final_design_add_file_btn', 'add_file_attachment'),
            ('other_attachments_add_link_btn', 'add_link_dialog'),
            ('other_attachments_add_file_btn', 'add_file_attachment')
        ]
        
        for btn_name, method_name in signal_connections:
            if hasattr(window, btn_name) and hasattr(window, method_name):
                print(f"✅ الزر {btn_name} والدالة {method_name} موجودان")
            else:
                print(f"❌ مشكلة في ربط الزر {btn_name} بالدالة {method_name}")
        
        # عرض النافذة للاختبار البصري
        window.show()
        
        if len(missing_elements) == 0:
            print("\n🎉 جميع عناصر التصميم الجديد موجودة!")
            print("✨ تم تطبيق التصميم الجديد بنجاح")
            print("🎨 التصميم يطابق شاشة الشحنة الجديدة")
        else:
            print(f"\n⚠️ يوجد {len(missing_elements)} عناصر مفقودة")
            print("العناصر المفقودة:", missing_elements)
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = test_documents_tab_design()
    sys.exit(exit_code)
