#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PySide6.QtWidgets import QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt

class TestButtonsDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار الأزرار")
        self.setModal(True)
        self.resize(600, 400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان
        title = QLabel("اختبار أزرار التحكم")
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 20px;")
        main_layout.addWidget(title)
        
        # مساحة فارغة
        main_layout.addStretch()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        # زر إضافة
        new_btn = QPushButton("🆕 إضافة")
        new_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر حفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر خروج
        exit_btn = QPushButton("🚪 خروج")
        exit_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # إضافة الأزرار
        buttons_layout.addWidget(new_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(exit_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # ربط الأزرار
        new_btn.clicked.connect(lambda: print("تم الضغط على زر إضافة"))
        save_btn.clicked.connect(lambda: print("تم الضغط على زر حفظ"))
        edit_btn.clicked.connect(lambda: print("تم الضغط على زر تعديل"))
        exit_btn.clicked.connect(self.accept)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    dialog = TestButtonsDialog()
    dialog.exec()
    
    print("تم إغلاق النافذة")
