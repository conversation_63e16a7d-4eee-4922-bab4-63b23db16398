#!/usr/bin/env python3
"""
عرض مبسط لنافذة تتبع طلبات الشراء
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🚀 تشغيل نافذة تتبع طلبات الشراء (نسخة مبسطة)...")
        
        from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                       QHBoxLayout, QGridLayout, QGroupBox, QTableWidget, 
                                       QComboBox, QLineEdit, QPushButton, QLabel, QFrame,
                                       QHeaderView, QAbstractItemView, QStatusBar, QToolBar)
        from PySide6.QtCore import Qt, QDate
        from PySide6.QtGui import QFont, QAction
        
        app = QApplication(sys.argv)
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("تتبع طلبات الشراء المتقدم - ProShipment")
        window.setMinimumSize(1600, 900)
        window.showMaximized()
        window.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # قسم الإحصائيات
        stats_group = QGroupBox("الإحصائيات")
        stats_layout = QGridLayout(stats_group)
        
        # إنشاء بطاقات الإحصائيات
        stats_data = [
            ("إجمالي الطلبات", "25", "#007bff"),
            ("الطلبات النشطة", "18", "#28a745"),
            ("الطلبات المكتملة", "7", "#17a2b8"),
            ("الطلبات المتأخرة", "3", "#dc3545"),
            ("إجمالي القيمة", "125,000.00", "#ffc107"),
            ("متوسط وقت التسليم", "12 يوم", "#6f42c1")
        ]
        
        for i, (title, value, color) in enumerate(stats_data):
            card = QFrame()
            card.setFrameStyle(QFrame.Box)
            card.setStyleSheet(f"""
                QFrame {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}, stop:1 #ffffff);
                    border: 2px solid {color};
                    border-radius: 10px;
                    padding: 10px;
                    margin: 5px;
                }}
            """)
            
            card_layout = QVBoxLayout(card)
            
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; font-size: 12px; color: #333;")
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #000;")
            value_label.setAlignment(Qt.AlignCenter)
            
            card_layout.addWidget(title_label)
            card_layout.addWidget(value_label)
            
            row = i // 3
            col = i % 3
            stats_layout.addWidget(card, row, col)
        
        main_layout.addWidget(stats_group)
        
        # قسم البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QHBoxLayout(search_group)
        
        # حقل البحث
        search_edit = QLineEdit()
        search_edit.setPlaceholderText("البحث في جميع الحقول...")
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(search_edit)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "مسودة", "مرسل", "مؤكد", "جزئي", "مكتمل", "ملغي"])
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(status_filter)
        
        # فلتر المورد
        supplier_filter = QComboBox()
        supplier_filter.addItems(["جميع الموردين", "مورد 1", "مورد 2", "مورد 3"])
        search_layout.addWidget(QLabel("المورد:"))
        search_layout.addWidget(supplier_filter)
        
        # أزرار
        search_btn = QPushButton("بحث")
        clear_btn = QPushButton("مسح")
        search_layout.addWidget(search_btn)
        search_layout.addWidget(clear_btn)
        
        main_layout.addWidget(search_group)
        
        # جدول الطلبات
        table_group = QGroupBox("طلبات الشراء")
        table_layout = QVBoxLayout(table_group)
        
        orders_table = QTableWidget()
        orders_table.setColumnCount(15)
        orders_table.setHorizontalHeaderLabels([
            "رقم الطلب", "تاريخ الطلب", "المورد", "حالة الطلب", 
            "عدد الأصناف", "إجمالي الكمية", "إجمالي القيمة", "العملة",
            "تاريخ التسليم المتوقع", "الكمية المسلمة", "الكمية المتبقية",
            "نسبة الإنجاز", "الأيام المتبقية", "حالة التأخير", "ملاحظات"
        ])
        
        # تكوين الجدول
        orders_table.setAlternatingRowColors(True)
        orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        orders_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        orders_table.horizontalHeader().setStretchLastSection(True)
        orders_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        
        # إضافة بيانات تجريبية
        sample_data = [
            ["PO-001", "2024-01-15", "مورد ABC", "مؤكد", "5", "100", "15,000", "ريال", 
             "2024-02-15", "75", "25", "75%", "5", "في الوقت", "جاري التنفيذ"],
            ["PO-002", "2024-01-20", "مورد XYZ", "جزئي", "3", "50", "8,500", "ريال", 
             "2024-02-10", "30", "20", "60%", "-2", "متأخر", "تأخير في التسليم"],
            ["PO-003", "2024-01-25", "مورد DEF", "مكتمل", "8", "200", "25,000", "ريال", 
             "2024-02-20", "200", "0", "100%", "0", "مكتمل", "تم التسليم بالكامل"]
        ]
        
        orders_table.setRowCount(len(sample_data))
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                from PySide6.QtWidgets import QTableWidgetItem
                orders_table.setItem(row, col, QTableWidgetItem(str(value)))
        
        table_layout.addWidget(orders_table)
        main_layout.addWidget(table_group)
        
        # شريط الأدوات
        toolbar = window.addToolBar("أدوات التتبع")
        toolbar.setMovable(False)
        
        refresh_action = QAction("تحديث", window)
        export_action = QAction("تصدير", window)
        print_action = QAction("طباعة", window)
        settings_action = QAction("إعدادات", window)
        close_action = QAction("إغلاق", window)
        
        toolbar.addAction(refresh_action)
        toolbar.addSeparator()
        toolbar.addAction(export_action)
        toolbar.addAction(print_action)
        toolbar.addSeparator()
        toolbar.addAction(settings_action)
        toolbar.addSeparator()
        toolbar.addAction(close_action)
        
        # شريط الحالة
        status_bar = window.statusBar()
        status_bar.showMessage("جاهز - تم تحميل 3 طلبات")
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📊 النافذة تحتوي على:")
        print("   ✓ قسم الإحصائيات مع 6 بطاقات ملونة")
        print("   ✓ قسم البحث والفلترة المتقدمة")
        print("   ✓ جدول تفصيلي بـ 15 عمود")
        print("   ✓ شريط أدوات متكامل")
        print("   ✓ شريط حالة مع مؤشرات")
        print("   ✓ بيانات تجريبية للعرض")
        
        # عرض النافذة
        window.show()
        
        print("🎉 النافذة جاهزة للاستخدام!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
