#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الفصل الكامل للتعبئة التلقائية
Complete Auto-Fill Separation Test
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.ui.widgets.smart_shipping_company_widget import SmartShippingCompanyWidget
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد المكونات المطلوبة: {e}")

class CompleteAutoFillSeparationTester:
    """مختبر الفصل الكامل للتعبئة التلقائية"""
    
    def __init__(self):
        """تهيئة المختبر"""
        self.test_results = []
        
    def log_test(self, test_name: str, result: str, status: str):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {test_name}: {result} - {status}"
        self.test_results.append(log_entry)
        print(log_entry)
    
    def test_default_disabled_state(self):
        """اختبار أن التعبئة التلقائية معطلة افتراضياً"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            # إنشاء واجهة ذكية جديدة
            widget = SmartShippingCompanyWidget()
            
            # التحقق من الحالة الافتراضية
            if not widget.is_auto_fill_enabled():
                self.log_test("الحالة الافتراضية", "التعبئة التلقائية معطلة افتراضياً", "✅ نجح")
            else:
                self.log_test("الحالة الافتراضية", "التعبئة التلقائية مفعلة افتراضياً", "❌ فشل")
                return False
            
            # التحقق من حالة الزر
            if not widget.auto_fill_toggle_button.isChecked():
                self.log_test("حالة زر التفعيل", "زر التفعيل معطل افتراضياً", "✅ نجح")
            else:
                self.log_test("حالة زر التفعيل", "زر التفعيل مفعل افتراضياً", "❌ فشل")
                return False
            
            # التحقق من أيقونة الزر
            if widget.auto_fill_toggle_button.text() == "🔒":
                self.log_test("أيقونة زر التفعيل", "أيقونة القفل المغلق صحيحة", "✅ نجح")
            else:
                self.log_test("أيقونة زر التفعيل", f"أيقونة خاطئة: {widget.auto_fill_toggle_button.text()}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("الحالة الافتراضية", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_manual_activation(self):
        """اختبار التفعيل اليدوي للتعبئة التلقائية"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            widget = SmartShippingCompanyWidget()
            
            # التفعيل اليدوي عبر الزر
            widget.auto_fill_toggle_button.setChecked(True)
            widget.toggle_auto_fill()
            
            # التحقق من التفعيل
            if widget.is_auto_fill_enabled():
                self.log_test("التفعيل اليدوي", "تم تفعيل التعبئة التلقائية يدوياً", "✅ نجح")
            else:
                self.log_test("التفعيل اليدوي", "فشل في التفعيل اليدوي", "❌ فشل")
                return False
            
            # التحقق من تغيير الأيقونة
            if widget.auto_fill_toggle_button.text() == "🔓":
                self.log_test("تغيير الأيقونة", "تم تغيير الأيقونة إلى القفل المفتوح", "✅ نجح")
            else:
                self.log_test("تغيير الأيقونة", f"أيقونة خاطئة: {widget.auto_fill_toggle_button.text()}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("التفعيل اليدوي", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_manual_deactivation(self):
        """اختبار التعطيل اليدوي للتعبئة التلقائية"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            widget = SmartShippingCompanyWidget()
            
            # تفعيل أولاً
            widget.set_auto_fill_enabled(True)
            widget.auto_fill_toggle_button.setChecked(True)
            
            # ثم التعطيل عبر الزر
            widget.auto_fill_toggle_button.setChecked(False)
            widget.toggle_auto_fill()
            
            # التحقق من التعطيل
            if not widget.is_auto_fill_enabled():
                self.log_test("التعطيل اليدوي", "تم تعطيل التعبئة التلقائية يدوياً", "✅ نجح")
            else:
                self.log_test("التعطيل اليدوي", "فشل في التعطيل اليدوي", "❌ فشل")
                return False
            
            # التحقق من إعادة الأيقونة
            if widget.auto_fill_toggle_button.text() == "🔒":
                self.log_test("إعادة الأيقونة", "تم إعادة الأيقونة إلى القفل المغلق", "✅ نجح")
            else:
                self.log_test("إعادة الأيقونة", f"أيقونة خاطئة: {widget.auto_fill_toggle_button.text()}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("التعطيل اليدوي", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_input_behavior_when_disabled(self):
        """اختبار سلوك الإدخال عند تعطيل التعبئة التلقائية"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            widget = SmartShippingCompanyWidget()
            
            # التأكد من أن التعبئة التلقائية معطلة
            widget.set_auto_fill_enabled(False)
            
            # إدخال اسم شركة
            widget.set_company_name("MAERSK LINE", auto_validate=False)
            
            # التحقق من عدم حدوث تحقق تلقائي
            company_name = widget.get_company_name()
            if company_name == "MAERSK LINE":
                self.log_test("الإدخال بدون تحقق", "تم إدخال اسم الشركة بدون تحقق تلقائي", "✅ نجح")
            else:
                self.log_test("الإدخال بدون تحقق", f"فشل في الإدخال: {company_name}", "❌ فشل")
                return False
            
            # محاولة التحقق اليدوي (يجب أن يظهر رسالة التعطيل)
            widget.validate_input()
            
            # التحقق من رسالة التعطيل
            status_text = widget.status_label.text()
            if "معطلة" in status_text:
                self.log_test("رسالة التعطيل", "تم عرض رسالة التعطيل بشكل صحيح", "✅ نجح")
            else:
                self.log_test("رسالة التعطيل", f"رسالة خاطئة: {status_text}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("سلوك الإدخال المعطل", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_input_behavior_when_enabled(self):
        """اختبار سلوك الإدخال عند تفعيل التعبئة التلقائية"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            widget = SmartShippingCompanyWidget()
            
            # تفعيل التعبئة التلقائية
            widget.set_auto_fill_enabled(True)
            
            # إدخال اسم شركة مع تحقق تلقائي
            widget.set_company_name("MSC", auto_validate=True)
            
            # التحقق من الإدخال
            company_name = widget.get_company_name()
            if company_name == "MSC":
                self.log_test("الإدخال مع تحقق", "تم إدخال اسم الشركة مع تحقق تلقائي", "✅ نجح")
            else:
                self.log_test("الإدخال مع تحقق", f"فشل في الإدخال: {company_name}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("سلوك الإدخال المفعل", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_toggle_functionality(self):
        """اختبار وظيفة التبديل الكاملة"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            widget = SmartShippingCompanyWidget()
            
            # البدء بالحالة المعطلة
            initial_state = widget.is_auto_fill_enabled()
            
            # التفعيل
            widget.auto_fill_toggle_button.setChecked(True)
            widget.toggle_auto_fill()
            enabled_state = widget.is_auto_fill_enabled()
            
            # التعطيل مرة أخرى
            widget.auto_fill_toggle_button.setChecked(False)
            widget.toggle_auto_fill()
            final_state = widget.is_auto_fill_enabled()
            
            # التحقق من التسلسل الصحيح
            if not initial_state and enabled_state and not final_state:
                self.log_test("وظيفة التبديل", "التبديل يعمل بشكل صحيح: معطل -> مفعل -> معطل", "✅ نجح")
            else:
                self.log_test("وظيفة التبديل", f"فشل في التبديل: {initial_state} -> {enabled_state} -> {final_state}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("وظيفة التبديل", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار الفصل الكامل للتعبئة التلقائية")
        print("=" * 60)
        
        tests = [
            ("اختبار الحالة الافتراضية المعطلة", self.test_default_disabled_state),
            ("اختبار التفعيل اليدوي", self.test_manual_activation),
            ("اختبار التعطيل اليدوي", self.test_manual_deactivation),
            ("اختبار سلوك الإدخال المعطل", self.test_input_behavior_when_disabled),
            ("اختبار سلوك الإدخال المفعل", self.test_input_behavior_when_enabled),
            ("اختبار وظيفة التبديل الكاملة", self.test_toggle_functionality)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            print(f"\n🔍 {test_name}:")
            print("-" * 40)
            
            try:
                if test_function():
                    passed_tests += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {str(e)}")
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("✅ جميع الاختبارات نجحت! الفصل الكامل للتعبئة التلقائية يعمل بشكل صحيح")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه")
        
        return passed_tests, total_tests

def main():
    """الدالة الرئيسية"""
    try:
        tester = CompleteAutoFillSeparationTester()
        passed, total = tester.run_all_tests()
        
        print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
        
        if passed == total:
            print("\n🎉 تم تطبيق الفصل الكامل للتعبئة التلقائية بنجاح!")
            print("🔒 التعبئة التلقائية معطلة افتراضياً في جميع الأوضاع")
            print("🔓 يمكن تفعيلها يدوياً عند الحاجة فقط")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
