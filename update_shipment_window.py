#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لاستبدال نافذة الشحنة الأصلية بالنافذة النهائية العاملة
"""

import os
import shutil
from datetime import datetime

def backup_original_file():
    """إنشاء نسخة احتياطية من الملف الأصلي"""
    original_file = "src/ui/shipments/new_shipment_window.py"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"src/ui/shipments/new_shipment_window_backup_{timestamp}.py"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_file}")
        return True
    else:
        print(f"❌ الملف الأصلي غير موجود: {original_file}")
        return False

def replace_with_final_version():
    """استبدال الملف الأصلي بالنسخة النهائية"""
    final_file = "src/ui/shipments/new_shipment_window_final.py"
    original_file = "src/ui/shipments/new_shipment_window.py"
    
    if os.path.exists(final_file):
        # قراءة محتوى النافذة النهائية
        with open(final_file, 'r', encoding='utf-8') as f:
            final_content = f.read()
        
        # استبدال اسم الكلاس
        updated_content = final_content.replace(
            "class NewShipmentWindowFinal(QDialog):",
            "class NewShipmentWindow(QDialog):"
        )
        
        # استبدال الاستيراد في main
        updated_content = updated_content.replace(
            "from src.ui.shipments.new_shipment_window_final import NewShipmentWindowFinal",
            "from src.ui.shipments.new_shipment_window import NewShipmentWindow"
        )
        
        updated_content = updated_content.replace(
            "window = NewShipmentWindowFinal()",
            "window = NewShipmentWindow()"
        )
        
        # كتابة المحتوى المحدث للملف الأصلي
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ تم استبدال الملف الأصلي بالنسخة النهائية")
        return True
    else:
        print(f"❌ النسخة النهائية غير موجودة: {final_file}")
        return False

def update_imports_in_main_files():
    """تحديث الاستيرادات في الملفات الرئيسية"""
    files_to_update = [
        "src/ui/shipments/shipment_management_window.py",
        "main.py"
    ]
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # لا حاجة لتغيير الاستيرادات لأن اسم الكلاس سيبقى نفسه
                print(f"✅ فحص الملف: {file_path} - لا حاجة لتحديث")
                
            except Exception as e:
                print(f"❌ خطأ في فحص الملف {file_path}: {str(e)}")
        else:
            print(f"⚠️ الملف غير موجود: {file_path}")

def test_updated_window():
    """اختبار النافذة المحدثة"""
    try:
        import sys
        sys.path.append('.')
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        window = NewShipmentWindow()
        
        # فحص وجود الأزرار
        buttons_found = []
        if hasattr(window, 'new_button'):
            buttons_found.append("new_button")
        if hasattr(window, 'save_button'):
            buttons_found.append("save_button")
        if hasattr(window, 'edit_button'):
            buttons_found.append("edit_button")
        if hasattr(window, 'exit_button'):
            buttons_found.append("exit_button")
        
        print(f"✅ النافذة المحدثة تحتوي على الأزرار: {buttons_found}")
        
        window.show()
        print("✅ تم فتح النافذة المحدثة بنجاح")
        
        # لا نشغل app.exec() لأننا في سكريبت
        window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة المحدثة: {str(e)}")
        return False

def main():
    """تنفيذ عملية التحديث"""
    print("=== تحديث نافذة الشحنة الجديدة ===")
    print("🔄 بدء عملية استبدال النافذة الأصلية بالنسخة النهائية العاملة...")
    
    # 1. إنشاء نسخة احتياطية
    print("\n1️⃣ إنشاء نسخة احتياطية...")
    if not backup_original_file():
        print("❌ فشل في إنشاء النسخة الاحتياطية")
        return
    
    # 2. استبدال الملف
    print("\n2️⃣ استبدال الملف الأصلي...")
    if not replace_with_final_version():
        print("❌ فشل في استبدال الملف")
        return
    
    # 3. تحديث الاستيرادات
    print("\n3️⃣ فحص الاستيرادات...")
    update_imports_in_main_files()
    
    # 4. اختبار النافذة المحدثة
    print("\n4️⃣ اختبار النافذة المحدثة...")
    if test_updated_window():
        print("\n✅ تم تحديث نافذة الشحنة بنجاح!")
        print("🎉 النافذة الآن تحتوي على أزرار التحكم العاملة:")
        print("   • 🆕 إضافة - لإنشاء شحنة جديدة")
        print("   • 💾 حفظ - لحفظ الشحنة الحالية")
        print("   • ✏️ تعديل - لتعديل الشحنة")
        print("   • 🚪 خروج - لإغلاق النافذة")
        print("\n📝 ملاحظة: تم حفظ النسخة الأصلية كنسخة احتياطية")
    else:
        print("\n❌ فشل في اختبار النافذة المحدثة")

if __name__ == "__main__":
    main()
