#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حل مشكلة تكرار رقم الشحنة
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_unique_shipment_number():
    """اختبار توليد رقم شحنة فريد"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار حل مشكلة تكرار رقم الشحنة ===")
        
        window = NewShipmentWindow()
        
        # اختبار 1: التحقق من الرقم الأولي
        initial_number = window.shipment_number_edit.text()
        print(f"1️⃣ رقم الشحنة الأولي: {initial_number}")
        
        # اختبار 2: توليد أرقام متعددة للتأكد من الفرادة
        print("\n2️⃣ توليد أرقام شحنة متعددة:")
        generated_numbers = []
        for i in range(5):
            new_number = window.generate_unique_shipment_number()
            generated_numbers.append(new_number)
            print(f"   - الرقم {i+1}: {new_number}")
        
        # التحقق من عدم التكرار
        unique_numbers = set(generated_numbers)
        if len(unique_numbers) == len(generated_numbers):
            print("   ✅ جميع الأرقام فريدة")
        else:
            print("   ❌ هناك أرقام مكررة")
        
        # اختبار 3: ملء البيانات التجريبية
        print("\n3️⃣ ملء البيانات التجريبية:")
        window.fill_test_data()
        test_number = window.shipment_number_edit.text()
        print(f"   - رقم الشحنة بعد التعبئة: {test_number}")
        
        # اختبار 4: التحقق من صحة البيانات
        print("\n4️⃣ التحقق من صحة البيانات:")
        validation_result = window.validate_data()
        print(f"   - نتيجة التحقق: {'✅ نجح' if validation_result else '❌ فشل'}")
        
        # اختبار 5: محاولة الحفظ (بدون حفظ فعلي)
        print("\n5️⃣ اختبار إعداد الحفظ:")
        if validation_result:
            print("   ✅ البيانات صحيحة - يمكن المتابعة للحفظ")
            print("   📝 سيتم استخدام رقم الشحنة الفريد في الحفظ")
        else:
            print("   ❌ البيانات غير صحيحة - لن يتم الحفظ")
        
        print(f"\n{'='*50}")
        return validation_result and len(unique_numbers) == len(generated_numbers)
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_unique_shipment_number()
    if success:
        print("🎉 تم حل مشكلة تكرار رقم الشحنة بنجاح!")
        print("✅ الآن يمكن حفظ الشحنات بأرقام فريدة")
        print("🔄 يمكن توليد أرقام جديدة باستخدام زر التوليد")
    else:
        print("⚠️ لا تزال هناك مشكلة في توليد أرقام الشحنة")
        print("❌ يرجى مراجعة الكود مرة أخرى")
