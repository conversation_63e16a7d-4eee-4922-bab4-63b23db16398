#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام تحديث كميات طلبات الشراء
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_system():
    """اختبار شامل للنظام"""
    try:
        from src.database.models import (
            PurchaseOrder, PurchaseOrderItem, Shipment, ShipmentItem, 
            Item, Supplier
        )
        from src.database.database_manager import DatabaseManager
        from datetime import datetime
        
        print("🧪 اختبار شامل لنظام تحديث كميات طلبات الشراء")
        print("=" * 70)
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # 1. إنشاء بيانات تجريبية
        print("1️⃣ إنشاء بيانات تجريبية...")
        
        # مورد
        supplier = session.query(Supplier).first()
        if not supplier:
            supplier = Supplier(
                name="مورد تجريبي شامل",
                code="COMP_SUPPLIER_001",
                contact_person="محمد أحمد",
                phone="987654321"
            )
            session.add(supplier)
            session.flush()
        
        # صنف
        item = session.query(Item).first()
        if not item:
            item = Item(
                code="COMP_ITEM_001",
                name="صنف تجريبي شامل",
                unit="قطعة",
                weight=2.0
            )
            session.add(item)
            session.flush()
        
        # طلب شراء
        order = PurchaseOrder(
            order_number=f"PO-COMP-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            order_date=datetime.now(),
            supplier_id=supplier.id,
            order_status="مؤكد",
            notes="طلب شراء تجريبي شامل"
        )
        session.add(order)
        session.flush()
        
        # صنف طلب شراء
        order_item = PurchaseOrderItem(
            purchase_order_id=order.id,
            item_id=item.id,
            quantity=200.0,  # كمية كبيرة للاختبار
            unit_price=15.0,
            total_price=3000.0,
            delivered_quantity=0.0,
            remaining_quantity=200.0
        )
        session.add(order_item)
        session.commit()
        
        print(f"✅ تم إنشاء البيانات:")
        print(f"   - طلب الشراء: {order.order_number}")
        print(f"   - الكمية المطلوبة: {order_item.quantity}")
        
        # 2. اختبار الشحنة الأولى
        print(f"\n2️⃣ اختبار الشحنة الأولى...")
        
        shipment1 = Shipment(
            shipment_number=f"SH-COMP-001-{datetime.now().strftime('%H%M%S')}",
            shipment_date=datetime.now(),
            supplier_id=supplier.id,
            shipment_status="قيد التحضير"
        )
        session.add(shipment1)
        session.flush()
        
        # شحن 60 قطعة
        ship_quantity1 = 60.0
        shipment_item1 = ShipmentItem(
            shipment_id=shipment1.id,
            item_id=item.id,
            purchase_order_item_id=order_item.id,
            quantity=ship_quantity1,
            unit_price=15.0,
            total_price=ship_quantity1 * 15.0
        )
        session.add(shipment_item1)
        
        # تحديث كميات طلب الشراء
        order_item.delivered_quantity += ship_quantity1
        order_item.remaining_quantity = order_item.quantity - order_item.delivered_quantity
        
        session.commit()
        
        print(f"✅ الشحنة الأولى:")
        print(f"   - رقم الشحنة: {shipment1.shipment_number}")
        print(f"   - الكمية المشحونة: {ship_quantity1}")
        print(f"   - الكمية المسلمة الإجمالية: {order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية: {order_item.remaining_quantity}")
        
        # 3. اختبار الشحنة الثانية
        print(f"\n3️⃣ اختبار الشحنة الثانية...")
        
        shipment2 = Shipment(
            shipment_number=f"SH-COMP-002-{datetime.now().strftime('%H%M%S')}",
            shipment_date=datetime.now(),
            supplier_id=supplier.id,
            shipment_status="قيد التحضير"
        )
        session.add(shipment2)
        session.flush()
        
        # شحن 80 قطعة إضافية
        ship_quantity2 = 80.0
        shipment_item2 = ShipmentItem(
            shipment_id=shipment2.id,
            item_id=item.id,
            purchase_order_item_id=order_item.id,
            quantity=ship_quantity2,
            unit_price=15.0,
            total_price=ship_quantity2 * 15.0
        )
        session.add(shipment_item2)
        
        # تحديث كميات طلب الشراء
        order_item.delivered_quantity += ship_quantity2
        order_item.remaining_quantity = order_item.quantity - order_item.delivered_quantity
        
        session.commit()
        
        print(f"✅ الشحنة الثانية:")
        print(f"   - رقم الشحنة: {shipment2.shipment_number}")
        print(f"   - الكمية المشحونة: {ship_quantity2}")
        print(f"   - الكمية المسلمة الإجمالية: {order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية: {order_item.remaining_quantity}")
        
        # 4. اختبار محاولة شحن كمية تتجاوز المتاح
        print(f"\n4️⃣ اختبار محاولة شحن كمية تتجاوز المتاح...")
        
        remaining_quantity = order_item.remaining_quantity
        over_quantity = remaining_quantity + 10.0  # كمية تتجاوز المتاح
        
        print(f"   - الكمية المتبقية: {remaining_quantity}")
        print(f"   - الكمية المطلوب شحنها: {over_quantity}")
        
        if over_quantity > remaining_quantity:
            print(f"   ❌ تجاوز الحد المسموح! الفرق: {over_quantity - remaining_quantity}")
            print(f"   ✅ نظام التحقق سيمنع هذه العملية")
        
        # 5. اختبار الشحنة الأخيرة (الكمية المتبقية بالضبط)
        print(f"\n5️⃣ اختبار الشحنة الأخيرة...")
        
        shipment3 = Shipment(
            shipment_number=f"SH-COMP-003-{datetime.now().strftime('%H%M%S')}",
            shipment_date=datetime.now(),
            supplier_id=supplier.id,
            shipment_status="قيد التحضير"
        )
        session.add(shipment3)
        session.flush()
        
        # شحن الكمية المتبقية بالضبط
        final_quantity = order_item.remaining_quantity
        shipment_item3 = ShipmentItem(
            shipment_id=shipment3.id,
            item_id=item.id,
            purchase_order_item_id=order_item.id,
            quantity=final_quantity,
            unit_price=15.0,
            total_price=final_quantity * 15.0
        )
        session.add(shipment_item3)
        
        # تحديث كميات طلب الشراء
        order_item.delivered_quantity += final_quantity
        order_item.remaining_quantity = order_item.quantity - order_item.delivered_quantity
        
        session.commit()
        
        print(f"✅ الشحنة الأخيرة:")
        print(f"   - رقم الشحنة: {shipment3.shipment_number}")
        print(f"   - الكمية المشحونة: {final_quantity}")
        print(f"   - الكمية المسلمة الإجمالية: {order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية: {order_item.remaining_quantity}")
        
        # 6. اختبار حذف شحنة واستعادة الكميات
        print(f"\n6️⃣ اختبار حذف شحنة واستعادة الكميات...")
        
        # حفظ الحالة قبل الحذف
        before_delete_delivered = order_item.delivered_quantity
        before_delete_remaining = order_item.remaining_quantity
        deleted_quantity = shipment_item2.quantity
        
        print(f"   - قبل الحذف - المسلم: {before_delete_delivered}, المتبقي: {before_delete_remaining}")
        print(f"   - كمية الشحنة المحذوفة: {deleted_quantity}")
        
        # حذف الشحنة الثانية واستعادة الكميات
        order_item.delivered_quantity -= deleted_quantity
        order_item.remaining_quantity = order_item.quantity - order_item.delivered_quantity
        
        # حذف الشحنة
        session.delete(shipment_item2)
        session.delete(shipment2)
        session.commit()
        
        print(f"   - بعد الحذف - المسلم: {order_item.delivered_quantity}, المتبقي: {order_item.remaining_quantity}")
        print(f"   ✅ تم استعادة الكميات بنجاح")
        
        # 7. النتائج النهائية
        print(f"\n7️⃣ النتائج النهائية:")
        print(f"   - الكمية المطلوبة الأصلية: {order_item.quantity}")
        print(f"   - إجمالي الكمية المسلمة: {order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية: {order_item.remaining_quantity}")
        print(f"   - نسبة التسليم: {(order_item.delivered_quantity / order_item.quantity) * 100:.1f}%")
        
        # التحقق من صحة الحسابات
        expected_remaining = order_item.quantity - order_item.delivered_quantity
        if abs(order_item.remaining_quantity - expected_remaining) < 0.01:
            print("   ✅ جميع الحسابات صحيحة!")
        else:
            print("   ❌ خطأ في الحسابات!")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار الشامل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 اختبار شامل لنظام تحديث كميات طلبات الشراء")
    print("=" * 70)
    
    success = test_complete_system()
    
    if success:
        print(f"\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام مع الميزات التالية:")
        print("   • تحديث كميات طلب الشراء تلقائياً عند الشحن")
        print("   • التحقق من الكميات المتاحة قبل السماح بالشحن")
        print("   • استعادة الكميات عند حذف أو تعديل الشحنات")
        print("   • عرض الكميات المسلمة والمتبقية في نافذة طلبات الشراء")
        print("   • ربط أصناف الشحنة بطلبات الشراء")
    else:
        print(f"\n❌ فشل في بعض الاختبارات!")
        
    print("\n" + "=" * 70)
