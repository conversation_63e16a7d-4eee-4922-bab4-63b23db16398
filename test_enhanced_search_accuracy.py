#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دقة البحث المحسن للتعبئة التلقائية
Enhanced Search Accuracy Test
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class EnhancedSearchTester:
    """مختبر دقة البحث المحسن"""
    
    def __init__(self):
        """تهيئة المختبر"""
        self.test_results = []
        
    def log_test(self, test_name: str, result: str, status: str):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {test_name}: {result} - {status}"
        self.test_results.append(log_entry)
        print(log_entry)
    
    async def test_multiple_sources_search(self):
        """اختبار البحث في مصادر متعددة"""
        try:
            from src.services.web_scraping_service import WebScrapingService
            
            web_service = WebScrapingService()
            
            # اختبار البحث في مصادر متعددة
            test_container = "COSU1234567"
            results = await web_service.search_all_carriers(container_number=test_container)
            
            if results:
                self.log_test("البحث في مصادر متعددة", f"تم العثور على {len(results)} نتيجة", "✅ نجح")
                
                # التحقق من تنوع المصادر
                sources = set()
                for result in results:
                    if hasattr(result, 'source'):
                        sources.add(result.source.split('(')[0].strip())
                
                if len(sources) >= 3:
                    self.log_test("تنوع المصادر", f"مصادر متنوعة: {len(sources)}", "✅ نجح")
                else:
                    self.log_test("تنوع المصادر", f"مصادر محدودة: {len(sources)}", "⚠️ متوسط")
                
                # التحقق من درجات الثقة
                confidence_scores = [getattr(result, 'confidence_score', 0) for result in results]
                avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
                
                if avg_confidence >= 70:
                    self.log_test("درجات الثقة", f"متوسط الثقة: {avg_confidence:.1f}%", "✅ نجح")
                elif avg_confidence >= 50:
                    self.log_test("درجات الثقة", f"متوسط الثقة: {avg_confidence:.1f}%", "⚠️ متوسط")
                else:
                    self.log_test("درجات الثقة", f"متوسط الثقة: {avg_confidence:.1f}%", "❌ منخفض")
                
                return True
            else:
                self.log_test("البحث في مصادر متعددة", "لم يتم العثور على نتائج", "❌ فشل")
                return False
                
        except Exception as e:
            self.log_test("البحث في مصادر متعددة", f"خطأ: {str(e)}", "❌ فشل")
            return False
    
    async def test_data_quality_assessment(self):
        """اختبار تقييم جودة البيانات"""
        try:
            from src.services.web_scraping_service import WebScrapingService
            
            web_service = WebScrapingService()
            
            # اختبار مع حاويات مختلفة
            test_containers = ["MSCU1234567", "COSU7654321", "MSKU9876543"]
            
            quality_results = []
            
            for container in test_containers:
                results = await web_service.search_all_carriers(container_number=container)
                
                if results:
                    # استخراج أفضل البيانات
                    best_data = web_service.extract_best_data_enhanced(results)
                    
                    if best_data:
                        quality = best_data.get('data_quality', 'Unknown')
                        sources_count = best_data.get('total_sources', 0)
                        
                        quality_results.append({
                            'container': container,
                            'quality': quality,
                            'sources': sources_count,
                            'fields': len([k for k in best_data.keys() if k not in ['data_quality', 'sources_used', 'total_sources']])
                        })
            
            if quality_results:
                high_quality = len([r for r in quality_results if r['quality'] == 'High'])
                medium_quality = len([r for r in quality_results if r['quality'] == 'Medium'])
                
                self.log_test("تقييم جودة البيانات", 
                             f"عالية: {high_quality}, متوسطة: {medium_quality}, إجمالي: {len(quality_results)}", 
                             "✅ نجح")
                
                # طباعة تفاصيل الجودة
                for result in quality_results:
                    print(f"   📊 {result['container']}: جودة {result['quality']}, {result['sources']} مصادر, {result['fields']} حقول")
                
                return True
            else:
                self.log_test("تقييم جودة البيانات", "لم يتم تقييم أي بيانات", "❌ فشل")
                return False
                
        except Exception as e:
            self.log_test("تقييم جودة البيانات", f"خطأ: {str(e)}", "❌ فشل")
            return False
    
    async def test_enhanced_fallback_data(self):
        """اختبار البيانات الاحتياطية المحسنة"""
        try:
            from src.services.web_scraping_service import WebScrapingService
            
            web_service = WebScrapingService()
            
            # اختبار البيانات الاحتياطية لشركات مختلفة
            test_cases = [
                ("MSCU1234567", "MSC"),
                ("COSU7654321", "COSCO"),
                ("MSKU9876543", "MAERSK")
            ]
            
            enhanced_data_count = 0
            
            for container, expected_carrier in test_cases:
                fallback_data = web_service._create_enhanced_fallback_data_searates(container, None)
                
                if fallback_data:
                    # التحقق من الحقول المحسنة
                    enhanced_fields = [
                        'shipping_method', 'shipping_type', 'final_destination',
                        'confidence_score', 'search_timestamp'
                    ]
                    
                    missing_fields = []
                    for field in enhanced_fields:
                        if not hasattr(fallback_data, field) or not getattr(fallback_data, field):
                            missing_fields.append(field)
                    
                    if not missing_fields:
                        enhanced_data_count += 1
                        self.log_test(f"بيانات احتياطية محسنة ({container})", 
                                     f"جميع الحقول موجودة، الشركة: {fallback_data.carrier}", 
                                     "✅ نجح")
                    else:
                        self.log_test(f"بيانات احتياطية محسنة ({container})", 
                                     f"حقول مفقودة: {missing_fields}", 
                                     "⚠️ ناقص")
                    
                    # التحقق من دقة تحديد الشركة
                    if expected_carrier in fallback_data.carrier:
                        self.log_test(f"دقة تحديد الشركة ({container})", 
                                     f"متوقع: {expected_carrier}, فعلي: {fallback_data.carrier}", 
                                     "✅ نجح")
                    else:
                        self.log_test(f"دقة تحديد الشركة ({container})", 
                                     f"متوقع: {expected_carrier}, فعلي: {fallback_data.carrier}", 
                                     "❌ خطأ")
            
            if enhanced_data_count >= 2:
                self.log_test("البيانات الاحتياطية المحسنة", f"نجح في {enhanced_data_count} من {len(test_cases)}", "✅ نجح")
                return True
            else:
                self.log_test("البيانات الاحتياطية المحسنة", f"نجح في {enhanced_data_count} من {len(test_cases)}", "❌ فشل")
                return False
                
        except Exception as e:
            self.log_test("البيانات الاحتياطية المحسنة", f"خطأ: {str(e)}", "❌ فشل")
            return False
    
    async def test_confidence_scoring_accuracy(self):
        """اختبار دقة حساب درجة الثقة"""
        try:
            from src.services.web_scraping_service import WebScrapingService, ShipmentData
            
            web_service = WebScrapingService()
            
            # إنشاء بيانات تجريبية بمستويات جودة مختلفة
            
            # بيانات عالية الجودة
            high_quality_data = ShipmentData(
                container_number="COSU1234567",
                carrier="COSCO",
                vessel_name="COSCO SHIPPING PANAMA",
                status="في الطريق",
                port_of_loading="Shanghai, China",
                port_of_discharge="Jeddah, Saudi Arabia",
                shipping_method="FCL",
                shipping_type="Sea",
                final_destination="Jeddah, Saudi Arabia - Port Terminal",
                tracking_number="TRK234567",
                source="SeaRates (Real Data)"
            )
            
            # بيانات متوسطة الجودة
            medium_quality_data = ShipmentData(
                container_number="MSCU7654321",
                carrier="MSC",
                vessel_name="MSC MEDITERRANEAN",
                status="In Transit",
                source="Enhanced Data"
            )
            
            # بيانات منخفضة الجودة
            low_quality_data = ShipmentData(
                container_number="TEST123456",
                source="Demo Data"
            )
            
            # حساب درجات الثقة
            high_score = web_service.calculate_confidence_score(high_quality_data)
            medium_score = web_service.calculate_confidence_score(medium_quality_data)
            low_score = web_service.calculate_confidence_score(low_quality_data)
            
            self.log_test("درجة الثقة - جودة عالية", f"{high_score:.1f}%", "✅ نجح" if high_score >= 80 else "⚠️ متوسط")
            self.log_test("درجة الثقة - جودة متوسطة", f"{medium_score:.1f}%", "✅ نجح" if 40 <= medium_score < 80 else "⚠️ متوسط")
            self.log_test("درجة الثقة - جودة منخفضة", f"{low_score:.1f}%", "✅ نجح" if low_score < 40 else "⚠️ متوسط")
            
            # التحقق من التدرج الصحيح
            if high_score > medium_score > low_score:
                self.log_test("تدرج درجات الثقة", f"عالية: {high_score:.1f}% > متوسطة: {medium_score:.1f}% > منخفضة: {low_score:.1f}%", "✅ نجح")
                return True
            else:
                self.log_test("تدرج درجات الثقة", "التدرج غير صحيح", "❌ فشل")
                return False
                
        except Exception as e:
            self.log_test("حساب درجة الثقة", f"خطأ: {str(e)}", "❌ فشل")
            return False
    
    async def test_status_translation_integration(self):
        """اختبار تكامل ترجمة الحالات مع البحث المحسن"""
        try:
            from src.services.web_scraping_service import WebScrapingService
            
            web_service = WebScrapingService()
            
            # اختبار ترجمة الحالات في البيانات المحسنة
            test_container = "COSU1234567"
            results = await web_service.search_all_carriers(container_number=test_container)
            
            translated_statuses = 0
            total_statuses = 0
            
            for result in results:
                if hasattr(result, 'status') and result.status:
                    total_statuses += 1
                    # التحقق من أن الحالة مترجمة (تحتوي على أحرف عربية)
                    if any(char in result.status for char in 'أبتثجحخدذرزسشصضطظعغفقكلمنهوي'):
                        translated_statuses += 1
            
            if total_statuses > 0:
                translation_rate = (translated_statuses / total_statuses) * 100
                self.log_test("ترجمة الحالات في البحث", 
                             f"{translated_statuses}/{total_statuses} مترجمة ({translation_rate:.1f}%)", 
                             "✅ نجح" if translation_rate >= 80 else "⚠️ متوسط")
                return translation_rate >= 50
            else:
                self.log_test("ترجمة الحالات في البحث", "لا توجد حالات للاختبار", "⚠️ متوسط")
                return False
                
        except Exception as e:
            self.log_test("ترجمة الحالات في البحث", f"خطأ: {str(e)}", "❌ فشل")
            return False
    
    async def run_all_tests(self):
        """تشغيل جميع اختبارات دقة البحث المحسن"""
        print("🔍 بدء اختبار دقة البحث المحسن للتعبئة التلقائية")
        print("=" * 70)
        
        tests = [
            ("البحث في مصادر متعددة", self.test_multiple_sources_search),
            ("تقييم جودة البيانات", self.test_data_quality_assessment),
            ("البيانات الاحتياطية المحسنة", self.test_enhanced_fallback_data),
            ("دقة حساب درجة الثقة", self.test_confidence_scoring_accuracy),
            ("تكامل ترجمة الحالات", self.test_status_translation_integration)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            print(f"\n🧪 {test_name}:")
            print("-" * 60)
            
            try:
                if await test_function():
                    passed_tests += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {str(e)}")
        
        # النتائج النهائية
        print("\n" + "=" * 70)
        print("📊 نتائج اختبار دقة البحث المحسن:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("✅ جميع تحسينات دقة البحث تعمل بشكل مثالي!")
            print("🎯 النظام المحسن جاهز للاستخدام الإنتاجي")
        elif passed_tests >= total_tests * 0.8:
            print("⚠️ معظم التحسينات تعمل بشكل جيد")
            print("🔧 بعض التحسينات تحتاج مراجعة طفيفة")
        else:
            print("❌ النظام يحتاج تحسينات إضافية")
        
        return passed_tests, total_tests

async def main():
    """الدالة الرئيسية"""
    try:
        tester = EnhancedSearchTester()
        passed, total = await tester.run_all_tests()
        
        print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
        
        if passed >= total * 0.8:
            print("\n🎉 تم تطوير دقة البحث بنجاح!")
            print("📋 التحسينات المطبقة:")
            print("• 🔍 بحث في مصادر متعددة وموثوقة")
            print("• 📊 تقييم جودة البيانات المستخرجة")
            print("• 🎯 حساب درجة الثقة المحسن")
            print("• 🌐 بيانات احتياطية واقعية")
            print("• 🔄 ترجمة وتوحيد الحالات")
            print("• ⚡ تحسين سرعة ودقة النتائج")
        
        return passed >= total * 0.8
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
