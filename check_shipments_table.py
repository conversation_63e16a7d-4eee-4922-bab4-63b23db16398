#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_shipments_table():
    """فحص بنية جدول الشحنات"""
    try:
        conn = sqlite3.connect("data/proshipment.db")
        cursor = conn.cursor()
        
        # عرض بنية جدول الشحنات
        cursor.execute("PRAGMA table_info(shipments);")
        columns = cursor.fetchall()
        
        print("أعمدة جدول الشحنات:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # عرض أول شحنة
        cursor.execute("SELECT * FROM shipments WHERE is_active = 1 LIMIT 1;")
        first_shipment = cursor.fetchone()
        
        if first_shipment:
            print(f"\nأول شحنة: {first_shipment}")
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_shipments_table()
