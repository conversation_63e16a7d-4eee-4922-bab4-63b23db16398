#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحديث كميات طلبات الشراء عند الشحن
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_purchase_order_quantity_system():
    """اختبار نظام تحديث كميات طلبات الشراء"""
    try:
        from src.database.models import PurchaseOrder, PurchaseOrderItem, Shipment, ShipmentItem, Item, Supplier
        from src.database.database_manager import DatabaseManager
        from datetime import datetime
        
        print("🧪 اختبار نظام تحديث كميات طلبات الشراء")
        print("=" * 60)
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # 1. البحث عن مورد موجود أو إنشاء واحد جديد
        test_supplier = session.query(Supplier).first()
        if not test_supplier:
            test_supplier = Supplier(
                name="مورد تجريبي للاختبار",
                code="TEST_SUPPLIER_001",
                contact_person="أحمد محمد",
                phone="123456789"
            )
            session.add(test_supplier)
            session.flush()

        # 2. البحث عن صنف موجود أو إنشاء واحد جديد
        test_item = session.query(Item).first()
        if not test_item:
            test_item = Item(
                code="TEST_ITEM_001",
                name="صنف تجريبي للاختبار",
                unit="قطعة",
                weight=1.5
            )
            session.add(test_item)
            session.flush()
        
        # 3. إنشاء طلب شراء تجريبي
        test_order = PurchaseOrder(
            order_number="PO-TEST-001",
            order_date=datetime.now(),
            supplier_id=test_supplier.id,
            order_status="مؤكد",
            notes="طلب شراء تجريبي للاختبار"
        )
        session.add(test_order)
        session.flush()
        
        # 4. إنشاء صنف طلب شراء
        test_order_item = PurchaseOrderItem(
            purchase_order_id=test_order.id,
            item_id=test_item.id,
            quantity=100.0,  # الكمية المطلوبة
            unit_price=10.0,
            total_price=1000.0,
            delivered_quantity=0.0,  # لم يتم التسليم بعد
            remaining_quantity=100.0  # الكمية المتبقية
        )
        session.add(test_order_item)
        session.flush()
        
        print(f"✅ تم إنشاء البيانات التجريبية:")
        print(f"   - المورد: {test_supplier.name}")
        print(f"   - الصنف: {test_item.name}")
        print(f"   - طلب الشراء: {test_order.order_number}")
        print(f"   - الكمية المطلوبة: {test_order_item.quantity}")
        print(f"   - الكمية المسلمة: {test_order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية: {test_order_item.remaining_quantity}")
        
        # 5. إنشاء شحنة تجريبية
        test_shipment = Shipment(
            shipment_number="SH-TEST-001",
            shipment_date=datetime.now(),
            supplier_id=test_supplier.id,
            shipment_status="قيد التحضير",
            notes="شحنة تجريبية للاختبار"
        )
        session.add(test_shipment)
        session.flush()
        
        # 6. إنشاء صنف شحنة مرتبط بطلب الشراء
        shipped_quantity = 30.0  # شحن 30 قطعة من أصل 100
        
        test_shipment_item = ShipmentItem(
            shipment_id=test_shipment.id,
            item_id=test_item.id,
            purchase_order_item_id=test_order_item.id,  # الربط مع طلب الشراء
            quantity=shipped_quantity,
            unit_price=10.0,
            total_price=300.0
        )
        session.add(test_shipment_item)
        
        # 7. تحديث كميات طلب الشراء (محاكاة ما يحدث في النظام)
        test_order_item.delivered_quantity += shipped_quantity
        test_order_item.remaining_quantity = test_order_item.quantity - test_order_item.delivered_quantity
        
        session.commit()
        
        print(f"\n✅ تم إنشاء الشحنة وتحديث الكميات:")
        print(f"   - الشحنة: {test_shipment.shipment_number}")
        print(f"   - الكمية المشحونة: {shipped_quantity}")
        print(f"   - الكمية المسلمة الجديدة: {test_order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية الجديدة: {test_order_item.remaining_quantity}")
        
        # 8. اختبار شحنة ثانية
        print(f"\n🚚 اختبار شحنة ثانية...")
        
        test_shipment2 = Shipment(
            shipment_number="SH-TEST-002",
            shipment_date=datetime.now(),
            supplier_id=test_supplier.id,
            shipment_status="قيد التحضير",
            notes="شحنة تجريبية ثانية"
        )
        session.add(test_shipment2)
        session.flush()
        
        shipped_quantity2 = 50.0  # شحن 50 قطعة إضافية
        
        test_shipment_item2 = ShipmentItem(
            shipment_id=test_shipment2.id,
            item_id=test_item.id,
            purchase_order_item_id=test_order_item.id,
            quantity=shipped_quantity2,
            unit_price=10.0,
            total_price=500.0
        )
        session.add(test_shipment_item2)
        
        # تحديث الكميات مرة أخرى
        test_order_item.delivered_quantity += shipped_quantity2
        test_order_item.remaining_quantity = test_order_item.quantity - test_order_item.delivered_quantity
        
        session.commit()
        
        print(f"✅ تم إنشاء الشحنة الثانية:")
        print(f"   - الشحنة: {test_shipment2.shipment_number}")
        print(f"   - الكمية المشحونة: {shipped_quantity2}")
        print(f"   - الكمية المسلمة الإجمالية: {test_order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية النهائية: {test_order_item.remaining_quantity}")
        
        # 9. التحقق من النتائج النهائية
        print(f"\n📊 النتائج النهائية:")
        print(f"   - الكمية المطلوبة الأصلية: {test_order_item.quantity}")
        print(f"   - إجمالي الكمية المسلمة: {test_order_item.delivered_quantity}")
        print(f"   - الكمية المتبقية: {test_order_item.remaining_quantity}")
        print(f"   - نسبة التسليم: {(test_order_item.delivered_quantity / test_order_item.quantity) * 100:.1f}%")
        
        # التحقق من صحة الحسابات
        expected_remaining = test_order_item.quantity - test_order_item.delivered_quantity
        if abs(test_order_item.remaining_quantity - expected_remaining) < 0.01:
            print("✅ الحسابات صحيحة!")
        else:
            print("❌ خطأ في الحسابات!")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_database_schema():
    """اختبار مخطط قاعدة البيانات"""
    try:
        from src.database.database_manager import DatabaseManager
        from sqlalchemy import text
        
        print("\n🔍 اختبار مخطط قاعدة البيانات...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        with session.bind.connect() as connection:
            # فحص جدول shipment_items
            result = connection.execute(text("PRAGMA table_info(shipment_items)"))
            columns = [row[1] for row in result.fetchall()]
            
            required_columns = ['purchase_order_item_id']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                print(f"❌ أعمدة مفقودة في shipment_items: {missing_columns}")
                return False
            else:
                print("✅ جميع الأعمدة المطلوبة موجودة في shipment_items")
                
            # فحص جدول purchase_order_items
            result = connection.execute(text("PRAGMA table_info(purchase_order_items)"))
            columns = [row[1] for row in result.fetchall()]
            
            required_columns = ['delivered_quantity', 'remaining_quantity']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                print(f"❌ أعمدة مفقودة في purchase_order_items: {missing_columns}")
                return False
            else:
                print("✅ جميع الأعمدة المطلوبة موجودة في purchase_order_items")
                
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المخطط: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار نظام تحديث كميات طلبات الشراء")
    print("=" * 60)
    
    # اختبار المخطط
    schema_ok = test_database_schema()
    
    if schema_ok:
        # اختبار النظام
        system_ok = test_purchase_order_quantity_system()
        
        if system_ok:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ النظام جاهز لتحديث كميات طلبات الشراء عند الشحن")
        else:
            print("\n❌ فشل في اختبار النظام!")
    else:
        print("\n❌ فشل في اختبار المخطط!")
        
    print("\n" + "=" * 60)
