#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إضافة حقول التاريخ لجدول أصناف الشحنة
يضيف حقول production_date و expiry_date لجدول shipment_items
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_shipment_item_date_fields():
    """إضافة حقول التاريخ لجدول أصناف الشحنة"""
    try:
        from src.database.database_manager import DatabaseManager
        from sqlalchemy import text
        
        print("🔧 بدء إضافة حقول التاريخ لجدول أصناف الشحنة...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()

        with session.bind.connect() as connection:
            # فحص الأعمدة الموجودة
            result = connection.execute(text("PRAGMA table_info(shipment_items)"))
            existing_columns = [row[1] for row in result.fetchall()]
            
            print(f"📋 الأعمدة الموجودة: {existing_columns}")
            
            # إضافة حقل تاريخ الإنتاج
            if 'production_date' not in existing_columns:
                try:
                    alter_sql = """
                    ALTER TABLE shipment_items 
                    ADD COLUMN production_date DATETIME
                    """
                    
                    connection.execute(text(alter_sql))
                    connection.commit()
                    print("✅ تم إضافة حقل تاريخ الإنتاج بنجاح!")
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة حقل تاريخ الإنتاج: {str(e)}")
                    return False
                    
            else:
                print("ℹ️ حقل تاريخ الإنتاج موجود بالفعل")
            
            # إضافة حقل تاريخ الانتهاء
            if 'expiry_date' not in existing_columns:
                try:
                    alter_sql = """
                    ALTER TABLE shipment_items 
                    ADD COLUMN expiry_date DATETIME
                    """
                    
                    connection.execute(text(alter_sql))
                    connection.commit()
                    print("✅ تم إضافة حقل تاريخ الانتهاء بنجاح!")
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة حقل تاريخ الانتهاء: {str(e)}")
                    return False
                    
            else:
                print("ℹ️ حقل تاريخ الانتهاء موجود بالفعل")
                
        print("🎉 تم إضافة حقول التاريخ بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def test_shipment_item_date_fields():
    """اختبار حقول التاريخ في أصناف الشحنة"""
    try:
        from src.database.models import ShipmentItem
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        print("\n🧪 اختبار حقول التاريخ في أصناف الشحنة...")
        
        # البحث عن صنف شحنة موجود
        shipment_item = session.query(ShipmentItem).first()
        if shipment_item:
            print(f"📦 اختبار صنف الشحنة ID: {shipment_item.id}")
            
            # اختبار الوصول لحقول التاريخ
            production_date = getattr(shipment_item, 'production_date', 'غير موجود')
            expiry_date = getattr(shipment_item, 'expiry_date', 'غير موجود')
            
            print(f"   - تاريخ الإنتاج: {production_date}")
            print(f"   - تاريخ الانتهاء: {expiry_date}")
            
            print("✅ حقول التاريخ متاحة!")
        else:
            print("ℹ️ لا توجد أصناف شحنة للاختبار")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 سكريبت إضافة حقول التاريخ لأصناف الشحنة")
    print("=" * 60)
    
    # إضافة الحقول
    if add_shipment_item_date_fields():
        # اختبار الحقول
        test_shipment_item_date_fields()
        print("\n🎯 تم الانتهاء بنجاح!")
    else:
        print("\n❌ فشل في إضافة الحقول!")
        sys.exit(1)
