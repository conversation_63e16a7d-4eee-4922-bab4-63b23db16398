#!/usr/bin/env python3
"""
اختبار بسيط لزر طلب جديد
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("بدء الاختبار...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        window = PurchaseOrdersWindow()
        
        print("تم إنشاء النافذة")
        
        # اختبار دالة new_order
        if hasattr(window, 'new_order'):
            print("دالة new_order موجودة")
            try:
                window.new_order()
                print("تم تنفيذ new_order بنجاح")
            except Exception as e:
                print(f"خطأ في new_order: {e}")
        else:
            print("دالة new_order غير موجودة")
        
        print("انتهى الاختبار")
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
