#!/usr/bin/env python3
"""
اختبار فتح نافذة طلبات الشراء
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print('🔍 اختبار فتح نافذة طلبات الشراء...')
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QFont
        
        app = QApplication(sys.argv)
        font = QFont('Segoe UI', 10)
        app.setFont(font)
        
        print('✅ تم إنشاء التطبيق')
        
        # استيراد النافذة
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        print('✅ تم استيراد PurchaseOrdersWindow')
        
        # إنشاء النافذة
        window = PurchaseOrdersWindow()
        print('✅ تم إنشاء النافذة')
        
        # عرض النافذة
        window.show()
        print('✅ تم عرض النافذة')
        
        print('🎉 النافذة جاهزة!')
        print('📊 معلومات النافذة:')
        print(f'   - العنوان: {window.windowTitle()}')
        print(f'   - الحجم: {window.size().width()} x {window.size().height()}')
        print(f'   - مرئية: {window.isVisible()}')
        print(f'   - الوضع: {window.mode}')
        
        # تشغيل التطبيق لمدة قصيرة للتأكد من عمل النافذة
        from PySide6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3 ثوان
        
        app.exec()
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
