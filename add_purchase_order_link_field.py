#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إضافة حقل الربط بين أصناف الشحنة وطلبات الشراء
يضيف حقل purchase_order_item_id لجدول shipment_items
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_purchase_order_link_field():
    """إضافة حقل الربط بطلبات الشراء"""
    try:
        from src.database.database_manager import DatabaseManager
        from sqlalchemy import text
        
        print("🔧 بدء إضافة حقل الربط بطلبات الشراء...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        with session.bind.connect() as connection:
            # فحص الأعمدة الموجودة
            result = connection.execute(text("PRAGMA table_info(shipment_items)"))
            existing_columns = [row[1] for row in result.fetchall()]
            
            print(f"📋 الأعمدة الموجودة: {len(existing_columns)} عمود")
            
            # إضافة حقل الربط
            if 'purchase_order_item_id' not in existing_columns:
                try:
                    alter_sql = """
                    ALTER TABLE shipment_items 
                    ADD COLUMN purchase_order_item_id INTEGER
                    """
                    
                    connection.execute(text(alter_sql))
                    connection.commit()
                    print("✅ تم إضافة حقل purchase_order_item_id بنجاح!")
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة حقل الربط: {str(e)}")
                    return False
                    
            else:
                print("ℹ️ حقل purchase_order_item_id موجود بالفعل")
                
        session.close()
        print("🎉 تم إضافة حقل الربط بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def test_purchase_order_link():
    """اختبار حقل الربط"""
    try:
        from src.database.models import ShipmentItem, PurchaseOrderItem
        from src.database.database_manager import DatabaseManager
        
        print("\n🧪 اختبار حقل الربط...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن صنف شحنة موجود
        shipment_item = session.query(ShipmentItem).first()
        if shipment_item:
            print(f"📦 اختبار صنف الشحنة ID: {shipment_item.id}")
            
            # اختبار الوصول لحقل الربط
            po_item_id = getattr(shipment_item, 'purchase_order_item_id', 'غير موجود')
            print(f"   - معرف صنف طلب الشراء: {po_item_id}")
            
            print("✅ حقل الربط متاح!")
        else:
            print("ℹ️ لا توجد أصناف شحنة للاختبار")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 سكريبت إضافة حقل الربط بطلبات الشراء")
    print("=" * 60)
    
    # إضافة الحقل
    if add_purchase_order_link_field():
        # اختبار الحقل
        test_purchase_order_link()
        print("\n🎯 تم الانتهاء بنجاح!")
    else:
        print("\n❌ فشل في إضافة الحقل!")
        sys.exit(1)
