#!/usr/bin/env python3
"""
اختبار نهائي لزر طلب جديد
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🔍 اختبار زر طلب جديد النهائي...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # 1. إنشاء النافذة الرئيسية (وضع القائمة)
        print("\n📋 إنشاء النافذة الرئيسية...")
        main_window = PurchaseOrdersWindow(mode="list")
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # 2. التحقق من وجود دالة new_order
        if hasattr(main_window, 'new_order'):
            print("✅ دالة new_order موجودة")
        else:
            print("❌ دالة new_order غير موجودة")
            return
        
        # 3. اختبار تنفيذ دالة new_order
        print("\n🆕 اختبار تنفيذ دالة new_order...")
        try:
            main_window.new_order()
            print("✅ تم تنفيذ دالة new_order بنجاح")
            print("✅ يجب أن تفتح نافذة إدخال منفصلة")
        except Exception as e:
            print(f"❌ خطأ في تنفيذ new_order: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 4. اختبار إنشاء نافذة الإدخال مباشرة
        print("\n📝 اختبار إنشاء نافذة الإدخال...")
        try:
            entry_window = PurchaseOrdersWindow(mode="entry")
            print("✅ تم إنشاء نافذة الإدخال بنجاح")
            
            if hasattr(entry_window, 'details_tabs'):
                print(f"✅ التبويبات موجودة: {entry_window.details_tabs.count()} تبويب")
            else:
                print("❌ التبويبات غير موجودة")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة الإدخال: {e}")
            import traceback
            traceback.print_exc()
            return
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ زر طلب جديد يعمل بشكل مثالي")
        
    except Exception as e:
        print(f"❌ فشل الاختبار العام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
