تحليل الأداء: اختبار_الاستيراد
وقت التنفيذ: 13.175 ثانية
تغيير الذاكرة: +40.18 MB
==================================================
         555946 function calls (544277 primitive calls) in 13.159 seconds

   Ordered by: cumulative time
   List reduced from 2681 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
    200/1    0.020    0.000   13.174   13.174 <frozen importlib._bootstrap>:1349(_find_and_load)
    457/3    0.043    0.000   13.166    4.389 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
    196/4    0.034    0.000   13.100    3.275 <frozen importlib._bootstrap>:911(_load_unlocked)
    167/4    0.007    0.000   13.095    3.274 <frozen importlib._bootstrap_external>:1020(exec_module)
    199/1    0.015    0.000   13.094   13.094 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
    365/4    0.069    0.000   13.091    3.273 {built-in method builtins.exec}
      263    0.081    0.000    7.422    0.028 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\enum.py:695(__call__)
        1    0.003    0.003    7.350    7.350 E:\project\backup\‏‏ProShipment1\src\ui\shipments\new_shipment_window.py:1(<module>)
    77/18    0.002    0.000    6.831    0.379 {built-in method builtins.__import__}
  711/239    0.017    0.000    6.503    0.027 <frozen importlib._bootstrap>:1390(_handle_fromlist)
1176/1145    0.109    0.000    4.953    0.004 {built-in method builtins.__build_class__}
        1    0.006    0.006    3.809    3.809 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\__init__.py:1(<module>)
  196/178    0.008    0.000    3.673    0.021 <frozen importlib._bootstrap>:806(module_from_spec)
    13/11    0.000    0.000    3.521    0.320 <frozen importlib._bootstrap_external>:1318(create_module)
    13/11    0.029    0.002    3.497    0.318 {built-in method _imp.create_dynamic}
      202    0.069    0.000    3.159    0.016 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\enum.py:842(_create_)
        1    0.001    0.001    3.045    3.045 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\__init__.py:1(<module>)
        1    0.000    0.000    2.622    2.622 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PySide6\__init__.py:1(<module>)
      321    0.011    0.000    2.619    0.008 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\re\__init__.py:330(_compile)
       40    0.004    0.000    2.594    0.065 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\re\_compiler.py:743(compile)


