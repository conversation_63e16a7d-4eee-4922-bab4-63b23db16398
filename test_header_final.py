#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لإصلاح رأس الجدول
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def test_final_header():
    """اختبار نهائي لرأس الجدول"""
    print("🔄 بدء الاختبار النهائي لإصلاح رأس الجدول...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فتح نافذة قائمة الطلبات
        window.show_orders_list()
        orders_window = window.orders_list_window
        
        if orders_window and hasattr(orders_window, 'orders_table'):
            table = orders_window.orders_table
            header = table.horizontalHeader()
            
            print(f"✅ عدد الأعمدة: {table.columnCount()}")
            print(f"✅ ارتفاع الرأس: {header.height()}")
            
            # فحص تسميات الأعمدة بعد الإصلاح
            print("📋 تسميات الأعمدة:")
            for i in range(table.columnCount()):
                item = table.horizontalHeaderItem(i)
                if item:
                    print(f"  ✅ العمود {i+1}: '{item.text()}'")
                else:
                    print(f"  ❌ العمود {i+1}: لا يوجد تسمية")
            
            # فحص التنسيق
            style = table.styleSheet()
            if "QHeaderView::section" in style:
                print("✅ تم تطبيق تنسيق الرأس")
            else:
                print("❌ لم يتم تطبيق تنسيق الرأس")
            
            print("🎉 اكتمل الاختبار النهائي - يجب أن تظهر عناوين الأعمدة الآن!")
            return True
        else:
            print("❌ لم يتم العثور على الجدول")
            return False
            
    except Exception as e:
        print(f"❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_header()
    sys.exit(0 if success else 1)
