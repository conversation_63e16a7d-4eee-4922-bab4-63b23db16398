#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لميزة التعبئة التلقائية
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def test_auto_fill_dialog():
    """اختبار نافذة التعبئة التلقائية"""
    print("🤖 اختبار نافذة التعبئة التلقائية")
    print("=" * 50)
    
    try:
        # اختبار استيراد النافذة
        print("1️⃣ اختبار استيراد النافذة...")
        from ui.dialogs.auto_fill_dialog import AutoFillDialog, AutoFillWorker
        print("   ✅ تم استيراد النافذة بنجاح")
        
        # اختبار إنشاء عامل البحث
        print("\n2️⃣ اختبار إنشاء عامل البحث...")
        worker = AutoFillWorker("MSCU1234567", 1)
        print("   ✅ تم إنشاء عامل البحث بنجاح")
        
        # اختبار تحديد شركة الشحن
        print("\n3️⃣ اختبار تحديد شركة الشحن...")
        test_cases = [
            ("MSCU1234567", "MSC"),
            ("MSKU9876543", "MAERSK"),
            ("CMAU5555555", "CMA CGM"),
            ("COSU7777777", "COSCO"),
            ("EGLV3333333", "EVERGREEN")
        ]
        
        for container, expected in test_cases:
            result = worker.detect_carrier_from_container(container)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {container} -> {result}")
        
        # اختبار معالجة النتائج
        print("\n4️⃣ اختبار معالجة النتائج...")
        sample_results = {
            'success': True,
            'carrier': 'MSC',
            'container_number': 'MSCU1234567',
            'found_data': {
                'shipping_company': 'MSC',
                'vessel_name': 'MSC OSCAR',
                'port_of_loading': 'Shanghai',
                'container_type': '40HC'
            }
        }
        
        processed = worker.process_search_results(sample_results)
        if processed['success']:
            print("   ✅ معالجة النتائج نجحت")
            print(f"   📦 بيانات الشحن: {len(processed['shipping_data'])} حقل")
            print(f"   📋 بيانات الحاوية: {len(processed['container_data'])} حقل")
        else:
            print("   ❌ معالجة النتائج فشلت")
        
        print("\n" + "=" * 50)
        print("🎉 اختبار نافذة التعبئة التلقائية نجح!")
        
        print("\n📋 الميزات المتاحة:")
        print("   ✅ تحديد شركة الشحن من رقم الحاوية")
        print("   ✅ البحث عبر الإنترنت")
        print("   ✅ معالجة وتنظيم النتائج")
        print("   ✅ واجهة مستخدم متقدمة")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_auto_fill_dialog()
    if success:
        print("\n✨ نافذة التعبئة التلقائية جاهزة!")
        print("\n🚀 للاستخدام:")
        print("   1. افتح التطبيق الرئيسي")
        print("   2. اذهب إلى نافذة إدارة الشحنات")
        print("   3. انقر بالزر الأيمن على أي شحنة")
        print("   4. اختر '🤖 تعبئة تلقائية'")
    else:
        print("\n💥 هناك مشاكل تحتاج إلى إصلاح")
