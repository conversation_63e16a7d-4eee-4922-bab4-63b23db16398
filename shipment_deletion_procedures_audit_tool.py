#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة مراجعة إجراءات حذف الشحنات
Shipment Deletion Procedures Audit Tool - Comprehensive review of shipment deletion functionality
"""

import sys
import os
import time
import threading
from datetime import datetime, date
from typing import Dict, List, Any, Tuple

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import (
        Shipment, ShipmentItem, Container, ShipmentDocument,
        Supplier, Item, Base
    )
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد النماذج: {e}")

class ShipmentDeletionProceduresAuditor:
    """مراجع إجراءات حذف الشحنات"""
    
    def __init__(self, db_manager=None):
        """تهيئة مراجع إجراءات الحذف"""
        self.db_manager = db_manager or DatabaseManager()
        self.audit_log = []
        self.issues_found = []
        self.score = 0
        self.max_score = 100
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج المراجعة"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status == "ERROR":
            self.issues_found.append(f"{category}: {message}")
    
    def audit_deletion_methods(self) -> int:
        """مراجعة طرق الحذف المختلفة"""
        self.log_audit("طرق الحذف", "بدء مراجعة طرق الحذف المختلفة")
        score = 0
        
        try:
            # فحص ملف new_shipment_window.py للحذف الفعلي
            window_file = "src/ui/shipments/new_shipment_window.py"
            if os.path.exists(window_file):
                with open(window_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود دالة delete_shipment
                if "def delete_shipment(self):" in content:
                    self.log_audit("طرق الحذف", "✅ دالة delete_shipment موجودة في نافذة الشحنة")
                    score += 10
                else:
                    self.log_audit("طرق الحذف", "❌ دالة delete_shipment غير موجودة", "ERROR")
                
                # فحص تأكيد الحذف
                if "QMessageBox.question" in content and "تأكيد الحذف" in content:
                    self.log_audit("طرق الحذف", "✅ تأكيد الحذف موجود")
                    score += 10
                else:
                    self.log_audit("طرق الحذف", "❌ تأكيد الحذف غير موجود", "ERROR")
                
                # فحص الحذف التتابعي للبيانات المرتبطة
                cascade_checks = [
                    "ShipmentItem",
                    "Container", 
                    "ShipmentDocument"
                ]
                
                cascade_found = 0
                for check in cascade_checks:
                    if f"session.query({check})" in content and "delete()" in content:
                        cascade_found += 1
                
                if cascade_found >= 3:
                    self.log_audit("طرق الحذف", "✅ الحذف التتابعي للبيانات المرتبطة موجود")
                    score += 15
                elif cascade_found > 0:
                    self.log_audit("طرق الحذف", f"⚠️ الحذف التتابعي جزئي ({cascade_found}/3)", "WARNING")
                    score += 5
                else:
                    self.log_audit("طرق الحذف", "❌ الحذف التتابعي غير موجود", "ERROR")
            
            # فحص ملف shipments_window.py للحذف المنطقي
            shipments_file = "src/ui/shipments/shipments_window.py"
            if os.path.exists(shipments_file):
                with open(shipments_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص الحذف المنطقي
                if "is_active = False" in content:
                    self.log_audit("طرق الحذف", "✅ الحذف المنطقي موجود")
                    score += 10
                else:
                    self.log_audit("طرق الحذف", "❌ الحذف المنطقي غير موجود", "ERROR")
                
                # فحص معالجة الأخطاء
                if "except Exception as e:" in content and "rollback" in content:
                    self.log_audit("طرق الحذف", "✅ معالجة الأخطاء موجودة")
                    score += 5
                else:
                    self.log_audit("طرق الحذف", "❌ معالجة الأخطاء غير كافية", "ERROR")
        
        except Exception as e:
            self.log_audit("طرق الحذف", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("طرق الحذف", f"نتيجة مراجعة طرق الحذف: {score}/50")
        return score
    
    def audit_data_integrity(self) -> int:
        """مراجعة سلامة البيانات عند الحذف"""
        self.log_audit("سلامة البيانات", "بدء مراجعة سلامة البيانات عند الحذف")
        score = 0
        
        try:
            # إنشاء بيانات اختبار
            session = self.db_manager.get_session()
            
            # الحصول على مورد وصنف للاختبار
            supplier = session.query(Supplier).first()
            item = session.query(Item).first()
            
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار الحذف",
                    contact_person="شخص الاتصال",
                    phone="123456789"
                )
                session.add(supplier)
                session.commit()
            
            if not item:
                item = Item(
                    code="DEL-TEST-001",
                    name="صنف اختبار الحذف",
                    unit="قطعة"
                )
                session.add(item)
                session.commit()
            
            # إنشاء شحنة تجريبية مع بيانات مرتبطة
            test_shipment = Shipment(
                shipment_number="DEL-AUDIT-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة اختبار الحذف",
                notes="شحنة اختبار لمراجعة الحذف"
            )
            session.add(test_shipment)
            session.flush()
            
            # إضافة أصناف
            test_items = []
            for i in range(3):
                test_item = ShipmentItem(
                    shipment_id=test_shipment.id,
                    item_id=item.id,
                    quantity=float(i + 1),
                    unit_price=100.0,
                    notes=f"صنف اختبار {i + 1}"
                )
                test_items.append(test_item)
                session.add(test_item)
            
            # إضافة حاويات
            test_containers = []
            for i in range(2):
                test_container = Container(
                    shipment_id=test_shipment.id,
                    container_number=f"DEL-CONT-{i+1:03d}",
                    container_type="عادية",
                    container_size="20 قدم",
                    status="فارغة"
                )
                test_containers.append(test_container)
                session.add(test_container)
            
            session.commit()
            
            self.log_audit("سلامة البيانات", f"✅ تم إنشاء شحنة تجريبية رقم {test_shipment.id} مع 3 أصناف و 2 حاوية")
            score += 15
            
            # اختبار الحذف التتابعي
            shipment_id = test_shipment.id
            
            # حذف الأصناف أولاً
            deleted_items = session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == shipment_id
            ).delete()
            
            # حذف الحاويات
            deleted_containers = session.query(Container).filter(
                Container.shipment_id == shipment_id
            ).delete()
            
            # حذف الشحنة
            session.delete(test_shipment)
            session.commit()
            
            if deleted_items == 3 and deleted_containers == 2:
                self.log_audit("سلامة البيانات", "✅ الحذف التتابعي يعمل بشكل صحيح")
                score += 20
            else:
                self.log_audit("سلامة البيانات", f"❌ مشكلة في الحذف التتابعي: أصناف={deleted_items}, حاويات={deleted_containers}", "ERROR")
            
            # التحقق من عدم وجود بيانات متبقية
            remaining_items = session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == shipment_id
            ).count()
            
            remaining_containers = session.query(Container).filter(
                Container.shipment_id == shipment_id
            ).count()
            
            remaining_shipment = session.query(Shipment).filter(
                Shipment.id == shipment_id
            ).count()
            
            if remaining_items == 0 and remaining_containers == 0 and remaining_shipment == 0:
                self.log_audit("سلامة البيانات", "✅ لا توجد بيانات متبقية بعد الحذف")
                score += 15
            else:
                self.log_audit("سلامة البيانات", f"❌ بيانات متبقية: شحنات={remaining_shipment}, أصناف={remaining_items}, حاويات={remaining_containers}", "ERROR")
            
            session.close()
            
        except Exception as e:
            self.log_audit("سلامة البيانات", f"خطأ في اختبار سلامة البيانات: {str(e)}", "ERROR")
        
        self.log_audit("سلامة البيانات", f"نتيجة مراجعة سلامة البيانات: {score}/50")
        return score
    
    def audit_soft_vs_hard_delete(self) -> int:
        """مراجعة الحذف المنطقي مقابل الحذف الفعلي"""
        self.log_audit("أنواع الحذف", "بدء مراجعة أنواع الحذف (منطقي/فعلي)")
        score = 0
        
        try:
            # فحص نموذج Shipment للحقول المطلوبة للحذف المنطقي
            models_file = "src/database/models.py"
            if os.path.exists(models_file):
                with open(models_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود حقول الحذف المنطقي
                soft_delete_fields = ["is_active", "is_deleted", "deleted_at"]
                found_fields = []
                
                for field in soft_delete_fields:
                    if field in content:
                        found_fields.append(field)
                
                if len(found_fields) >= 1:
                    self.log_audit("أنواع الحذف", f"✅ حقول الحذف المنطقي موجودة: {', '.join(found_fields)}")
                    score += 15
                else:
                    self.log_audit("أنواع الحذف", "❌ حقول الحذف المنطقي غير موجودة", "ERROR")
            
            # اختبار الحذف المنطقي عملياً
            session = self.db_manager.get_session()
            
            # إنشاء شحنة للاختبار
            supplier = session.query(Supplier).first()
            if supplier:
                test_shipment = Shipment(
                    shipment_number="SOFT-DEL-TEST-001",
                    shipment_date=date.today(),
                    supplier_id=supplier.id,
                    shipment_status="تحت الطلب"
                )
                session.add(test_shipment)
                session.commit()
                
                # محاولة الحذف المنطقي
                try:
                    if hasattr(test_shipment, 'is_active'):
                        setattr(test_shipment, 'is_active', False)
                        session.commit()
                        self.log_audit("أنواع الحذف", "✅ الحذف المنطقي باستخدام is_active يعمل")
                        score += 10
                    elif hasattr(test_shipment, 'is_deleted'):
                        setattr(test_shipment, 'is_deleted', True)
                        if hasattr(test_shipment, 'deleted_at'):
                            setattr(test_shipment, 'deleted_at', datetime.now())
                        session.commit()
                        self.log_audit("أنواع الحذف", "✅ الحذف المنطقي باستخدام is_deleted يعمل")
                        score += 10
                    else:
                        self.log_audit("أنواع الحذف", "❌ لا يمكن تطبيق الحذف المنطقي", "ERROR")
                    
                    # تنظيف البيانات التجريبية
                    session.delete(test_shipment)
                    session.commit()
                    
                except Exception as e:
                    self.log_audit("أنواع الحذف", f"خطأ في اختبار الحذف المنطقي: {str(e)}", "ERROR")
                    session.rollback()
            
            session.close()
            
        except Exception as e:
            self.log_audit("أنواع الحذف", f"خطأ في مراجعة أنواع الحذف: {str(e)}", "ERROR")
        
        self.log_audit("أنواع الحذف", f"نتيجة مراجعة أنواع الحذف: {score}/25")
        return score
    
    def audit_ui_integration(self) -> int:
        """مراجعة تكامل واجهة المستخدم مع الحذف"""
        self.log_audit("تكامل الواجهة", "بدء مراجعة تكامل واجهة المستخدم مع الحذف")
        score = 0
        
        try:
            # فحص ملف shipments_window.py
            shipments_file = "src/ui/shipments/shipments_window.py"
            if os.path.exists(shipments_file):
                with open(shipments_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود دالة حذف في قائمة الزر الأيمن
                if "delete_shipment_from_context_menu" in content:
                    self.log_audit("تكامل الواجهة", "✅ دالة الحذف موجودة في قائمة الزر الأيمن")
                    score += 10
                else:
                    self.log_audit("تكامل الواجهة", "❌ دالة الحذف غير موجودة في قائمة الزر الأيمن", "ERROR")
                
                # فحص ربط الإشارات
                if "shipment_deleted.connect" in content:
                    self.log_audit("تكامل الواجهة", "✅ إشارة shipment_deleted مربوطة")
                    score += 5
                else:
                    self.log_audit("تكامل الواجهة", "❌ إشارة shipment_deleted غير مربوطة", "ERROR")
                
                # فحص تحديث القائمة بعد الحذف
                if "load_shipments" in content:
                    self.log_audit("تكامل الواجهة", "✅ تحديث القائمة بعد الحذف موجود")
                    score += 5
                else:
                    self.log_audit("تكامل الواجهة", "❌ تحديث القائمة بعد الحذف غير موجود", "ERROR")
            
            # فحص ملف new_shipment_window.py
            window_file = "src/ui/shipments/new_shipment_window.py"
            if os.path.exists(window_file):
                with open(window_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود زر الحذف
                if "delete_button" in content:
                    self.log_audit("تكامل الواجهة", "✅ زر الحذف موجود في نافذة الشحنة")
                    score += 5
                else:
                    self.log_audit("تكامل الواجهة", "❌ زر الحذف غير موجود في نافذة الشحنة", "ERROR")
                
                # فحص إرسال الإشارة بعد الحذف
                if "shipment_deleted.emit" in content:
                    self.log_audit("تكامل الواجهة", "✅ إرسال إشارة الحذف موجود")
                    score += 5
                else:
                    self.log_audit("تكامل الواجهة", "❌ إرسال إشارة الحذف غير موجود", "ERROR")
        
        except Exception as e:
            self.log_audit("تكامل الواجهة", f"خطأ في مراجعة تكامل الواجهة: {str(e)}", "ERROR")
        
        self.log_audit("تكامل الواجهة", f"نتيجة مراجعة تكامل الواجهة: {score}/30")
        return score
    
    def run_comprehensive_audit(self) -> Dict:
        """تشغيل المراجعة الشاملة لإجراءات حذف الشحنات"""
        self.log_audit("مراجعة شاملة", "بدء المراجعة الشاملة لإجراءات حذف الشحنات")
        
        # تشغيل جميع المراجعات
        deletion_methods_score = self.audit_deletion_methods()
        data_integrity_score = self.audit_data_integrity()
        soft_hard_delete_score = self.audit_soft_vs_hard_delete()
        ui_integration_score = self.audit_ui_integration()
        
        # حساب النتيجة الإجمالية
        total_score = deletion_methods_score + data_integrity_score + soft_hard_delete_score + ui_integration_score
        max_possible = 155  # 50 + 50 + 25 + 30
        percentage = (total_score / max_possible) * 100
        
        # تحديد التقييم
        if percentage >= 90:
            grade = "ممتاز"
        elif percentage >= 80:
            grade = "جيد جداً"
        elif percentage >= 70:
            grade = "جيد"
        elif percentage >= 60:
            grade = "مقبول"
        else:
            grade = "يحتاج تحسين"
        
        self.log_audit("مراجعة شاملة", f"النتيجة الإجمالية: {total_score}/{max_possible} ({percentage:.1f}%) - {grade}")
        
        return {
            'total_score': total_score,
            'max_score': max_possible,
            'percentage': percentage,
            'grade': grade,
            'detailed_scores': {
                'deletion_methods': deletion_methods_score,
                'data_integrity': data_integrity_score,
                'soft_hard_delete': soft_hard_delete_score,
                'ui_integration': ui_integration_score
            },
            'issues_found': self.issues_found,
            'audit_log': self.audit_log
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("🗑️ أداة مراجعة إجراءات حذف الشحنات")
        print("=" * 50)
        
        # إنشاء مراجع إجراءات الحذف
        auditor = ShipmentDeletionProceduresAuditor()
        
        # تشغيل المراجعة الشاملة
        results = auditor.run_comprehensive_audit()
        
        print("\n" + "=" * 50)
        print("📊 تقرير المراجعة الشاملة:")
        print(f"• النتيجة الإجمالية: {results['total_score']}/{results['max_score']}")
        print(f"• النسبة المئوية: {results['percentage']:.1f}%")
        print(f"• التقييم: {results['grade']}")
        
        print("\n📋 النتائج التفصيلية:")
        for category, score in results['detailed_scores'].items():
            print(f"• {category}: {score}")
        
        if results['issues_found']:
            print(f"\n⚠️ المشاكل المكتشفة ({len(results['issues_found'])}):")
            for issue in results['issues_found']:
                print(f"  - {issue}")
        else:
            print("\n✅ لم يتم اكتشاف مشاكل")
        
        print("\n" + "=" * 50)
        if results['percentage'] >= 80:
            print("✅ إجراءات حذف الشحنات في حالة جيدة!")
        else:
            print("⚠️ إجراءات حذف الشحنات تحتاج إلى تحسين")
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في مراجعة إجراءات الحذف: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
