# -*- coding: utf-8 -*-
"""
نافذة تفاصيل حوالات الموردين
Supplier Remittance Details Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox,
                               QTextEdit, QAbstractItemView, QDoubleSpinBox, QFrame)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import SupplierRemittance, SupplierRemittanceDetail, Supplier


class SupplierRemittanceDetailsDialog(QDialog):
    """نافذة تفاصيل حوالات الموردين"""
    
    def __init__(self, parent=None, remittance_id=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.remittance_id = remittance_id
        self.supplier_details = []
        
        self.setWindowTitle("تفاصيل حوالة الموردين")
        self.setModal(True)
        self.resize(800, 600)
        
        self.setup_ui()
        self.load_suppliers()
        
        if self.remittance_id:
            self.load_remittance_details()
            self.setWindowTitle("عرض تفاصيل الحوالة")
            # تعطيل التعديل للحوالات المؤكدة
            self.check_edit_permissions()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # العنوان
        title_label = QLabel("تفاصيل توزيع الحوالة على الموردين")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # قسم إضافة مورد جديد
        add_group = QGroupBox("إضافة مورد للحوالة")
        add_layout = QFormLayout(add_group)

        # اختيار المورد
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumHeight(30)
        add_layout.addRow("المورد:", self.supplier_combo)

        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0.01, 999999999)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" ريال")
        self.amount_spin.setMinimumHeight(30)
        add_layout.addRow("المبلغ:", self.amount_spin)

        # الغرض من الدفع
        self.purpose_edit = QLineEdit()
        self.purpose_edit.setPlaceholderText("مثل: دفع فاتورة، دفعة مقدمة، تسوية حساب...")
        self.purpose_edit.setMinimumHeight(30)
        add_layout.addRow("الغرض من الدفع:", self.purpose_edit)

        # أرقام الفواتير
        self.invoices_edit = QLineEdit()
        self.invoices_edit.setPlaceholderText("أرقام الفواتير مفصولة بفاصلة (اختياري)")
        self.invoices_edit.setMinimumHeight(30)
        add_layout.addRow("أرقام الفواتير:", self.invoices_edit)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        add_layout.addRow("الملاحظات:", self.notes_edit)

        # زر الإضافة
        add_button_layout = QHBoxLayout()
        self.add_supplier_button = QPushButton("إضافة المورد")
        self.add_supplier_button.clicked.connect(self.add_supplier_detail)
        self.add_supplier_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        add_button_layout.addWidget(self.add_supplier_button)
        add_button_layout.addStretch()
        add_layout.addRow("", add_button_layout)

        layout.addWidget(add_group)

        # جدول الموردين المضافين
        table_group = QGroupBox("الموردين المضافين للحوالة")
        table_layout = QVBoxLayout(table_group)

        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(6)
        self.suppliers_table.setHorizontalHeaderLabels([
            "المورد", "المبلغ", "الغرض", "أرقام الفواتير", "الملاحظات", "العمليات"
        ])

        # ضبط خصائص الجدول
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # ضبط عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # الغرض
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الفواتير
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # الملاحظات
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # العمليات

        table_layout.addWidget(self.suppliers_table)

        # معلومات الإجمالي
        total_layout = QHBoxLayout()
        total_layout.addStretch()
        
        self.total_label = QLabel("الإجمالي: 0.00 ريال")
        self.total_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.total_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #f8f9fa;
                padding: 8px 16px;
                border: 2px solid #3498db;
                border-radius: 5px;
            }
        """)
        total_layout.addWidget(self.total_label)
        
        table_layout.addLayout(total_layout)
        layout.addWidget(table_group)

        # أزرار الحوار
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        if not self.remittance_id:  # نافذة إضافة جديدة
            self.ok_button = QPushButton("موافق")
            self.ok_button.clicked.connect(self.accept)
            self.ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            buttons_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("إغلاق" if self.remittance_id else "إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        buttons_layout.addWidget(self.cancel_button)

        layout.addLayout(buttons_layout)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()
            
            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر المورد --", None)
            
            for supplier in suppliers:
                display_text = f"{supplier.name} ({supplier.code})" if supplier.code else supplier.name
                self.supplier_combo.addItem(display_text, supplier.id)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()

    def add_supplier_detail(self):
        """إضافة تفاصيل مورد جديد"""
        if not self.validate_supplier_form():
            return
        
        supplier_id = self.supplier_combo.currentData()
        supplier_name = self.supplier_combo.currentText().split(" (")[0]
        amount = self.amount_spin.value()
        purpose = self.purpose_edit.text().strip()
        invoices = self.invoices_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()
        
        # التحقق من عدم تكرار المورد
        for detail in self.supplier_details:
            if detail['supplier_id'] == supplier_id:
                QMessageBox.warning(self, "تحذير", "هذا المورد مضاف بالفعل")
                return
        
        # إضافة التفاصيل
        detail = {
            'supplier_id': supplier_id,
            'supplier_name': supplier_name,
            'amount': amount,
            'purpose': purpose,
            'invoices': invoices,
            'notes': notes
        }
        
        self.supplier_details.append(detail)
        self.update_suppliers_table()
        self.clear_supplier_form()

    def validate_supplier_form(self):
        """التحقق من صحة بيانات المورد"""
        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            self.supplier_combo.setFocus()
            return False
        
        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            self.amount_spin.setFocus()
            return False
        
        if not self.purpose_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الغرض من الدفع")
            self.purpose_edit.setFocus()
            return False
        
        return True

    def clear_supplier_form(self):
        """مسح نموذج المورد"""
        self.supplier_combo.setCurrentIndex(0)
        self.amount_spin.setValue(0)
        self.purpose_edit.clear()
        self.invoices_edit.clear()
        self.notes_edit.clear()

    def update_suppliers_table(self):
        """تحديث جدول الموردين"""
        self.suppliers_table.setRowCount(len(self.supplier_details))

        total_amount = 0

        for row, detail in enumerate(self.supplier_details):
            # المورد
            supplier_item = QTableWidgetItem(detail['supplier_name'])
            self.suppliers_table.setItem(row, 0, supplier_item)

            # المبلغ
            amount_item = QTableWidgetItem(f"{detail['amount']:,.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.suppliers_table.setItem(row, 1, amount_item)
            total_amount += detail['amount']

            # الغرض
            purpose_item = QTableWidgetItem(detail['purpose'])
            self.suppliers_table.setItem(row, 2, purpose_item)

            # أرقام الفواتير
            invoices_item = QTableWidgetItem(detail['invoices'])
            self.suppliers_table.setItem(row, 3, invoices_item)

            # الملاحظات
            notes_item = QTableWidgetItem(detail['notes'])
            self.suppliers_table.setItem(row, 4, notes_item)

            # زر الحذف
            if not self.remittance_id or not self.is_posted():
                delete_button = QPushButton("حذف")
                delete_button.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 3px;
                        font-size: 11px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                delete_button.clicked.connect(lambda checked, r=row: self.remove_supplier_detail(r))
                self.suppliers_table.setCellWidget(row, 5, delete_button)

        # تحديث الإجمالي
        self.total_label.setText(f"الإجمالي: {total_amount:,.2f} ريال")

    def remove_supplier_detail(self, row):
        """حذف تفاصيل مورد"""
        if 0 <= row < len(self.supplier_details):
            supplier_name = self.supplier_details[row]['supplier_name']

            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المورد '{supplier_name}' من الحوالة؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                del self.supplier_details[row]
                self.update_suppliers_table()

    def load_remittance_details(self):
        """تحميل تفاصيل الحوالة الموجودة"""
        if not self.remittance_id:
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.remittance_id)
            if not remittance:
                return

            self.supplier_details = []

            for detail in remittance.remittance_details:
                supplier_detail = {
                    'supplier_id': detail.supplier_id,
                    'supplier_name': detail.supplier.name,
                    'amount': detail.amount,
                    'purpose': detail.payment_purpose or "",
                    'invoices': detail.invoice_numbers or "",
                    'notes': detail.notes or ""
                }
                self.supplier_details.append(supplier_detail)

            self.update_suppliers_table()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل تفاصيل الحوالة: {str(e)}")
        finally:
            session.close()

    def check_edit_permissions(self):
        """فحص صلاحيات التعديل"""
        if not self.remittance_id:
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.remittance_id)
            if remittance and remittance.status in ["confirmed", "posted"]:
                # تعطيل التعديل للحوالات المؤكدة أو المرحلة
                self.add_supplier_button.setEnabled(False)
                self.supplier_combo.setEnabled(False)
                self.amount_spin.setEnabled(False)
                self.purpose_edit.setEnabled(False)
                self.invoices_edit.setEnabled(False)
                self.notes_edit.setEnabled(False)

        except Exception as e:
            print(f"خطأ في فحص صلاحيات التعديل: {str(e)}")
        finally:
            session.close()

    def is_posted(self):
        """فحص ما إذا كانت الحوالة مرحلة"""
        if not self.remittance_id:
            return False

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.remittance_id)
            return remittance and remittance.status in ["confirmed", "posted"]
        except:
            return False
        finally:
            session.close()

    def get_supplier_details(self):
        """الحصول على تفاصيل الموردين"""
        return self.supplier_details.copy()

    def accept(self):
        """قبول الحوار"""
        if not self.supplier_details:
            QMessageBox.warning(self, "تحذير", "يجب إضافة مورد واحد على الأقل")
            return

        super().accept()
