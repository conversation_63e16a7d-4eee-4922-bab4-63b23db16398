# -*- coding: utf-8 -*-
"""
نافذة تفاصيل الحوالة الجديدة - تصميم محسن
New Remittance Details Dialog - Enhanced Design
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox,
                               QTextEdit, QAbstractItemView, QDoubleSpinBox, QFrame,
                               QScrollArea, QGridLayout, QDateEdit)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QIcon

from ...database.database_manager import DatabaseManager
# from ...models.supplier_remittance import SupplierRemittance  # سيتم إنشاؤه لاحقاً


class SupplierRemittanceDetailsDialog(QDialog):
    """نافذة تفاصيل الحوالة الجديدة - تصميم محسن"""
    
    # إشارات
    remittance_saved = Signal(dict)
    
    def __init__(self, parent=None, remittance_id=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.remittance_id = remittance_id
        
        # إعداد النافذة
        self.setWindowTitle("📋 تفاصيل الحوالة - نظام محسن")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 11)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        self.setup_ui()
        self.setup_connections()
        
        # تحميل البيانات إذا كان هناك معرف حوالة
        if self.remittance_id:
            self.load_remittance_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان النافذة
        title_label = QLabel("📋 تفاصيل الحوالة المالية")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # المحتوى الرئيسي
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        
        # قسم المعلومات الأساسية
        basic_section = self.create_basic_section()
        content_layout.addWidget(basic_section)
        
        # قسم معلومات البنوك
        banks_section = self.create_banks_section()
        content_layout.addWidget(banks_section)
        
        # قسم الرسوم والمعلومات الإضافية
        fees_section = self.create_fees_section()
        content_layout.addWidget(fees_section)
        
        # قسم الموردين المرتبطين
        suppliers_section = self.create_suppliers_section()
        content_layout.addWidget(suppliers_section)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons_section()
        main_layout.addLayout(buttons_layout)
        
    def create_basic_section(self):
        """إنشاء قسم المعلومات الأساسية"""
        group = QGroupBox("📝 المعلومات الأساسية")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #3498db;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)
        
        # رقم الحوالة
        layout.addWidget(QLabel("رقم الحوالة:"), 0, 0)
        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setReadOnly(True)
        self.remittance_number_edit.setMinimumHeight(40)
        self.remittance_number_edit.setStyleSheet(self.get_readonly_style())
        layout.addWidget(self.remittance_number_edit, 0, 1)
        
        # تاريخ الحوالة
        layout.addWidget(QLabel("تاريخ الحوالة:"), 0, 2)
        self.remittance_date_edit = QDateEdit()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.remittance_date_edit.setCalendarPopup(True)
        self.remittance_date_edit.setMinimumHeight(40)
        self.remittance_date_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.remittance_date_edit, 0, 3)
        
        # العملة
        layout.addWidget(QLabel("العملة:"), 1, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(40)
        self.currency_combo.setStyleSheet(self.get_combo_style())
        layout.addWidget(self.currency_combo, 1, 1)
        
        # سعر الصرف
        layout.addWidget(QLabel("سعر الصرف:"), 1, 2)
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.001, 999999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0)
        self.exchange_rate_spin.setMinimumHeight(40)
        self.exchange_rate_spin.setStyleSheet(self.get_input_style())
        layout.addWidget(self.exchange_rate_spin, 1, 3)
        
        # الحالة
        layout.addWidget(QLabel("الحالة:"), 2, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["مسودة", "مرسل", "مؤكد", "ملغي"])
        self.status_combo.setMinimumHeight(40)
        self.status_combo.setStyleSheet(self.get_combo_style())
        layout.addWidget(self.status_combo, 2, 1)
        
        # المبلغ الإجمالي
        layout.addWidget(QLabel("المبلغ الإجمالي:"), 2, 2)
        self.total_amount_edit = QLineEdit()
        self.total_amount_edit.setReadOnly(True)
        self.total_amount_edit.setMinimumHeight(40)
        self.total_amount_edit.setStyleSheet(self.get_readonly_style())
        layout.addWidget(self.total_amount_edit, 2, 3)
        
        return group
        
    def create_banks_section(self):
        """إنشاء قسم معلومات البنوك"""
        group = QGroupBox("🏦 معلومات البنوك")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #e74c3c;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)
        
        # البنك المرسل
        layout.addWidget(QLabel("البنك المرسل:"), 0, 0)
        self.sender_bank_edit = QLineEdit()
        self.sender_bank_edit.setPlaceholderText("اسم البنك المرسل")
        self.sender_bank_edit.setMinimumHeight(40)
        self.sender_bank_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.sender_bank_edit, 0, 1)
        
        # رقم الحساب المرسل
        layout.addWidget(QLabel("رقم الحساب المرسل:"), 0, 2)
        self.sender_account_edit = QLineEdit()
        self.sender_account_edit.setPlaceholderText("رقم الحساب المرسل")
        self.sender_account_edit.setMinimumHeight(40)
        self.sender_account_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.sender_account_edit, 0, 3)
        
        # البنك المستقبل
        layout.addWidget(QLabel("البنك المستقبل:"), 1, 0)
        self.receiver_bank_edit = QLineEdit()
        self.receiver_bank_edit.setPlaceholderText("اسم البنك المستقبل")
        self.receiver_bank_edit.setMinimumHeight(40)
        self.receiver_bank_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.receiver_bank_edit, 1, 1)
        
        # دولة البنك
        layout.addWidget(QLabel("دولة البنك:"), 1, 2)
        self.receiver_country_edit = QLineEdit()
        self.receiver_country_edit.setPlaceholderText("دولة البنك المستقبل")
        self.receiver_country_edit.setMinimumHeight(40)
        self.receiver_country_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.receiver_country_edit, 1, 3)
        
        # كود SWIFT
        layout.addWidget(QLabel("كود SWIFT:"), 2, 0)
        self.swift_code_edit = QLineEdit()
        self.swift_code_edit.setPlaceholderText("كود SWIFT للتحويلات الدولية")
        self.swift_code_edit.setMinimumHeight(40)
        self.swift_code_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.swift_code_edit, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة
        
        return group

    def create_fees_section(self):
        """إنشاء قسم الرسوم والمعلومات الإضافية"""
        group = QGroupBox("💰 الرسوم والمعلومات الإضافية")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #f39c12;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #f39c12;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)

        # رسوم التحويل
        layout.addWidget(QLabel("رسوم التحويل:"), 0, 0)
        self.transfer_fees_spin = QDoubleSpinBox()
        self.transfer_fees_spin.setRange(0, 999999)
        self.transfer_fees_spin.setDecimals(2)
        self.transfer_fees_spin.setSuffix(" ريال")
        self.transfer_fees_spin.setMinimumHeight(40)
        self.transfer_fees_spin.setStyleSheet(self.get_input_style())
        layout.addWidget(self.transfer_fees_spin, 0, 1)

        # رسوم البنك
        layout.addWidget(QLabel("رسوم البنك:"), 0, 2)
        self.bank_charges_spin = QDoubleSpinBox()
        self.bank_charges_spin.setRange(0, 999999)
        self.bank_charges_spin.setDecimals(2)
        self.bank_charges_spin.setSuffix(" ريال")
        self.bank_charges_spin.setMinimumHeight(40)
        self.bank_charges_spin.setStyleSheet(self.get_input_style())
        layout.addWidget(self.bank_charges_spin, 0, 3)

        # الغرض من التحويل
        layout.addWidget(QLabel("الغرض من التحويل:"), 1, 0)
        self.purpose_edit = QTextEdit()
        self.purpose_edit.setMaximumHeight(80)
        self.purpose_edit.setMinimumHeight(80)
        self.purpose_edit.setPlaceholderText("الغرض من التحويل...")
        self.purpose_edit.setStyleSheet(self.get_text_style())
        layout.addWidget(self.purpose_edit, 1, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # الملاحظات
        layout.addWidget(QLabel("الملاحظات:"), 2, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setMinimumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        self.notes_edit.setStyleSheet(self.get_text_style())
        layout.addWidget(self.notes_edit, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        return group

    def create_suppliers_section(self):
        """إنشاء قسم الموردين المرتبطين"""
        group = QGroupBox("👥 الموردين المرتبطين بالحوالة")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #27ae60;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "اسم المورد", "المبلغ", "العملة", "الحالة", "ملاحظات"
        ])

        # تحسين مظهر الجدول
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #27ae60;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                min-height: 200px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #27ae60;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # إعدادات الجدول
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # ضبط عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)      # اسم المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(4, QHeaderView.Stretch)      # ملاحظات

        layout.addWidget(self.suppliers_table)

        # أزرار إدارة الموردين
        suppliers_buttons_layout = QHBoxLayout()

        add_supplier_btn = QPushButton("➕ إضافة مورد")
        add_supplier_btn.setMinimumHeight(35)
        add_supplier_btn.setStyleSheet(self.get_button_style("#27ae60"))
        suppliers_buttons_layout.addWidget(add_supplier_btn)

        remove_supplier_btn = QPushButton("➖ إزالة مورد")
        remove_supplier_btn.setMinimumHeight(35)
        remove_supplier_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        suppliers_buttons_layout.addWidget(remove_supplier_btn)

        suppliers_buttons_layout.addStretch()

        layout.addLayout(suppliers_buttons_layout)

        return group

    def create_buttons_section(self):
        """إنشاء قسم أزرار التحكم"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر حفظ
        self.save_btn = QPushButton("💾 حفظ الحوالة")
        self.save_btn.setMinimumHeight(45)
        self.save_btn.setStyleSheet(self.get_button_style("#27ae60"))
        buttons_layout.addWidget(self.save_btn)

        # زر طباعة
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setMinimumHeight(45)
        self.print_btn.setStyleSheet(self.get_button_style("#3498db"))
        buttons_layout.addWidget(self.print_btn)

        # مساحة مرنة
        buttons_layout.addStretch()

        # زر إلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setMinimumHeight(45)
        self.cancel_btn.setStyleSheet(self.get_button_style("#95a5a6"))
        buttons_layout.addWidget(self.cancel_btn)

        return buttons_layout

    def get_input_style(self):
        """الحصول على نمط الحقول"""
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_readonly_style(self):
        """الحصول على نمط الحقول للقراءة فقط"""
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: #ecf0f1;
                color: #7f8c8d;
            }
        """

    def get_combo_style(self):
        """الحصول على نمط القوائم المنسدلة"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
            }
        """

    def get_text_style(self):
        """الحصول على نمط النصوص المتعددة الأسطر"""
        return """
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_button_style(self, color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b",
            "#95a5a6": "#7f8c8d"
        }
        return color_map.get(color, color)

    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        self.save_btn.clicked.connect(self.save_remittance)
        self.print_btn.clicked.connect(self.print_remittance)
        self.cancel_btn.clicked.connect(self.reject)

    def load_remittance_data(self):
        """تحميل بيانات الحوالة"""
        try:
            with self.db_manager.get_session() as session:
                remittance = session.query(SupplierRemittance).get(self.remittance_id)
                if remittance:
                    # تحميل البيانات إلى النموذج
                    self.remittance_number_edit.setText(remittance.remittance_number or "")
                    # ... باقي الحقول

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الحوالة: {str(e)}")

    def save_remittance(self):
        """حفظ الحوالة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_data():
                return

            # حفظ البيانات
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ الحوالة بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ الحوالة: {str(e)}")

    def print_remittance(self):
        """طباعة الحوالة"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة الطباعة قريباً")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.sender_bank_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "البنك المرسل مطلوب")
            return False

        if not self.receiver_bank_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "البنك المستقبل مطلوب")
            return False

        return True
