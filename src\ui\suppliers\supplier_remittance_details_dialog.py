# -*- coding: utf-8 -*-
"""
نافذة تفاصيل حوالات الموردين
Supplier Remittance Details Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox,
                               QTextEdit, QAbstractItemView, QDoubleSpinBox, QFrame)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import SupplierRemittance, SupplierRemittanceDetail, Supplier


class SupplierRemittanceDetailsDialog(QDialog):
    """نافذة تفاصيل حوالات الموردين"""
    
    def __init__(self, parent=None, remittance_id=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.remittance_id = remittance_id
        self.supplier_details = []
        
        self.setWindowTitle("تفاصيل حوالة الموردين")
        self.setModal(True)
        self.resize(800, 600)
        
        self.setup_ui()
        self.load_suppliers()
        
        if self.remittance_id:
            self.load_remittance_details()
            self.setWindowTitle("عرض تفاصيل الحوالة")
            # تعطيل التعديل للحوالات المؤكدة
            self.check_edit_permissions()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # العنوان
        title_label = QLabel("تفاصيل توزيع الحوالة على الموردين")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # قسم إضافة مورد جديد
        add_group = QGroupBox("إضافة مورد للحوالة")
        add_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: white;
                color: #3498db;
            }
        """)

        # استخدام Grid Layout للتحكم الأفضل
        add_layout = QGridLayout(add_group)
        add_layout.setSpacing(20)
        add_layout.setContentsMargins(25, 30, 25, 25)

        # اختيار المورد
        supplier_label = QLabel("المورد:")
        supplier_label.setStyleSheet("font-weight: bold; color: #34495e; font-size: 14px;")
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumHeight(40)
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 14px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                font-size: 14px;
            }
        """)
        add_layout.addWidget(supplier_label, 0, 0)
        add_layout.addWidget(self.supplier_combo, 0, 1)

        # المبلغ
        amount_label = QLabel("المبلغ:")
        amount_label.setStyleSheet("font-weight: bold; color: #34495e; font-size: 14px;")
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0.01, 999999999)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" ريال")
        self.amount_spin.setMinimumHeight(40)
        self.amount_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 14px;
                background-color: white;
                min-width: 150px;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """)
        add_layout.addWidget(amount_label, 0, 2)
        add_layout.addWidget(self.amount_spin, 0, 3)

        # الغرض من الدفع
        purpose_label = QLabel("الغرض من الدفع:")
        purpose_label.setStyleSheet("font-weight: bold; color: #34495e; font-size: 14px;")
        self.purpose_edit = QLineEdit()
        self.purpose_edit.setPlaceholderText("مثل: دفع فاتورة، دفعة مقدمة، تسوية حساب...")
        self.purpose_edit.setMinimumHeight(40)
        self.purpose_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        add_layout.addWidget(purpose_label, 1, 0)
        add_layout.addWidget(self.purpose_edit, 1, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # أرقام الفواتير
        invoices_label = QLabel("أرقام الفواتير:")
        invoices_label.setStyleSheet("font-weight: bold; color: #34495e; font-size: 14px;")
        self.invoices_edit = QLineEdit()
        self.invoices_edit.setPlaceholderText("أرقام الفواتير مفصولة بفاصلة (اختياري)")
        self.invoices_edit.setMinimumHeight(40)
        self.invoices_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        add_layout.addWidget(invoices_label, 2, 0)
        add_layout.addWidget(self.invoices_edit, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # ملاحظات
        notes_label = QLabel("الملاحظات:")
        notes_label.setStyleSheet("font-weight: bold; color: #34495e; font-size: 14px;")
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setMinimumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 14px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        add_layout.addWidget(notes_label, 3, 0)
        add_layout.addWidget(self.notes_edit, 3, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # زر الإضافة
        add_button_layout = QHBoxLayout()
        add_button_layout.addStretch()

        self.add_supplier_button = QPushButton("➕ إضافة المورد")
        self.add_supplier_button.clicked.connect(self.add_supplier_detail)
        self.add_supplier_button.setMinimumHeight(45)
        self.add_supplier_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 160px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #229954;
                transform: translateY(0px);
            }
        """)
        add_button_layout.addWidget(self.add_supplier_button)
        add_button_layout.addStretch()

        add_layout.addLayout(add_button_layout, 4, 0, 1, 4)  # يمتد عبر 4 أعمدة

        layout.addWidget(add_group)

        # جدول الموردين المضافين
        table_group = QGroupBox("الموردين المضافين للحوالة")
        table_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e67e22;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: white;
                color: #e67e22;
            }
        """)

        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(15)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(6)
        self.suppliers_table.setHorizontalHeaderLabels([
            "المورد", "المبلغ", "الغرض", "أرقام الفواتير", "الملاحظات", "العمليات"
        ])

        # ضبط خصائص الجدول المحسنة
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setMinimumHeight(200)
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #f1f3f4;
                border-right: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5d6d7e, stop:1 #34495e);
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c7b8a, stop:1 #3b4a5c);
            }
        """)

        # ضبط عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # الغرض
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الفواتير
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # الملاحظات
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # العمليات

        table_layout.addWidget(self.suppliers_table)

        # معلومات الإجمالي المحسنة
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 10px;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        total_layout.setContentsMargins(20, 15, 20, 15)
        total_layout.addStretch()

        total_icon = QLabel("💰")
        total_icon.setFont(QFont("Arial", 16))
        total_layout.addWidget(total_icon)

        self.total_label = QLabel("الإجمالي: 0.00 ريال")
        self.total_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.total_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: transparent;
                padding: 5px 15px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                background-color: white;
            }
        """)
        total_layout.addWidget(self.total_label)
        total_layout.addStretch()

        table_layout.addWidget(total_frame)
        layout.addWidget(table_group)

        # أزرار الحوار المحسنة
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 2px solid #dee2e6;
                border-radius: 0 0 10px 10px;
                margin-top: 15px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(25, 20, 25, 20)
        buttons_layout.setSpacing(15)
        buttons_layout.addStretch()

        if not self.remittance_id:  # نافذة إضافة جديدة
            self.ok_button = QPushButton("✅ موافق")
            self.ok_button.clicked.connect(self.accept)
            self.ok_button.setMinimumHeight(45)
            self.ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 14px;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                    transform: translateY(-2px);
                }
                QPushButton:pressed {
                    background-color: #21618c;
                    transform: translateY(0px);
                }
            """)
            buttons_layout.addWidget(self.ok_button)

        cancel_text = "❌ إغلاق" if self.remittance_id else "❌ إلغاء"
        self.cancel_button = QPushButton(cancel_text)
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setMinimumHeight(45)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
                transform: translateY(0px);
            }
        """)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()
            
            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر المورد --", None)
            
            for supplier in suppliers:
                display_text = f"{supplier.name} ({supplier.code})" if supplier.code else supplier.name
                self.supplier_combo.addItem(display_text, supplier.id)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()

    def add_supplier_detail(self):
        """إضافة تفاصيل مورد جديد"""
        if not self.validate_supplier_form():
            return
        
        supplier_id = self.supplier_combo.currentData()
        supplier_name = self.supplier_combo.currentText().split(" (")[0]
        amount = self.amount_spin.value()
        purpose = self.purpose_edit.text().strip()
        invoices = self.invoices_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()
        
        # التحقق من عدم تكرار المورد
        for detail in self.supplier_details:
            if detail['supplier_id'] == supplier_id:
                QMessageBox.warning(self, "تحذير", "هذا المورد مضاف بالفعل")
                return
        
        # إضافة التفاصيل
        detail = {
            'supplier_id': supplier_id,
            'supplier_name': supplier_name,
            'amount': amount,
            'purpose': purpose,
            'invoices': invoices,
            'notes': notes
        }
        
        self.supplier_details.append(detail)
        self.update_suppliers_table()
        self.clear_supplier_form()

    def validate_supplier_form(self):
        """التحقق من صحة بيانات المورد"""
        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            self.supplier_combo.setFocus()
            return False
        
        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            self.amount_spin.setFocus()
            return False
        
        if not self.purpose_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الغرض من الدفع")
            self.purpose_edit.setFocus()
            return False
        
        return True

    def clear_supplier_form(self):
        """مسح نموذج المورد"""
        self.supplier_combo.setCurrentIndex(0)
        self.amount_spin.setValue(0)
        self.purpose_edit.clear()
        self.invoices_edit.clear()
        self.notes_edit.clear()

    def update_suppliers_table(self):
        """تحديث جدول الموردين"""
        self.suppliers_table.setRowCount(len(self.supplier_details))

        total_amount = 0

        for row, detail in enumerate(self.supplier_details):
            # المورد
            supplier_item = QTableWidgetItem(detail['supplier_name'])
            self.suppliers_table.setItem(row, 0, supplier_item)

            # المبلغ
            amount_item = QTableWidgetItem(f"{detail['amount']:,.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.suppliers_table.setItem(row, 1, amount_item)
            total_amount += detail['amount']

            # الغرض
            purpose_item = QTableWidgetItem(detail['purpose'])
            self.suppliers_table.setItem(row, 2, purpose_item)

            # أرقام الفواتير
            invoices_item = QTableWidgetItem(detail['invoices'])
            self.suppliers_table.setItem(row, 3, invoices_item)

            # الملاحظات
            notes_item = QTableWidgetItem(detail['notes'])
            self.suppliers_table.setItem(row, 4, notes_item)

            # زر الحذف المحسن
            if not self.remittance_id or not self.is_posted():
                delete_button = QPushButton("🗑️ حذف")
                delete_button.setMinimumHeight(35)
                delete_button.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        padding: 8px 15px;
                        border-radius: 6px;
                        font-size: 12px;
                        font-weight: bold;
                        min-width: 70px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                        transform: translateY(-1px);
                    }
                    QPushButton:pressed {
                        background-color: #a93226;
                        transform: translateY(0px);
                    }
                """)
                delete_button.clicked.connect(lambda checked, r=row: self.remove_supplier_detail(r))
                self.suppliers_table.setCellWidget(row, 5, delete_button)

        # تحديث الإجمالي
        self.total_label.setText(f"الإجمالي: {total_amount:,.2f} ريال")

    def remove_supplier_detail(self, row):
        """حذف تفاصيل مورد"""
        if 0 <= row < len(self.supplier_details):
            supplier_name = self.supplier_details[row]['supplier_name']

            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المورد '{supplier_name}' من الحوالة؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                del self.supplier_details[row]
                self.update_suppliers_table()

    def load_remittance_details(self):
        """تحميل تفاصيل الحوالة الموجودة"""
        if not self.remittance_id:
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.remittance_id)
            if not remittance:
                return

            self.supplier_details = []

            for detail in remittance.remittance_details:
                supplier_detail = {
                    'supplier_id': detail.supplier_id,
                    'supplier_name': detail.supplier.name,
                    'amount': detail.amount,
                    'purpose': detail.payment_purpose or "",
                    'invoices': detail.invoice_numbers or "",
                    'notes': detail.notes or ""
                }
                self.supplier_details.append(supplier_detail)

            self.update_suppliers_table()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل تفاصيل الحوالة: {str(e)}")
        finally:
            session.close()

    def check_edit_permissions(self):
        """فحص صلاحيات التعديل"""
        if not self.remittance_id:
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.remittance_id)
            if remittance and remittance.status in ["confirmed", "posted"]:
                # تعطيل التعديل للحوالات المؤكدة أو المرحلة
                self.add_supplier_button.setEnabled(False)
                self.supplier_combo.setEnabled(False)
                self.amount_spin.setEnabled(False)
                self.purpose_edit.setEnabled(False)
                self.invoices_edit.setEnabled(False)
                self.notes_edit.setEnabled(False)

        except Exception as e:
            print(f"خطأ في فحص صلاحيات التعديل: {str(e)}")
        finally:
            session.close()

    def is_posted(self):
        """فحص ما إذا كانت الحوالة مرحلة"""
        if not self.remittance_id:
            return False

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.remittance_id)
            return remittance and remittance.status in ["confirmed", "posted"]
        except:
            return False
        finally:
            session.close()

    def get_supplier_details(self):
        """الحصول على تفاصيل الموردين"""
        return self.supplier_details.copy()

    def accept(self):
        """قبول الحوار"""
        if not self.supplier_details:
            QMessageBox.warning(self, "تحذير", "يجب إضافة مورد واحد على الأقل")
            return

        super().accept()
