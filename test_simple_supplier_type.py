#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنوع شركة شحن في الموردين
Simple Test for Shipping Company Supplier Type
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_supplier_types_in_code():
    """اختبار أنواع الموردين في الكود"""
    print("🔍 فحص أنواع الموردين في الكود")
    print("=" * 50)
    
    try:
        # قراءة ملف suppliers_data.py
        suppliers_file = "src/ui/suppliers/suppliers_data.py"
        
        with open(suppliers_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن السطر الذي يحتوي على addItems
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'addItems' in line and 'supplier_type_combo' in lines[i-1]:
                print(f"✅ تم العثور على السطر {i+1}:")
                print(f"   {line.strip()}")
                
                # فحص الأنواع
                if "شركة شحن" in line:
                    print("✅ تم إضافة نوع 'شركة شحن' بنجاح!")
                    
                    # استخراج جميع الأنواع
                    import re
                    types_match = re.search(r'\[(.*?)\]', line)
                    if types_match:
                        types_str = types_match.group(1)
                        types = [t.strip().strip('"') for t in types_str.split(',')]
                        
                        print("\n📋 جميع أنواع الموردين:")
                        for j, supplier_type in enumerate(types, 1):
                            status = "🆕" if supplier_type == "شركة شحن" else "🔹"
                            print(f"   {j}. {status} {supplier_type}")
                        
                        return True
                else:
                    print("❌ لم يتم العثور على نوع 'شركة شحن'")
                    return False
        
        print("❌ لم يتم العثور على السطر المطلوب")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص الكود: {e}")
        return False

def test_documentation_update():
    """اختبار تحديث التوثيق"""
    print("\n📚 فحص تحديث التوثيق")
    print("-" * 30)
    
    try:
        # قراءة ملف التوثيق
        doc_file = "دليل_استيراد_الموردين.md"
        
        with open(doc_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "شركة شحن" in content:
            print("✅ تم تحديث التوثيق ليشمل نوع 'شركة شحن'")
            
            # البحث عن السطر المحدث
            lines = content.split('\n')
            for line in lines:
                if "نوع المورد" in line and "شركة شحن" in line:
                    print(f"   📝 {line.strip()}")
                    break
            
            return True
        else:
            print("❌ لم يتم تحديث التوثيق")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص التوثيق: {e}")
        return False

def test_database_compatibility():
    """اختبار التوافق مع قاعدة البيانات"""
    print("\n🗄️ اختبار التوافق مع قاعدة البيانات")
    print("-" * 40)
    
    try:
        from src.database.models import Supplier
        
        # إنشاء مورد تجريبي
        test_supplier = Supplier(
            code="SHIP001",
            name="شركة الخليج للشحن",
            supplier_type="شركة شحن",
            phone="011-1234567",
            is_active=True
        )
        
        print("✅ تم إنشاء مورد من نوع 'شركة شحن' بنجاح")
        print(f"   📝 الاسم: {test_supplier.name}")
        print(f"   🏷️ النوع: {test_supplier.supplier_type}")
        print(f"   📞 الهاتف: {test_supplier.phone}")
        
        # التحقق من صحة النوع
        if test_supplier.supplier_type == "شركة شحن":
            print("✅ النوع محفوظ بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في حفظ النوع")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بسيط لإضافة نوع 'شركة شحن' للموردين")
    print("=" * 60)
    
    tests = [
        ("فحص الكود", test_supplier_types_in_code),
        ("فحص التوثيق", test_documentation_update),
        ("اختبار قاعدة البيانات", test_database_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print(f"• الاختبارات الناجحة: {passed}/{total}")
    print(f"• معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 تم إضافة نوع 'شركة شحن' بنجاح!")
        print("\n📋 ملخص التحديث:")
        print("┌─────────────────────────────────────────────────────────┐")
        print("│ ✅ تم إضافة 'شركة شحن' إلى قائمة أنواع الموردين       │")
        print("│ ✅ تم تحديث الكود في suppliers_data.py               │")
        print("│ ✅ تم تحديث التوثيق في دليل_استيراد_الموردين.md     │")
        print("│ ✅ النوع متوافق مع قاعدة البيانات                   │")
        print("│ ✅ يمكن إنشاء موردين من نوع 'شركة شحن'              │")
        print("└─────────────────────────────────────────────────────────┘")
        print("\n🎯 الأنواع المتاحة الآن:")
        print("   1. 🏢 شركة")
        print("   2. 👤 فرد") 
        print("   3. 🏛️ مؤسسة")
        print("   4. 🚢 شركة شحن (جديد)")
        
    elif passed >= total * 0.8:
        print("\n⭐ معظم التحديثات تمت بنجاح!")
        
    else:
        print("\n⚠️ هناك مشاكل في التحديث")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
