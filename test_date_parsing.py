#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحليل التواريخ في الحقول المرنة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QDate
from src.ui.widgets.flexible_date_edit import FlexibleDateEdit

def test_date_parsing():
    """اختبار تحليل التواريخ"""

    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)

    print("🧪 اختبار تحليل التواريخ المرنة")
    print("=" * 50)

    # إنشاء حقل تاريخ مرن
    date_edit = FlexibleDateEdit()
    
    # قائمة التواريخ للاختبار
    test_dates = [
        "15/03/2024",
        "15032024", 
        "15-03-2024",
        "15.03.2024",
        "1/1/2024",
        "01012024",
        "31/12/2023",
        "31122023"
    ]
    
    print("📝 اختبار تحليل التواريخ:")
    print()
    
    for test_date in test_dates:
        try:
            # تحليل التاريخ
            parsed_date = date_edit.parse_date_string(test_date)
            
            if parsed_date.isValid():
                formatted_date = parsed_date.toString("dd/MM/yyyy")
                print(f"✅ '{test_date}' -> '{formatted_date}'")
            else:
                print(f"❌ '{test_date}' -> غير صالح")
                
        except Exception as e:
            print(f"❌ '{test_date}' -> خطأ: {str(e)}")
    
    print()
    print("🎉 انتهى الاختبار!")
    
    # اختبار إضافي للتحقق من التحويل التلقائي
    print()
    print("🔄 اختبار التحويل التلقائي:")
    print()
    
    # محاكاة إدخال المستخدم
    user_inputs = [
        "15032024",  # بدون فواصل
        "1012024",   # تاريخ قصير
        "5012024",   # تاريخ قصير آخر
        "31122023"   # نهاية السنة
    ]
    
    for user_input in user_inputs:
        try:
            # تحليل وتحويل
            parsed = date_edit.parse_date_string(user_input)
            if parsed.isValid():
                formatted = parsed.toString("dd/MM/yyyy")
                print(f"🔄 المستخدم أدخل: '{user_input}' -> تم التحويل إلى: '{formatted}'")
            else:
                print(f"❌ لا يمكن تحويل: '{user_input}'")
        except Exception as e:
            print(f"❌ خطأ في تحويل '{user_input}': {str(e)}")

if __name__ == "__main__":
    test_date_parsing()
