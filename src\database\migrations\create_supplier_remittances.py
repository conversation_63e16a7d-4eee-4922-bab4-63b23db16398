# -*- coding: utf-8 -*-
"""
إنشاء جداول حوالات الموردين
Create Supplier Remittances Tables Migration
"""

import sqlite3
import os

def create_supplier_remittances_tables():
    """إنشاء جداول حوالات الموردين"""
    
    # مسار قاعدة البيانات
    db_path = 'data/proshipment.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 فحص وجود جداول حوالات الموردين...")
        
        # فحص وجود الجداول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('supplier_remittances', 'supplier_remittance_details')
        """)
        
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        # إنشاء جدول الحوالات الرئيسي
        if 'supplier_remittances' not in existing_tables:
            print("🔨 إنشاء جدول حوالات الموردين...")
            
            cursor.execute("""
                CREATE TABLE supplier_remittances (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    remittance_number VARCHAR(50) UNIQUE NOT NULL,
                    remittance_date DATE NOT NULL,
                    
                    -- بيانات البنك المرسل
                    sender_bank_name VARCHAR(200) NOT NULL,
                    sender_bank_code VARCHAR(50),
                    sender_account_number VARCHAR(100),
                    
                    -- بيانات البنك المستقبل
                    receiver_bank_name VARCHAR(200) NOT NULL,
                    receiver_bank_code VARCHAR(50),
                    receiver_bank_country VARCHAR(100),
                    
                    -- المبلغ والعملة
                    total_amount REAL NOT NULL,
                    currency_id INTEGER NOT NULL,
                    exchange_rate REAL DEFAULT 1.0,
                    amount_in_base_currency REAL,
                    
                    -- رسوم التحويل
                    transfer_fees REAL DEFAULT 0,
                    bank_charges REAL DEFAULT 0,
                    
                    -- حالة الحوالة
                    status VARCHAR(50) DEFAULT 'pending',
                    
                    -- تواريخ مهمة
                    sent_date DATETIME,
                    confirmation_date DATETIME,
                    posting_date DATETIME,
                    
                    -- معلومات إضافية
                    reference_number VARCHAR(100),
                    swift_code VARCHAR(50),
                    purpose TEXT,
                    notes TEXT,
                    
                    -- معلومات النظام
                    created_by VARCHAR(100),
                    confirmed_by VARCHAR(100),
                    posted_by VARCHAR(100),
                    
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (currency_id) REFERENCES currencies (id)
                )
            """)
            
            print("✅ تم إنشاء جدول حوالات الموردين بنجاح!")
        else:
            print("ℹ️ جدول حوالات الموردين موجود بالفعل")
        
        # إنشاء جدول تفاصيل الحوالات
        if 'supplier_remittance_details' not in existing_tables:
            print("🔨 إنشاء جدول تفاصيل حوالات الموردين...")
            
            cursor.execute("""
                CREATE TABLE supplier_remittance_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    remittance_id INTEGER NOT NULL,
                    supplier_id INTEGER NOT NULL,
                    
                    -- المبلغ المخصص للمورد
                    amount REAL NOT NULL,
                    amount_in_base_currency REAL,
                    
                    -- معلومات الدفع
                    payment_purpose VARCHAR(200),
                    invoice_numbers TEXT,
                    
                    -- حالة التفصيل
                    is_posted BOOLEAN DEFAULT 0,
                    posting_date DATETIME,
                    
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (remittance_id) REFERENCES supplier_remittances (id),
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            """)
            
            print("✅ تم إنشاء جدول تفاصيل حوالات الموردين بنجاح!")
        else:
            print("ℹ️ جدول تفاصيل حوالات الموردين موجود بالفعل")
        
        # إنشاء الفهارس
        print("🔨 إنشاء الفهارس...")
        
        indexes = [
            ("idx_supplier_remittances_number", "supplier_remittances", "remittance_number"),
            ("idx_supplier_remittances_date", "supplier_remittances", "remittance_date"),
            ("idx_supplier_remittances_status", "supplier_remittances", "status"),
            ("idx_supplier_remittances_currency", "supplier_remittances", "currency_id"),
            ("idx_supplier_remittance_details_remittance", "supplier_remittance_details", "remittance_id"),
            ("idx_supplier_remittance_details_supplier", "supplier_remittance_details", "supplier_id"),
            ("idx_supplier_remittance_details_posted", "supplier_remittance_details", "is_posted"),
        ]
        
        for index_name, table_name, column_name in indexes:
            try:
                cursor.execute(f"CREATE INDEX {index_name} ON {table_name}({column_name})")
            except sqlite3.OperationalError:
                pass  # الفهرس موجود بالفعل
        
        print("✅ تم إنشاء الفهارس بنجاح!")
        
        # حفظ التغييرات
        conn.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول حوالات الموردين: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def insert_sample_data():
    """إدراج بيانات تجريبية"""
    try:
        db_path = 'data/proshipment.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧪 إدراج بيانات تجريبية...")
        
        # إدراج حوالة تجريبية
        cursor.execute("""
            INSERT INTO supplier_remittances 
            (remittance_number, remittance_date, sender_bank_name, receiver_bank_name, 
             total_amount, currency_id, status, purpose, created_by)
            VALUES 
            ('REM-2024-001', '2024-01-15', 'البنك الأهلي السعودي', 'Bank of America', 
             50000.00, 1, 'pending', 'دفع مستحقات الموردين', 'النظام')
        """)
        
        remittance_id = cursor.lastrowid
        
        # إدراج تفاصيل الحوالة
        cursor.execute("""
            INSERT INTO supplier_remittance_details 
            (remittance_id, supplier_id, amount, payment_purpose)
            VALUES 
            (?, 1, 30000.00, 'دفع فاتورة رقم INV-001'),
            (?, 2, 20000.00, 'دفع فاتورة رقم INV-002')
        """, (remittance_id, remittance_id))
        
        conn.commit()
        
        # عرض البيانات
        cursor.execute("""
            SELECT sr.remittance_number, sr.total_amount, sr.status,
                   srd.amount, s.name as supplier_name
            FROM supplier_remittances sr
            JOIN supplier_remittance_details srd ON sr.id = srd.remittance_id
            JOIN suppliers s ON srd.supplier_id = s.id
            WHERE sr.is_active = 1
        """)
        
        remittances = cursor.fetchall()
        print("💰 الحوالات التجريبية:")
        for remittance in remittances:
            print(f"   - {remittance[0]}: {remittance[1]} ريال ({remittance[2]})")
            print(f"     → {remittance[4]}: {remittance[3]} ريال")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات التجريبية: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إنشاء نظام حوالات الموردين")
    print("=" * 50)
    
    if create_supplier_remittances_tables():
        print("\n" + "=" * 50)
        insert_sample_data()
        print("\n🎉 تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشل في إنشاء جداول حوالات الموردين")
