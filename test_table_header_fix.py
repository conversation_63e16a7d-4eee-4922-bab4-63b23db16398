#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح رأس جدول طلبات الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def test_table_header_fix():
    """اختبار إصلاح رأس الجدول"""
    print("🔄 بدء اختبار إصلاح رأس جدول طلبات الشراء...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # فتح نافذة قائمة الطلبات
        window.show_orders_list()
        print("✅ تم فتح نافذة قائمة الطلبات")
        
        # التحقق من وجود الجدول ورأسه
        if hasattr(window, 'orders_list_window') and window.orders_list_window:
            orders_window = window.orders_list_window
            
            if hasattr(orders_window, 'orders_table'):
                table = orders_window.orders_table
                
                # التحقق من عدد الأعمدة
                column_count = table.columnCount()
                print(f"✅ عدد الأعمدة: {column_count}")
                
                # التحقق من تسميات الأعمدة
                headers = []
                for i in range(column_count):
                    header_text = table.horizontalHeaderItem(i)
                    if header_text:
                        headers.append(header_text.text())
                    else:
                        headers.append(f"عمود {i+1}")
                
                print(f"✅ تسميات الأعمدة: {headers}")
                
                # التحقق من ظهور الرأس
                header_widget = table.horizontalHeader()
                is_visible = header_widget.isVisible()
                print(f"✅ رأس الجدول مرئي: {is_visible}")
                
                # التحقق من ارتفاع الرأس
                header_height = header_widget.height()
                print(f"✅ ارتفاع رأس الجدول: {header_height} بكسل")
                
                if is_visible and header_height > 0:
                    print("🎉 رأس الجدول يعمل بشكل صحيح!")
                else:
                    print("⚠️ قد تكون هناك مشكلة في رأس الجدول")
                
        print("✅ اكتمل الاختبار")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_table_header_fix()
    sys.exit(0 if success else 1)
