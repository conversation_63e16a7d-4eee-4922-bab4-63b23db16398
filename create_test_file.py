# -*- coding: utf-8 -*-
"""
إنشاء ملف إكسيل تجريبي لاختبار نظام الاستيراد المحسن
"""

import pandas as pd

# بيانات تجريبية تحتوي على أكواد مكررة
test_data = {
    'اسم الصنف': [
        'صنف تجريبي 1',
        'صنف تجريبي 2', 
        'صنف تجريبي 3',
        'صنف تجريبي 1 مكرر',  # نفس الكود
        'صنف تجريبي 4',
        'صنف تجريبي 5',
        'صنف تجريبي 2 مكرر',  # نفس الكود
        'صنف موجود مسبقاً',    # كود موجود في قاعدة البيانات
    ],
    'كود الصنف': [
        'TEST001',
        'TEST002',
        'TEST003', 
        'TEST001',  # مكرر
        'TEST004',
        'TEST005',
        'TEST002',  # مكرر
        'RICE001',  # موجود في قاعدة البيانات
    ],
    'الاسم الإنجليزي': [
        'Test Item 1',
        'Test Item 2',
        'Test Item 3',
        'Test Item 1 Duplicate',
        'Test Item 4', 
        'Test Item 5',
        'Test Item 2 Duplicate',
        'Existing Rice',
    ],
    'الوصف': [
        'وصف الصنف التجريبي الأول',
        'وصف الصنف التجريبي الثاني',
        'وصف الصنف التجريبي الثالث',
        'وصف الصنف المكرر الأول',
        'وصف الصنف التجريبي الرابع',
        'وصف الصنف التجريبي الخامس',
        'وصف الصنف المكرر الثاني',
        'وصف الصنف الموجود مسبقاً',
    ],
    'المجموعة': [
        'مواد غذائية',
        'مواد غذائية',
        'مواد غذائية',
        'مواد غذائية',
        'مواد غذائية',
        'مواد غذائية',
        'مواد غذائية',
        'مواد غذائية',
    ],
    'وحدة القياس': [
        'كيلوجرام',
        'كيلوجرام',
        'كيلوجرام',
        'كيلوجرام',
        'كيلوجرام',
        'كيلوجرام',
        'كيلوجرام',
        'كيلوجرام',
    ],
    'سعر التكلفة': [
        10.5,
        15.0,
        20.0,
        12.0,  # سعر مختلف للمكرر
        25.0,
        30.0,
        18.0,  # سعر مختلف للمكرر
        8.0,
    ],
    'سعر البيع': [
        15.0,
        22.0,
        28.0,
        18.0,  # سعر مختلف للمكرر
        35.0,
        42.0,
        25.0,  # سعر مختلف للمكرر
        12.0,
    ],
    'الوزن': [
        1.0,
        1.5,
        2.0,
        1.2,
        2.5,
        3.0,
        1.8,
        1.0,
    ],
    'الأبعاد': [
        '10x10x10 سم',
        '15x15x15 سم',
        '20x20x20 سم',
        '12x12x12 سم',
        '25x25x25 سم',
        '30x30x30 سم',
        '18x18x18 سم',
        '10x10x10 سم',
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(test_data)

# حفظ الملف
filename = 'اختبار_استيراد_أصناف_مكررة.xlsx'
df.to_excel(filename, index=False, engine='openpyxl')

print(f"✅ تم إنشاء ملف الاختبار: {filename}")
print("\nمحتويات الملف:")
print("=" * 50)
print("الأكواد المكررة في الملف:")
print("- TEST001 (مكرر في الصف 1 و 4)")
print("- TEST002 (مكرر في الصف 2 و 7)")
print("\nالأكواد الموجودة في قاعدة البيانات:")
print("- RICE001 (موجود مسبقاً)")
print("\nالأكواد الجديدة:")
print("- TEST003, TEST004, TEST005")
print("\nيمكنك الآن استخدام هذا الملف لاختبار نظام الاستيراد المحسن!")
