#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة أعمدة التواريخ في شاشة الشحنة الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.database.database_manager import DatabaseManager
from src.utils.arabic_support import setup_arabic_support

def main():
    """اختبار شاشة الشحنة الجديدة مع أعمدة التواريخ"""
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # إعداد دعم اللغة العربية
    setup_arabic_support(app)
    
    # إعداد قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    print("🚀 تم تشغيل شاشة الشحنة الجديدة مع التحديثات")
    print("✅ التحديثات المطبقة:")
    print("   - إضافة عمود تاريخ الإنتاج بعد عمود سعر الوحدة")
    print("   - إضافة عمود تاريخ الانتهاء بعد عمود تاريخ الإنتاج")
    print("   - تحديث جميع الفهارس للأعمدة الأخرى")
    print("   - تحديث دوال التحميل والحفظ")
    print("   - إصلاح مشكلة الوصول لنظام الموردين")
    print()
    print("📋 ترتيب الأعمدة الجديد:")
    print("   0: كود الصنف")
    print("   1: اسم الصنف") 
    print("   2: الكمية")
    print("   3: سعر الوحدة")
    print("   4: تاريخ الإنتاج")
    print("   5: تاريخ الانتهاء")
    print("   6: السعر الإجمالي")
    print("   7: الوزن")
    print("   8: ملاحظات")
    
    # إنشاء نافذة الشحنة الجديدة
    window = NewShipmentWindow()
    window.show()
    
    # إضافة بعض البيانات التجريبية لاختبار الأعمدة الجديدة
    window.add_test_items()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
