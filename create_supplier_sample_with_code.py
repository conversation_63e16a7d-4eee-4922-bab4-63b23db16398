#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def create_supplier_sample_files():
    """إنشاء ملفات نموذجية لاستيراد الموردين مع رقم المورد"""
    
    # البيانات النموذجية
    sample_data = [
        {
            'رقم المورد': 'SUP001',
            'اسم المورد': 'شركة الأهرام للتجارة',
            'الاسم الإنجليزي': 'Al-Ahram Trading Company',
            'نوع المورد': 'شركة',
            'الرقم الضريبي': '*********',
            'السجل التجاري': 'CR123456',
            'الهاتف': '011-1234567',
            'الجوال': '0101234567',
            'البريد الإلكتروني': '<EMAIL>',
            'الموقع الإلكتروني': 'www.ahram-trading.com',
            'الدولة': 'مصر',
            'المدينة': 'القاهرة',
            'العنوان': 'شارع التحرير، وسط البلد',
            'الرمز البريدي': '11511',
            'حد الائتمان': 50000,
            'مدة السداد': 30,
            'شخص الاتصال': 'أحمد محمد'
        },
        {
            'رقم المورد': 'SUP002',
            'اسم المورد': 'مؤسسة النيل للمواد الغذائية',
            'الاسم الإنجليزي': 'Nile Food Materials Est.',
            'نوع المورد': 'مؤسسة فردية',
            'الرقم الضريبي': '987654321',
            'السجل التجاري': 'CR987654',
            'الهاتف': '02-2345678',
            'الجوال': '0109876543',
            'البريد الإلكتروني': '<EMAIL>',
            'الموقع الإلكتروني': 'www.nile-food.com',
            'الدولة': 'مصر',
            'المدينة': 'الجيزة',
            'العنوان': 'شارع الهرم، الجيزة',
            'الرمز البريدي': '12411',
            'حد الائتمان': 75000,
            'مدة السداد': 45,
            'شخص الاتصال': 'فاطمة أحمد'
        },
        {
            'رقم المورد': 'SUP003',
            'اسم المورد': 'شركة الدلتا للإلكترونيات',
            'الاسم الإنجليزي': 'Delta Electronics Co.',
            'نوع المورد': 'شركة',
            'الرقم الضريبي': '456789123',
            'السجل التجاري': 'CR456789',
            'الهاتف': '040-3456789',
            'الجوال': '0*********',
            'البريد الإلكتروني': '<EMAIL>',
            'الموقع الإلكتروني': 'www.delta-electronics.com',
            'الدولة': 'مصر',
            'المدينة': 'الإسكندرية',
            'العنوان': 'طريق الكورنيش، الإسكندرية',
            'الرمز البريدي': '21500',
            'حد الائتمان': 100000,
            'مدة السداد': 60,
            'شخص الاتصال': 'محمد علي'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(sample_data)
    
    # إنشاء الملف النموذجي الأساسي
    sample_file = 'نموذج_استيراد_الموردين_محدث.xlsx'
    df.to_excel(sample_file, index=False, engine='openpyxl')
    print(f"تم إنشاء الملف النموذجي: {sample_file}")
    
    # إنشاء ملف اختبار أكبر
    test_data = []
    for i in range(1, 21):  # 20 مورد للاختبار
        test_data.append({
            'رقم المورد': f'TEST{i:03d}',
            'اسم المورد': f'مورد اختبار {i}',
            'الاسم الإنجليزي': f'Test Supplier {i}',
            'نوع المورد': 'شركة' if i % 2 == 0 else 'مؤسسة فردية',
            'الرقم الضريبي': f'{100000000 + i}',
            'السجل التجاري': f'CR{100000 + i}',
            'الهاتف': f'02-{2000000 + i}',
            'الجوال': f'010{1000000 + i}',
            'البريد الإلكتروني': f'supplier{i}@test.com',
            'الموقع الإلكتروني': f'www.supplier{i}.com',
            'الدولة': 'مصر',
            'المدينة': 'القاهرة' if i % 3 == 0 else ('الجيزة' if i % 3 == 1 else 'الإسكندرية'),
            'العنوان': f'عنوان اختبار {i}',
            'الرمز البريدي': f'{11000 + i}',
            'حد الائتمان': 10000 + (i * 1000),
            'مدة السداد': 30 + (i % 4) * 15,
            'شخص الاتصال': f'شخص الاتصال {i}'
        })
    
    test_df = pd.DataFrame(test_data)
    test_file = 'اختبار_استيراد_موردين_محدث.xlsx'
    test_df.to_excel(test_file, index=False, engine='openpyxl')
    print(f"تم إنشاء ملف الاختبار: {test_file}")
    
    # إنشاء ملف فارغ للنموذج
    empty_data = [{
        'رقم المورد': '',
        'اسم المورد': '',
        'الاسم الإنجليزي': '',
        'نوع المورد': '',
        'الرقم الضريبي': '',
        'السجل التجاري': '',
        'الهاتف': '',
        'الجوال': '',
        'البريد الإلكتروني': '',
        'الموقع الإلكتروني': '',
        'الدولة': '',
        'المدينة': '',
        'العنوان': '',
        'الرمز البريدي': '',
        'حد الائتمان': '',
        'مدة السداد': '',
        'شخص الاتصال': ''
    }]
    
    empty_df = pd.DataFrame(empty_data)
    empty_file = 'نموذج_فارغ_استيراد_الموردين.xlsx'
    empty_df.to_excel(empty_file, index=False, engine='openpyxl')
    print(f"تم إنشاء النموذج الفارغ: {empty_file}")
    
    print("\n✅ تم إنشاء جميع ملفات الاستيراد بنجاح!")
    print("📋 الملفات المنشأة:")
    print(f"   • {sample_file} - نموذج مع بيانات تجريبية")
    print(f"   • {test_file} - ملف اختبار مع 20 مورد")
    print(f"   • {empty_file} - نموذج فارغ للتعبئة")

if __name__ == "__main__":
    create_supplier_sample_files()
