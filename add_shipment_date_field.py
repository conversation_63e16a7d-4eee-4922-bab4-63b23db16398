#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة حقل تاريخ الشحنة لجدول الشحنات
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from sqlalchemy import text

def add_shipment_date_field():
    """إضافة حقل تاريخ الشحنة لجدول الشحنات"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على الاتصال المباشر
        engine = db_manager.engine
        
        print("🔄 بدء إضافة حقل تاريخ الشحنة...")
        
        with engine.connect() as connection:
            # التحقق من وجود الحقل
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            existing_columns = [row[1] for row in result.fetchall()]
            
            print(f"📋 الأعمدة الموجودة: {len(existing_columns)}")
            
            if 'shipment_date' not in existing_columns:
                try:
                    # إضافة حقل تاريخ الشحنة
                    alter_sql = """
                    ALTER TABLE shipments 
                    ADD COLUMN shipment_date DATETIME
                    """
                    
                    connection.execute(text(alter_sql))
                    connection.commit()
                    print("✅ تم إضافة حقل تاريخ الشحنة بنجاح!")
                    
                    # تحديث الشحنات الموجودة بتاريخ الإنشاء
                    update_sql = """
                    UPDATE shipments 
                    SET shipment_date = created_at 
                    WHERE shipment_date IS NULL
                    """
                    
                    connection.execute(text(update_sql))
                    connection.commit()
                    print("✅ تم تحديث تواريخ الشحنات الموجودة!")
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة الحقل: {str(e)}")
                    return False
                    
            else:
                print("ℹ️ حقل تاريخ الشحنة موجود بالفعل")
            
            # التحقق من النتيجة النهائية
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            final_columns = [row[1] for row in result.fetchall()]
            
            print(f"\n📊 إجمالي الأعمدة بعد التحديث: {len(final_columns)}")
            print("✅ تم تحديث جدول الشحنات بنجاح!")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_shipment_date_field():
    """اختبار حقل تاريخ الشحنة"""
    try:
        from src.database.models import Shipment
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        print("\n🧪 اختبار حقل تاريخ الشحنة...")
        
        # البحث عن شحنة موجودة
        shipment = session.query(Shipment).first()
        if shipment:
            print(f"📦 اختبار الشحنة: {shipment.shipment_number}")
            
            # اختبار الوصول لحقل تاريخ الشحنة
            shipment_date = getattr(shipment, 'shipment_date', 'غير موجود')
            print(f"   - تاريخ الشحنة: {shipment_date}")
            
            # إذا لم يكن هناك تاريخ، استخدم تاريخ الإنشاء
            if not shipment.shipment_date and shipment.created_at:
                shipment.shipment_date = shipment.created_at
                session.commit()
                print("✅ تم تحديث تاريخ الشحنة!")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== إضافة حقل تاريخ الشحنة ===")
    
    # إضافة الحقل
    if add_shipment_date_field():
        # اختبار الحقل
        test_shipment_date_field()
        print("\n🎉 تم الانتهاء من إضافة حقل تاريخ الشحنة!")
    else:
        print("\n❌ فشل في إضافة حقل تاريخ الشحنة!")
