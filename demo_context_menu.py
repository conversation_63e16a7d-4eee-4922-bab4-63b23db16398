#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لقائمة الزر الأيمن في جدول طلبات الشراء
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_context_menu():
    """عرض توضيحي لقائمة الزر الأيمن"""
    try:
        print("🎬 بدء العرض التوضيحي لقائمة الزر الأيمن...")
        
        # إنشاء التطبيق
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # استيراد النافذة
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # إنشاء النافذة في وضع القائمة
        window = PurchaseOrdersWindow(maximize_on_start=False, mode="list")
        
        # إضافة بيانات تجريبية للعرض
        table = window.orders_table
        table.setRowCount(3)
        
        from PySide6.QtWidgets import QTableWidgetItem
        
        # الطلب الأول
        table.setItem(0, 0, QTableWidgetItem("PO-2024-001"))
        table.item(0, 0).setData(Qt.UserRole, 1)
        table.setItem(0, 1, QTableWidgetItem("2024-01-15"))
        table.setItem(0, 2, QTableWidgetItem("شركة الأجهزة المتقدمة"))
        table.setItem(0, 3, QTableWidgetItem("5"))
        table.setItem(0, 4, QTableWidgetItem("100"))
        table.setItem(0, 5, QTableWidgetItem("15,000.00"))
        table.setItem(0, 6, QTableWidgetItem("USD"))
        table.setItem(0, 7, QTableWidgetItem("جديد"))
        table.setItem(0, 8, QTableWidgetItem("2024-02-15"))
        table.setItem(0, 9, QTableWidgetItem("0"))
        table.setItem(0, 10, QTableWidgetItem("100"))
        table.setItem(0, 11, QTableWidgetItem("طلب أجهزة كمبيوتر"))
        
        # الطلب الثاني
        table.setItem(1, 0, QTableWidgetItem("PO-2024-002"))
        table.item(1, 0).setData(Qt.UserRole, 2)
        table.setItem(1, 1, QTableWidgetItem("2024-01-20"))
        table.setItem(1, 2, QTableWidgetItem("مؤسسة القرطاسية الحديثة"))
        table.setItem(1, 3, QTableWidgetItem("10"))
        table.setItem(1, 4, QTableWidgetItem("500"))
        table.setItem(1, 5, QTableWidgetItem("2,500.00"))
        table.setItem(1, 6, QTableWidgetItem("SAR"))
        table.setItem(1, 7, QTableWidgetItem("مؤكد"))
        table.setItem(1, 8, QTableWidgetItem("2024-02-01"))
        table.setItem(1, 9, QTableWidgetItem("200"))
        table.setItem(1, 10, QTableWidgetItem("300"))
        table.setItem(1, 11, QTableWidgetItem("أدوات مكتبية"))
        
        # الطلب الثالث
        table.setItem(2, 0, QTableWidgetItem("PO-2024-003"))
        table.item(2, 0).setData(Qt.UserRole, 3)
        table.setItem(2, 1, QTableWidgetItem("2024-01-25"))
        table.setItem(2, 2, QTableWidgetItem("شركة المواد الخام"))
        table.setItem(2, 3, QTableWidgetItem("3"))
        table.setItem(2, 4, QTableWidgetItem("1000"))
        table.setItem(2, 5, QTableWidgetItem("50,000.00"))
        table.setItem(2, 6, QTableWidgetItem("EUR"))
        table.setItem(2, 7, QTableWidgetItem("قيد التنفيذ"))
        table.setItem(2, 8, QTableWidgetItem("2024-03-01"))
        table.setItem(2, 9, QTableWidgetItem("600"))
        table.setItem(2, 10, QTableWidgetItem("400"))
        table.setItem(2, 11, QTableWidgetItem("مواد خام للإنتاج"))
        
        # تحديث شريط الحالة
        window.status_bar.showMessage("تم تحميل 3 طلبات شراء - جاهز للاستخدام")
        
        # إظهار النافذة
        window.show()
        
        print("✅ تم إنشاء العرض التوضيحي بنجاح!")
        print("\n📋 تعليمات الاستخدام:")
        print("   1. انقر بالزر الأيمن على أي رقم طلب في الجدول")
        print("   2. ستظهر قائمة تحتوي على:")
        print("      🔧 تعديل الطلب - لفتح نافذة تعديل الطلب")
        print("      🗑️ حذف الطلب - لحذف الطلب (مع تأكيد)")
        print("   3. اختر العملية المطلوبة")
        print("\n⚠️ ملاحظات مهمة:")
        print("   • عملية الحذف تتطلب تأكيد من المستخدم")
        print("   • الحذف منطقي (is_active = False) وليس حذف فعلي")
        print("   • يمكن استرداد الطلبات المحذوفة من قاعدة البيانات")
        print("   • عملية التعديل تفتح نافذة منفصلة")
        
        print("\n🎯 البيانات التجريبية المعروضة:")
        print("   • PO-2024-001: طلب أجهزة كمبيوتر (جديد)")
        print("   • PO-2024-002: أدوات مكتبية (مؤكد)")
        print("   • PO-2024-003: مواد خام للإنتاج (قيد التنفيذ)")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ فشل العرض التوضيحي: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """الدالة الرئيسية"""
    print("🎬 مرحباً بك في العرض التوضيحي لقائمة الزر الأيمن")
    print("=" * 60)
    
    exit_code = demo_context_menu()
    
    print("\n👋 شكراً لك على تجربة العرض التوضيحي!")
    return exit_code

if __name__ == "__main__":
    sys.exit(main())
