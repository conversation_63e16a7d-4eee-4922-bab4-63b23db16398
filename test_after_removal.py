#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق بعد حذف نظام تعبئة البيانات المفقودة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_window_import():
    """اختبار استيراد النافذة الرئيسية"""
    print("🔍 اختبار استيراد النافذة الرئيسية...")
    
    try:
        from src.ui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد MainWindow: {e}")
        return False

def test_main_window_creation():
    """اختبار إنشاء النافذة الرئيسية"""
    print("🔍 اختبار إنشاء النافذة الرئيسية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        main_window = MainWindow()
        print("✅ تم إنشاء MainWindow بنجاح")
        
        # اختبار القوائم
        menubar = main_window.menuBar()
        menus = [menubar.actionAt(i) for i in range(menubar.actions().__len__())]
        print(f"✅ تم إنشاء {len(menus)} قوائم رئيسية")
        
        # البحث عن قائمة الأدوات
        tools_menu = None
        for action in menubar.actions():
            if action.text() == "الأدوات":
                tools_menu = action.menu()
                break
        
        if tools_menu:
            tools_actions = tools_menu.actions()
            print(f"✅ قائمة الأدوات تحتوي على {len(tools_actions)} عنصر")
            
            # التحقق من عدم وجود نظام تعبئة البيانات المفقودة
            data_filler_found = False
            for action in tools_actions:
                if "تعبئة البيانات المفقودة" in action.text():
                    data_filler_found = True
                    break
            
            if not data_filler_found:
                print("✅ تم حذف نظام تعبئة البيانات المفقودة من قائمة الأدوات بنجاح")
            else:
                print("❌ لا يزال نظام تعبئة البيانات المفقودة موجود في قائمة الأدوات")
                return False
        else:
            print("⚠️ لم يتم العثور على قائمة الأدوات")
        
        main_window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء MainWindow: {e}")
        return False

def test_shipments_window():
    """اختبار نافذة الشحنات"""
    print("🔍 اختبار نافذة الشحنات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.shipments.shipments_window import ShipmentsWindow
        
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        shipments_window = ShipmentsWindow()
        print("✅ تم إنشاء ShipmentsWindow بنجاح")
        
        shipments_window.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ShipmentsWindow: {e}")
        return False

def test_new_shipment_window():
    """اختبار نافذة الشحنة الجديدة"""
    print("🔍 اختبار نافذة الشحنة الجديدة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # اختبار وضع الإنشاء الجديد
        new_window = NewShipmentWindow()
        print("✅ تم إنشاء NewShipmentWindow (وضع الإنشاء) بنجاح")
        new_window.close()
        
        # اختبار وضع التعديل
        from src.database.database_manager import DatabaseManager
        from src.database.models import Shipment
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            shipment = session.query(Shipment).first()
            if shipment:
                edit_window = NewShipmentWindow(shipment_id=shipment.id)
                print("✅ تم إنشاء NewShipmentWindow (وضع التعديل) بنجاح")
                edit_window.close()
            else:
                print("⚠️ لا توجد شحنات لاختبار وضع التعديل")
        finally:
            session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء NewShipmentWindow: {e}")
        return False

def test_archived_components():
    """اختبار المكونات المؤرشفة"""
    print("🔍 اختبار المكونات المؤرشفة...")
    
    try:
        import os
        
        # التحقق من وجود مجلد الأرشيف
        if os.path.exists("archived_components"):
            print("✅ تم إنشاء مجلد archived_components")
            
            # التحقق من وجود الملفات المؤرشفة
            archived_files = [
                "shipment_data_filler_dialog.py",
                "shipment_data_filler.py",
                "README.md"
            ]
            
            for file_name in archived_files:
                file_path = os.path.join("archived_components", file_name)
                if os.path.exists(file_path):
                    print(f"✅ تم أرشفة {file_name}")
                else:
                    print(f"❌ لم يتم العثور على {file_name} في الأرشيف")
                    return False
            
            return True
        else:
            print("❌ لم يتم إنشاء مجلد archived_components")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المكونات المؤرشفة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار التطبيق بعد حذف نظام تعبئة البيانات المفقودة")
    print("=" * 70)
    
    tests = [
        ("اختبار استيراد النافذة الرئيسية", test_main_window_import),
        ("اختبار إنشاء النافذة الرئيسية", test_main_window_creation),
        ("اختبار نافذة الشحنات", test_shipments_window),
        ("اختبار نافذة الشحنة الجديدة", test_new_shipment_window),
        ("اختبار المكونات المؤرشفة", test_archived_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"💥 {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print("-" * 35)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 جميع الاختبارات نجحت! تم حذف النظام بنجاح.")
        print("✅ التطبيق جاهز للاستخدام بدون نظام تعبئة البيانات المفقودة.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    main()
