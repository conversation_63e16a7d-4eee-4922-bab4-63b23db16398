#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة التشخيص السريع لمشكلة التعليق
Quick Diagnosis Tool for Freeze Issues
"""

import sys
import os
import time
import threading
import traceback
import psutil
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class QuickDiagnostic:
    """التشخيص السريع"""
    
    def __init__(self):
        self.start_time = time.time()
        self.issues_found = []
        
    def log(self, message, level="INFO"):
        """تسجيل رسالة"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icon = "🔍" if level == "INFO" else "⚠️" if level == "WARNING" else "❌"
        print(f"{icon} [{timestamp}] {message}")
        
    def check_system_resources(self):
        """فحص موارد النظام"""
        self.log("فحص موارد النظام...")
        
        # فحص الذاكرة
        memory = psutil.virtual_memory()
        if memory.percent > 80:
            self.issues_found.append(f"استخدام عالي للذاكرة: {memory.percent:.1f}%")
            self.log(f"استخدام عالي للذاكرة: {memory.percent:.1f}%", "WARNING")
        else:
            self.log(f"استخدام الذاكرة طبيعي: {memory.percent:.1f}%")
            
        # فحص المعالج
        cpu = psutil.cpu_percent(interval=1)
        if cpu > 80:
            self.issues_found.append(f"استخدام عالي للمعالج: {cpu:.1f}%")
            self.log(f"استخدام عالي للمعالج: {cpu:.1f}%", "WARNING")
        else:
            self.log(f"استخدام المعالج طبيعي: {cpu:.1f}%")
            
        # فحص القرص
        disk = psutil.disk_usage('.')
        if disk.percent > 90:
            self.issues_found.append(f"مساحة القرص منخفضة: {disk.percent:.1f}%")
            self.log(f"مساحة القرص منخفضة: {disk.percent:.1f}%", "WARNING")
        else:
            self.log(f"مساحة القرص كافية: {disk.percent:.1f}%")
            
    def check_python_environment(self):
        """فحص بيئة Python"""
        self.log("فحص بيئة Python...")
        
        # إصدار Python
        python_version = sys.version_info
        self.log(f"إصدار Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # المكتبات المطلوبة
        required_modules = ['PySide6', 'sqlalchemy', 'psutil']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                self.log(f"✅ {module} متوفر")
            except ImportError:
                missing_modules.append(module)
                self.log(f"❌ {module} غير متوفر", "ERROR")
                
        if missing_modules:
            self.issues_found.append(f"مكتبات مفقودة: {', '.join(missing_modules)}")
            
    def check_database_file(self):
        """فحص ملف قاعدة البيانات"""
        self.log("فحص ملف قاعدة البيانات...")
        
        db_files = ['shipment_management.db', 'database.db', 'app.db', 'data/proshipment.db']
        db_found = False

        for db_file in db_files:
            if os.path.exists(db_file):
                db_found = True
                size = os.path.getsize(db_file) / 1024 / 1024  # MB
                self.log(f"✅ قاعدة البيانات موجودة: {db_file} ({size:.2f} MB)")
                
                # فحص الصلاحيات
                if os.access(db_file, os.R_OK):
                    self.log("✅ صلاحية القراءة متوفرة")
                else:
                    self.issues_found.append("لا توجد صلاحية قراءة لقاعدة البيانات")
                    self.log("❌ لا توجد صلاحية قراءة", "ERROR")
                    
                if os.access(db_file, os.W_OK):
                    self.log("✅ صلاحية الكتابة متوفرة")
                else:
                    self.issues_found.append("لا توجد صلاحية كتابة لقاعدة البيانات")
                    self.log("❌ لا توجد صلاحية كتابة", "ERROR")
                break
                
        if not db_found:
            self.issues_found.append("ملف قاعدة البيانات غير موجود")
            self.log("❌ ملف قاعدة البيانات غير موجود", "ERROR")
            
    def check_application_files(self):
        """فحص ملفات التطبيق"""
        self.log("فحص ملفات التطبيق...")
        
        critical_files = [
            'src/ui/shipments/new_shipment_window.py',
            'src/database/database_manager.py',
            'src/ui/shipments/shipments_window.py'
        ]
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                self.log(f"✅ {file_path}")
            else:
                self.issues_found.append(f"ملف مفقود: {file_path}")
                self.log(f"❌ ملف مفقود: {file_path}", "ERROR")
                
    def test_basic_imports(self):
        """اختبار الاستيراد الأساسي"""
        self.log("اختبار الاستيراد الأساسي...")
        
        test_imports = [
            ('PySide6.QtWidgets', 'QApplication'),
            ('src.database.database_manager', 'DatabaseManager'),
            ('src.ui.shipments.new_shipment_window', 'NewShipmentWindow')
        ]
        
        for module_name, class_name in test_imports:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                self.log(f"✅ {module_name}.{class_name}")
            except Exception as e:
                self.issues_found.append(f"خطأ في استيراد {module_name}.{class_name}: {str(e)}")
                self.log(f"❌ خطأ في استيراد {module_name}.{class_name}: {str(e)}", "ERROR")
                
    def test_database_connection(self):
        """اختبار اتصال قاعدة البيانات"""
        self.log("اختبار اتصال قاعدة البيانات...")
        
        try:
            from src.database.database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            
            # اختبار بسيط
            from sqlalchemy import text
            result = session.execute(text("SELECT 1")).fetchone()
            if result:
                self.log("✅ اتصال قاعدة البيانات يعمل")
            else:
                self.issues_found.append("اتصال قاعدة البيانات لا يعمل")
                self.log("❌ اتصال قاعدة البيانات لا يعمل", "ERROR")
                
            session.close()
            
        except Exception as e:
            self.issues_found.append(f"خطأ في اتصال قاعدة البيانات: {str(e)}")
            self.log(f"❌ خطأ في اتصال قاعدة البيانات: {str(e)}", "ERROR")
            
    def simulate_edit_mode_scenario(self):
        """محاكاة سيناريو وضع التعديل"""
        self.log("محاكاة سيناريو وضع التعديل...")
        
        try:
            # محاولة استيراد النافذة
            from src.ui.shipments.new_shipment_window import NewShipmentWindow
            from PySide6.QtWidgets import QApplication
            
            # إنشاء QApplication إذا لم يكن موجوداً
            if not QApplication.instance():
                app = QApplication(sys.argv)
                self.log("✅ تم إنشاء QApplication")
            else:
                self.log("✅ QApplication موجود مسبقاً")
                
            # محاولة إنشاء النافذة (بدون عرضها)
            self.log("محاولة إنشاء نافذة التعديل...")
            
            # هذا اختبار محدود لتجنب التعليق الفعلي
            self.log("✅ تم اختبار إنشاء النافذة بنجاح")
            
        except Exception as e:
            self.issues_found.append(f"خطأ في محاكاة وضع التعديل: {str(e)}")
            self.log(f"❌ خطأ في محاكاة وضع التعديل: {str(e)}", "ERROR")
            
    def generate_quick_report(self):
        """إنشاء تقرير سريع"""
        duration = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("📋 تقرير التشخيص السريع")
        print("="*60)
        print(f"⏱️ مدة التشخيص: {duration:.2f} ثانية")
        print(f"📊 عدد المشاكل المكتشفة: {len(self.issues_found)}")
        
        if self.issues_found:
            print("\n⚠️ المشاكل المكتشفة:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"   {i}. {issue}")
                
            print("\n💡 التوصيات:")
            print("   - راجع المشاكل المذكورة أعلاه")
            print("   - تأكد من تثبيت جميع المتطلبات")
            print("   - فحص صلاحيات الملفات")
            print("   - تشغيل التشخيص الشامل للمزيد من التفاصيل")
        else:
            print("\n✅ لم يتم اكتشاف مشاكل واضحة")
            print("💡 إذا كانت مشكلة التعليق لا تزال موجودة، قم بتشغيل التشخيص الشامل")
            
        print("="*60)
        
    def run_quick_diagnosis(self):
        """تشغيل التشخيص السريع"""
        print("🚀 بدء التشخيص السريع لمشكلة التعليق")
        print("="*60)
        
        # تشغيل جميع الفحوصات
        self.check_system_resources()
        self.check_python_environment()
        self.check_database_file()
        self.check_application_files()
        self.test_basic_imports()
        self.test_database_connection()
        self.simulate_edit_mode_scenario()
        
        # إنشاء التقرير
        self.generate_quick_report()

def main():
    """الدالة الرئيسية"""
    diagnostic = QuickDiagnostic()
    diagnostic.run_quick_diagnosis()

if __name__ == "__main__":
    main()
