تحليل الأداء: السيناريو_الكامل
وقت التنفيذ: 6.040 ثانية
تغيير الذاكرة: +0.02 MB
==================================================
         6071 function calls (5987 primitive calls) in 6.039 seconds

   Ordered by: cumulative time
   List reduced from 292 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        2    0.000    0.000    1.095    0.548 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py:1757(cpu_percent)
        1    0.000    0.000    0.202    0.202 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:65(_handle_freeze_detection)
        1    0.002    0.002    0.139    0.139 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:78(_dump_all_stacks)
       40    0.004    0.000    0.117    0.003 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:31(log_activity)
        7    0.001    0.000    0.096    0.014 E:\project\backup\‏‏ProShipment1\src\database\database_manager.py:20(__init__)
    49/48    0.089    0.002    0.088    0.002 {built-in method builtins.print}
        7    0.000    0.000    0.084    0.012 <string>:1(create_engine)
     21/7    0.002    0.000    0.083    0.012 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py:249(warned)
        7    0.006    0.001    0.083    0.012 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\create.py:92(create_engine)
        6    0.001    0.000    0.064    0.011 E:\project\backup\‏‏ProShipment1\debug_tools\database_monitor.py:59(_check_connections)
        1    0.001    0.001    0.056    0.056 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:106(_check_database_locks)
        5    0.000    0.000    0.050    0.010 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:242(format_stack)
       47    0.009    0.000    0.030    0.001 {method 'strftime' of 'datetime.date' objects}
        5    0.000    0.000    0.024    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:249(extract_stack)
        5    0.000    0.000    0.024    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:431(extract)
        5    0.011    0.002    0.024    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:453(_extract_from_extended_frame_gen)
        5    0.000    0.000    0.024    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:33(format_list)
        5    0.001    0.000    0.023    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:738(format)
       22    0.002    0.000    0.022    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:522(format_frame_summary)
      118    0.005    0.000    0.021    0.000 shibokensupport/signature/loader.py:59(feature_import)


