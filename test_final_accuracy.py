#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لدقة نظام التعبئة التلقائية
"""

import sys
import os

def test_carrier_detection():
    """اختبار تحديد شركة الشحن من رقم الحاوية"""
    
    # قاموس شركات الشحن وبادئات الحاويات - محدث من قاعدة بيانات PIER2PIER
    carrier_prefixes = {
        'COSCO': ['COSU', 'CXDU', 'OOCU', 'CSNU', 'VECU'],  # إضافة CSNU للحاوية المطلوبة
        'MAERSK': ['MSKU', 'TEMU', 'MRKU', 'APMU'],
        'MSC': ['MSCU', 'MEDU'],
        'CMA CGM': ['CMAU', 'CGMU', 'ANNU', 'AMCU', 'APHU', 'APLU', 'APRU', 'APZU'],
        'EVERGREEN': ['EGLV', 'EGHU', 'GESU', 'UGMU'],
        'HAPAG-LLOYD': ['HLBU', 'HLCU', 'HLXU', 'UACU', 'UAEU', 'UASU'],
        'ONE': ['ONEY', 'ONEU', 'NYKU', 'AKLU'],
        'YANG MING': ['YMLU', 'YAMU', 'SUDU', 'YMMU'],
        'HMM': ['HMMU', 'HDMU', 'HYMU'],
        'ZIM': ['ZIMU', 'ZCSU', 'ZILU', 'ZCLU', 'ZMOU'],
        'OOCL': ['OOCU'],  # Orient Overseas Container Line
        'PIL': ['PILU'],   # Pacific International Lines
        'WAN HAI': ['WHLU', 'WHSU'],
        'HYUNDAI': ['HDMU', 'HYMU'],
        'K LINE': ['KOLU'],
        'MOL': ['MOLU'],
        'NYK': ['NYKU'],
        'ARKAS': ['ARKU'],
        'BORCHARD': ['BORU'],
        'UNIFEEDER': ['UNFU', 'BLJU'],
        'SAMSKIP': ['VDMU'],
        'ATLANTIC CONTAINER LINE': ['ACLU'],
        'TEXTAINER': ['TXBU', 'TXGU', 'TXTU', 'AMFU', 'AMZU', 'AXIU', 'WCIU', 'XINU']
    }
    
    def detect_carrier(container_number):
        """تحديد شركة الشحن من رقم الحاوية"""
        if not container_number:
            return "غير محدد"
        
        container_upper = container_number.upper()
        
        for carrier, prefixes in carrier_prefixes.items():
            for prefix in prefixes:
                if container_upper.startswith(prefix):
                    return carrier
        
        return "غير محدد"
    
    # اختبارات الدقة
    test_cases = [
        {
            'container': 'CSNU6166920',
            'expected': 'COSCO',
            'description': 'الحاوية المشكوك فيها - يجب أن تكون COSCO'
        },
        {
            'container': 'OOCU7496892', 
            'expected': 'COSCO',
            'description': 'حاوية COSCO أخرى للتأكيد'
        },
        {
            'container': 'MSKU1234567',
            'expected': 'MAERSK',
            'description': 'حاوية MAERSK للمقارنة'
        }
    ]
    
    print("🎯 اختبار نهائي لدقة نظام التعبئة التلقائية")
    print("=" * 80)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        container = test_case['container']
        expected = test_case['expected']
        description = test_case['description']
        
        print(f"\n📋 اختبار {i}: {description}")
        print(f"📦 رقم الحاوية: {container}")
        print(f"🎯 النتيجة المتوقعة: {expected}")
        
        detected = detect_carrier(container)
        print(f"🔍 الشركة المكتشفة: {detected}")
        
        if detected == expected:
            print("✅ النتيجة صحيحة!")
            status = "PASS"
        else:
            print("❌ النتيجة خاطئة!")
            status = "FAIL"
            all_passed = False
        
        print(f"🏆 حالة الاختبار: {status}")
        print("-" * 80)
    
    print(f"\n🎯 النتيجة النهائية:")
    if all_passed:
        print("✅ جميع الاختبارات نجحت! تم حل مشكلة دقة النظام بنجاح.")
        print("✅ الحاوية CSNU6166920 تُعرف الآن بشكل صحيح كـ COSCO")
        print("✅ النظام جاهز للاستخدام مع دقة محسنة")
    else:
        print("❌ بعض الاختبارات فشلت. يحتاج النظام إلى مراجعة إضافية.")
    
    return all_passed

if __name__ == "__main__":
    success = test_carrier_detection()
    sys.exit(0 if success else 1)
