#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح خطأ إضافة الحاوية
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication, QDialog
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_container_dialog():
    """اختبار نافذة الحاوية"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة الرئيسية
        window = NewShipmentWindow()
        
        print("=== اختبار إصلاح خطأ الحاوية ===")
        
        # التحقق من وجود الأزرار
        if hasattr(window, 'add_container_button'):
            print("✅ زر إضافة الحاوية موجود")
        else:
            print("❌ زر إضافة الحاوية غير موجود")
            return
            
        # التحقق من وجود الوظيفة
        if hasattr(window, 'add_container'):
            print("✅ وظيفة إضافة الحاوية موجودة")
        else:
            print("❌ وظيفة إضافة الحاوية غير موجودة")
            return
            
        # التحقق من استيراد QDialog
        try:
            from PySide6.QtWidgets import QDialog
            print("✅ QDialog مستورد بنجاح")
        except ImportError:
            print("❌ فشل في استيراد QDialog")
            return
            
        # التحقق من وجود ContainerDialog
        try:
            from src.ui.shipments.container_dialog import ContainerDialog
            print("✅ ContainerDialog متوفر")
            
            # اختبار إنشاء النافذة
            dialog = ContainerDialog(window)
            print("✅ تم إنشاء ContainerDialog بنجاح")
            
            # التحقق من QDialog.Accepted
            accepted_value = QDialog.Accepted
            print(f"✅ QDialog.Accepted = {accepted_value}")
            
        except ImportError as e:
            print(f"⚠️ ContainerDialog غير متوفر: {e}")
            print("✅ سيتم استخدام الطريقة البسيطة")
        except Exception as e:
            print(f"❌ خطأ في إنشاء ContainerDialog: {e}")
            return
            
        print("\n🎯 الإصلاح تم بنجاح!")
        print("📋 يمكنك الآن:")
        print("   1. فتح نافذة شحنة جديدة")
        print("   2. الانتقال إلى تبويب الحاويات")
        print("   3. الضغط على زر 'إضافة حاوية'")
        print("   4. ملء البيانات والحفظ")
        
        # عرض النافذة للاختبار اليدوي
        window.show()
        print("\n🔍 النافذة مفتوحة - اختبر إضافة الحاوية يدوياً")
        
        app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_container_dialog()
