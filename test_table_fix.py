#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة QTableWidgetItem
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_table_fix():
    """اختبار إصلاح مشكلة الجدول"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # محاولة إنشاء النافذة
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # محاولة تحميل الطلبات
        window.load_orders()
        print("✅ تم تحميل الطلبات بنجاح")
        
        # التحقق من وجود الجدول
        assert hasattr(window, 'orders_table'), "جدول الطلبات غير موجود"
        print("✅ جدول الطلبات موجود")
        
        print("✅ تم إصلاح مشكلة QTableWidgetItem")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 اختبار إصلاح مشكلة الجدول...")
    
    if test_table_fix():
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ النافذة تعمل بالتصميم الجديد")
        print("✅ لا توجد مشاكل مع QTableWidgetItem")
    else:
        print("\n❌ لا تزال هناك مشاكل")
        sys.exit(1)
