#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتطبيق الرئيسي
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_final_main():
    """اختبار نهائي للتطبيق الرئيسي"""
    print("🎯 اختبار نهائي للتطبيق الرئيسي...")
    
    try:
        # استيراد المكونات الأساسية
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTranslator, QLocale, Qt
        from PySide6.QtGui import QFont
        
        from src.ui.main_window import MainWindow
        from src.database.database_manager import DatabaseManager
        from src.utils.arabic_support import setup_arabic_support
        
        print("✅ تم استيراد جميع المكونات بنجاح")
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication")
        
        # إعداد دعم اللغة العربية
        setup_arabic_support(app)
        print("✅ تم إعداد دعم اللغة العربية")
        
        # إعداد قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ تم إعداد قاعدة البيانات")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        print("✅ تم إنشاء وعرض النافذة الرئيسية")
        
        # اختبار نافذة تعبئة البيانات
        print("🔍 اختبار نافذة تعبئة البيانات...")
        try:
            from src.ui.dialogs.shipment_data_filler_dialog import ShipmentDataFillerDialog
            dialog = ShipmentDataFillerDialog(parent=main_window)
            print("✅ تم إنشاء نافذة تعبئة البيانات بنجاح")
            dialog.close()
            print("✅ تم إغلاق نافذة تعبئة البيانات")
        except Exception as e:
            print(f"❌ خطأ في نافذة تعبئة البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # إغلاق التطبيق
        main_window.close()
        app.quit()
        print("✅ تم إغلاق التطبيق بنجاح")
        
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار النهائي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_main()
    if success:
        print("\n🚀 يمكنك الآن تشغيل التطبيق باستخدام: python main.py")
    else:
        print("\n💥 يوجد مشاكل تحتاج إلى إصلاح")
