# دليل استخدام قائمة الزر الأيمن في طلبات الشراء

## نظرة عامة
تم إضافة قائمة الزر الأيمن (Context Menu) إلى جدول طلبات الشراء في النافذة الرئيسية لتسهيل عمليات التعديل والحذف.

## الميزات الجديدة

### 1. قائمة الزر الأيمن
- **الموقع**: جدول طلبات الشراء في النافذة الرئيسية
- **التفعيل**: النقر بالزر الأيمن على أي طلب في الجدول
- **المحتوى**: قائمة تحتوي على خيارين رئيسيين

### 2. العمليات المتاحة

#### أ) تعديل الطلب
- **الوصف**: فتح نافذة تعديل الطلب المحدد
- **الطريقة**: النقر على "تعديل الطلب" من القائمة
- **النتيجة**: فتح نافذة جديدة لتعديل تفاصيل الطلب

#### ب) حذف الطلب
- **الوصف**: حذف الطلب المحدد من النظام
- **الطريقة**: النقر على "حذف الطلب" من القائمة
- **الحماية**: رسالة تأكيد قبل الحذف
- **نوع الحذف**: حذف منطقي (is_active = False)

## طريقة الاستخدام

### الخطوات:
1. **فتح النافذة الرئيسية** لطلبات الشراء
2. **تحديد الطلب** المراد التعامل معه في الجدول
3. **النقر بالزر الأيمن** على رقم الطلب أو أي خانة في نفس الصف
4. **اختيار العملية** المطلوبة من القائمة المنبثقة:
   - **تعديل الطلب**: لفتح نافذة التعديل
   - **حذف الطلب**: لحذف الطلب (مع التأكيد)

### مثال عملي:
```
الجدول:
+----------+------------+----------+--------+
| رقم الطلب | تاريخ الطلب | المورد   | الحالة  |
+----------+------------+----------+--------+
| PO-001   | 2024-01-15 | مورد أ   | جديد    | ← النقر بالزر الأيمن هنا
| PO-002   | 2024-01-16 | مورد ب   | مؤكد    |
+----------+------------+----------+--------+

القائمة المنبثقة:
┌─────────────────┐
│ تعديل الطلب     │
├─────────────────┤
│ حذف الطلب       │
└─────────────────┘
```

## الأمان والحماية

### حماية الحذف:
- **رسالة تأكيد**: تظهر رسالة تأكيد قبل الحذف
- **نص التحذير**: "هل أنت متأكد من رغبتك في حذف هذا الطلب؟ تحذير: هذه العملية لا يمكن التراجع عنها!"
- **خيارات**: نعم / لا (الافتراضي: لا)

### الحذف المنطقي:
- **المبدأ**: لا يتم حذف البيانات فعلياً من قاعدة البيانات
- **الطريقة**: تعيين `is_active = False`
- **الفائدة**: إمكانية استرداد البيانات لاحقاً إذا لزم الأمر

## التحديثات التقنية

### الملفات المُحدثة:
- `src/ui/suppliers/purchase_orders_window.py`

### الدوال الجديدة:
1. `show_context_menu(position)` - إظهار قائمة الزر الأيمن
2. `edit_order_from_context_menu(order_id)` - تعديل الطلب من القائمة
3. `delete_order_from_context_menu(order_id)` - حذف الطلب من القائمة

### الإعدادات المضافة:
```python
# إعداد قائمة الزر الأيمن
self.orders_table.setContextMenuPolicy(Qt.CustomContextMenu)
self.orders_table.customContextMenuRequested.connect(self.show_context_menu)
```

### الاستيرادات المضافة:
```python
from PySide6.QtWidgets import QMenu
```

## الاختبار

### ملف الاختبار:
- `test_context_menu.py`

### نتائج الاختبار:
- ✅ إعداد Context Menu للجدول
- ✅ دالة إظهار قائمة الزر الأيمن
- ✅ دالة تعديل الطلب من القائمة
- ✅ دالة حذف الطلب من القائمة
- ✅ التأكد من الحذف قبل التنفيذ
- ✅ الحذف المنطقي

## ملاحظات مهمة

1. **التوافق**: الميزة متوافقة مع النظام الحالي
2. **الأداء**: لا تؤثر على أداء النظام
3. **سهولة الاستخدام**: تحسن تجربة المستخدم
4. **الأمان**: محمية بآليات التأكيد والحذف المنطقي

## المشاكل المحتملة وحلولها

### المشكلة: القائمة لا تظهر
**الحل**: التأكد من النقر على صف يحتوي على بيانات

### المشكلة: خطأ في التعديل
**الحل**: التأكد من وجود الطلب في قاعدة البيانات

### المشكلة: خطأ في الحذف
**الحل**: التأكد من صلاحيات قاعدة البيانات

---

**تاريخ التحديث**: 2024-01-15
**الإصدار**: 1.0
**المطور**: نظام إدارة الشحنات
