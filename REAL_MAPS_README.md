# 🌍 نظام الخرائط الحقيقية - ProShipment

## نظرة عامة
تم تطوير نظام خرائط حقيقية متصلة بالإنترنت لعرض الشحنات والموانئ على خرائط العالم الحقيقية باستخدام OpenStreetMap.

## المكتبات المثبتة
تم تثبيت المكتبات التالية بنجاح:

### مكتبات الخرائط الأساسية
- **folium-0.20.0**: لإنشاء خرائط تفاعلية باستخدام OpenStreetMap
- **geopy-2.4.1**: للحصول على الإحداثيات الجغرافية والحسابات
- **branca-0.8.1**: لتوليد HTML/CSS/JS للخرائط
- **xyzservices-2025.4.0**: لخدمات الخرائط المختلفة
- **geographiclib-2.0**: للحسابات الجغرافية والإسقاطات

### مكتبات الدعم
- **matplotlib**: للرسوم البيانية والتصور
- **plotly**: للرسوم التفاعلية
- **dash**: لتطبيقات الويب التفاعلية

## الميزات الجديدة

### 🗺️ خرائط حقيقية متصلة بالإنترنت
- عرض الشحنات على خرائط العالم الحقيقية
- استخدام بيانات OpenStreetMap الحية
- طبقات متعددة للخرائط (أساسية، فاتحة، داكنة)
- تكبير وتصغير تفاعلي

### 🚢 عرض الموانئ والشحنات
- **الموانئ**: عرض جميع الموانئ مع معلومات مفصلة
- **الشحنات**: عرض مواقع الشحنات حسب حالتها
- **المسارات**: رسم مسارات الشحن بين الموانئ
- **الإحصائيات**: عرض إحصائيات شاملة

### 🎨 واجهة مستخدم محسنة
- لوحة تحكم شاملة مع فلاتر البحث
- إحصائيات مباشرة (إجمالي الشحنات، الموانئ، المسارات النشطة)
- خيارات عرض قابلة للتخصيص
- شريط تقدم لتحميل البيانات

### 🌐 التفاعل مع المتصفح
- فتح الخرائط في المتصفح الافتراضي
- حفظ الخرائط كملفات HTML
- تصدير ومشاركة الخرائط
- معلومات تفصيلية لكل عنصر

## كيفية الاستخدام

### الوصول للنظام
1. افتح البرنامج الرئيسي
2. انتقل إلى **الأدوات المساعدة** → **التتبع والخرائط**
3. اختر **🌍 خرائط حقيقية**

### استخدام الخرائط
1. **تحميل البيانات**: سيتم تحميل البيانات تلقائياً عند فتح النافذة
2. **الفلترة**: استخدم فلاتر البحث لتصفية الشحنات حسب الحالة أو الميناء
3. **خيارات العرض**: تحكم في عرض الموانئ، الشحنات، والمسارات
4. **التفاعل**: اضغط على العناصر في الخريطة لعرض المعلومات التفصيلية

### الأزرار والوظائف
- **🔄 تحديث البيانات**: إعادة تحميل البيانات من قاعدة البيانات
- **🌐 فتح في المتصفح**: عرض الخريطة في المتصفح للتفاعل الكامل
- **💾 حفظ الخريطة**: حفظ الخريطة كملف HTML
- **📤 تصدير الخريطة**: تصدير الخريطة للمشاركة

## التقنيات المستخدمة

### الخرائط والجغرافيا
- **OpenStreetMap**: مصدر بيانات الخرائط الأساسي
- **Folium**: مكتبة Python لإنشاء خرائط تفاعلية
- **Leaflet.js**: محرك الخرائط التفاعلية في المتصفح
- **CartoDB**: طبقات خرائط إضافية

### معالجة البيانات
- **Geopy**: تحويل أسماء الأماكن إلى إحداثيات
- **Nominatim**: خدمة الجيوكودينغ من OpenStreetMap
- **GeographicLib**: حسابات المسافات والمسارات

### واجهة المستخدم
- **PySide6**: إطار العمل للواجهة الرسومية
- **Qt Threading**: معالجة متعددة الخيوط لتحميل البيانات
- **HTML/CSS**: تنسيق المعلومات في النوافذ المنبثقة

## الملفات الجديدة

### `src/ui/shipments/real_map_window.py`
الملف الرئيسي للنظام يحتوي على:
- **RealMapWindow**: النافذة الرئيسية للخرائط
- **MapDataLoader**: خيط تحميل البيانات في الخلفية
- وظائف إنشاء وعرض الخرائط التفاعلية

## المشاكل المحلولة

### مشكلة Attribution
- **المشكلة**: `Custom tiles must have an attribution`
- **الحل**: إضافة attribution صحيح لجميع طبقات الخرائط
- **النتيجة**: عمل الخرائط بدون أخطاء

### مشكلة الاستيراد
- **المشكلة**: `ModuleNotFoundError` للنماذج
- **الحل**: تصحيح مسارات الاستيراد للنماذج من `src.database.models`
- **النتيجة**: تحميل البيانات بنجاح

### مشكلة الترميز
- **المشكلة**: مشاكل في حفظ الملفات العربية
- **الحل**: استخدام `encoding='utf-8'` في حفظ ملفات HTML
- **النتيجة**: عرض النصوص العربية بشكل صحيح

## الموانئ المدعومة
النظام يدعم الموانئ التالية مع إحداثياتها الحقيقية:

### الموانئ العربية
- جدة، الدمام، الكويت، دبي، أبوظبي
- الدوحة، المنامة، مسقط
- الإسكندرية، بورسعيد

### الموانئ العالمية
- شنغهاي، سنغافورة، هونغ كونغ
- روتردام، هامبورغ
- لوس أنجلوس، نيويورك

## المميزات التقنية

### الأداء
- تحميل البيانات في خيط منفصل لعدم تجميد الواجهة
- تخزين مؤقت للإحداثيات لتسريع العمليات
- معالجة الأخطاء الشاملة

### الأمان
- تنظيف الملفات المؤقتة عند الإغلاق
- التحقق من صحة البيانات قبل العرض
- معالجة آمنة للاستثناءات

### قابلية التوسع
- إمكانية إضافة موانئ جديدة بسهولة
- دعم طبقات خرائط إضافية
- واجهة قابلة للتخصيص

## الخطوات التالية المقترحة

### تحسينات مستقبلية
1. **تتبع مباشر**: إضافة تحديث مباشر لمواقع الشحنات
2. **طبقات متقدمة**: إضافة طبقات الطقس والمرور البحري
3. **تحليلات متقدمة**: إحصائيات وتقارير تفاعلية
4. **تصدير متقدم**: تصدير كـ PDF أو صور عالية الجودة

### التكامل
1. **APIs خارجية**: ربط مع خدمات تتبع الشحن الحقيقية
2. **قواعد بيانات الموانئ**: ربط مع قواعد بيانات الموانئ العالمية
3. **خدمات الطقس**: إضافة معلومات الطقس البحري

## الدعم والصيانة
- النظام جاهز للاستخدام الفوري
- يتطلب اتصال إنترنت لتحميل بيانات الخرائط
- يحفظ الخرائط محلياً للعرض دون اتصال

---
**تم التطوير بنجاح ✅**
**النظام جاهز للاستخدام 🚀**
