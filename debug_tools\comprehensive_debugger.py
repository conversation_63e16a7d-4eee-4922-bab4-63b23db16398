#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة التشخيص الشاملة لحل مشكلة التعليق
Comprehensive Debugger for Freeze Issue Resolution
"""

import sys
import os
import time
import threading
from datetime import datetime
import argparse

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# استيراد أدوات التشخيص
from performance_analyzer import analyzer
from edit_mode_debugger import debugger
from database_monitor import db_monitor

class ComprehensiveDebugger:
    """المشخص الشامل"""
    
    def __init__(self):
        self.debug_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results = {}
        
    def log(self, message):
        """تسجيل رسالة"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"🔧 [{timestamp}] {message}")
        
    def run_full_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        self.log("🚀 بدء التشخيص الشامل لمشكلة التعليق")
        self.log("=" * 60)
        
        # المرحلة 1: تحليل الأداء العام
        self.log("📊 المرحلة 1: تحليل الأداء العام")
        self._run_performance_analysis()
        
        # المرحلة 2: تشخيص وضع التعديل
        self.log("🔍 المرحلة 2: تشخيص وضع التعديل")
        self._run_edit_mode_diagnosis()
        
        # المرحلة 3: مراقبة قاعدة البيانات
        self.log("🗄️ المرحلة 3: مراقبة قاعدة البيانات")
        self._run_database_monitoring()
        
        # المرحلة 4: اختبار السيناريو الكامل
        self.log("🎯 المرحلة 4: اختبار السيناريو الكامل")
        self._run_full_scenario_test()
        
        # إنشاء التقرير النهائي
        self.log("📋 إنشاء التقرير النهائي")
        final_report = self._generate_final_report()
        
        self.log("✅ تم الانتهاء من التشخيص الشامل")
        self.log(f"📄 التقرير النهائي: {final_report}")
        
        return final_report
        
    def _run_performance_analysis(self):
        """تشغيل تحليل الأداء"""
        try:
            self.log("بدء مراقبة الأداء...")
            analyzer.start_monitoring()
            
            # محاكاة عمليات مختلفة
            with analyzer.profile_code("اختبار_الاستيراد"):
                from src.ui.shipments.new_shipment_window import NewShipmentWindow
                
            with analyzer.profile_code("اختبار_قاعدة_البيانات"):
                from src.database.database_manager import DatabaseManager
                db_manager = DatabaseManager()
                session = db_manager.get_session()
                session.close()
                
            time.sleep(3)  # مراقبة لفترة
            
            analyzer.stop_monitoring()
            analyzer.analyze_database_connections()
            analyzer.detect_deadlocks()
            
            self.results['performance'] = "مكتمل"
            self.log("✅ تم تحليل الأداء")
            
        except Exception as e:
            self.log(f"❌ خطأ في تحليل الأداء: {e}")
            self.results['performance'] = f"خطأ: {e}"
            
    def _run_edit_mode_diagnosis(self):
        """تشغيل تشخيص وضع التعديل"""
        try:
            self.log("بدء تشخيص وضع التعديل...")
            debugger.start_freeze_detection()
            
            # محاكاة عمليات وضع التعديل
            with debugger.monitor_operation("اختبار_فتح_التعديل"):
                from src.ui.shipments.new_shipment_window import NewShipmentWindow
                from PySide6.QtWidgets import QApplication
                
                if not QApplication.instance():
                    app = QApplication(sys.argv)
                    
                # محاولة إنشاء نافذة تعديل
                debugger.log_activity("إنشاء نافذة تعديل...")
                
            time.sleep(2)  # مراقبة لفترة
            
            debugger.stop_freeze_detection()
            
            self.results['edit_mode'] = "مكتمل"
            self.log("✅ تم تشخيص وضع التعديل")
            
        except Exception as e:
            self.log(f"❌ خطأ في تشخيص وضع التعديل: {e}")
            self.results['edit_mode'] = f"خطأ: {e}"
            
    def _run_database_monitoring(self):
        """تشغيل مراقبة قاعدة البيانات"""
        try:
            self.log("بدء مراقبة قاعدة البيانات...")
            db_monitor.start_monitoring()
            
            # تحليل الأداء
            db_monitor.analyze_database_performance()
            
            # اختبار العمليات
            db_monitor.test_database_operations()
            
            time.sleep(3)  # مراقبة لفترة
            
            db_monitor.stop_monitoring()
            
            self.results['database'] = "مكتمل"
            self.log("✅ تم مراقبة قاعدة البيانات")
            
        except Exception as e:
            self.log(f"❌ خطأ في مراقبة قاعدة البيانات: {e}")
            self.results['database'] = f"خطأ: {e}"
            
    def _run_full_scenario_test(self):
        """تشغيل اختبار السيناريو الكامل"""
        try:
            self.log("بدء اختبار السيناريو الكامل...")
            
            # تشغيل جميع المراقبات
            analyzer.start_monitoring()
            debugger.start_freeze_detection()
            db_monitor.start_monitoring()
            
            # محاكاة السيناريو الكامل
            with analyzer.profile_code("السيناريو_الكامل"):
                with debugger.monitor_operation("فتح_وتعديل_وحفظ"):
                    with db_monitor.monitor_query("عملية_كاملة"):
                        
                        self.log("محاكاة فتح نافذة التعديل...")
                        time.sleep(1)
                        
                        self.log("محاكاة تحميل البيانات...")
                        time.sleep(1)
                        
                        self.log("محاكاة التعديل...")
                        time.sleep(1)
                        
                        self.log("محاكاة الحفظ...")
                        time.sleep(2)  # الحفظ قد يستغرق وقتاً أطول
                        
                        self.log("محاكاة الإغلاق...")
                        time.sleep(1)
                        
            # إيقاف المراقبات
            analyzer.stop_monitoring()
            debugger.stop_freeze_detection()
            db_monitor.stop_monitoring()
            
            self.results['full_scenario'] = "مكتمل"
            self.log("✅ تم اختبار السيناريو الكامل")
            
        except Exception as e:
            self.log(f"❌ خطأ في اختبار السيناريو الكامل: {e}")
            self.results['full_scenario'] = f"خطأ: {e}"
            
    def _generate_final_report(self):
        """إنشاء التقرير النهائي"""
        report_file = f"debug_tools/comprehensive_report_{self.debug_session_id}.txt"
        
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("التقرير الشامل لتشخيص مشكلة التعليق\n")
            f.write("Comprehensive Freeze Issue Diagnosis Report\n")
            f.write("=" * 80 + "\n")
            f.write(f"جلسة التشخيص: {self.debug_session_id}\n")
            f.write(f"التاريخ والوقت: {datetime.now()}\n\n")
            
            # ملخص النتائج
            f.write("📊 ملخص النتائج:\n")
            f.write("-" * 40 + "\n")
            for test_name, result in self.results.items():
                status = "✅" if result == "مكتمل" else "❌"
                f.write(f"{status} {test_name}: {result}\n")
            f.write("\n")
            
            # التوصيات
            f.write("💡 التوصيات:\n")
            f.write("-" * 40 + "\n")
            
            if any("خطأ" in str(result) for result in self.results.values()):
                f.write("⚠️ تم اكتشاف أخطاء في التشخيص:\n")
                for test_name, result in self.results.items():
                    if "خطأ" in str(result):
                        f.write(f"   - {test_name}: {result}\n")
                f.write("\n")
                
            f.write("🔧 خطوات الحل المقترحة:\n")
            f.write("1. فحص سجلات التشخيص التفصيلية\n")
            f.write("2. تحليل نقاط الاختناق في الأداء\n")
            f.write("3. فحص أقفال قاعدة البيانات\n")
            f.write("4. تحسين إدارة الجلسات\n")
            f.write("5. تطبيق الحلول المقترحة\n\n")
            
            # ملفات السجلات
            f.write("📁 ملفات السجلات المرتبطة:\n")
            f.write("-" * 40 + "\n")
            f.write("- performance_analyzer logs\n")
            f.write("- edit_mode_debugger logs\n")
            f.write("- database_monitor logs\n\n")
            
        return report_file

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="أداة التشخيص الشاملة لمشكلة التعليق")
    parser.add_argument("--mode", choices=["full", "performance", "edit", "database"], 
                       default="full", help="نوع التشخيص")
    
    args = parser.parse_args()
    
    debugger_tool = ComprehensiveDebugger()
    
    if args.mode == "full":
        debugger_tool.run_full_diagnosis()
    elif args.mode == "performance":
        debugger_tool._run_performance_analysis()
    elif args.mode == "edit":
        debugger_tool._run_edit_mode_diagnosis()
    elif args.mode == "database":
        debugger_tool._run_database_monitoring()

if __name__ == "__main__":
    main()
