#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظيفة زر إضافة المرفق في تبويب المستندات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def test_purchase_orders_window():
    """اختبار نافذة طلبات الشراء مع التحديثات الجديدة"""
    try:
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

        # إنشاء نافذة طلب جديد
        window = PurchaseOrdersWindow(mode="entry", maximize_on_start=False)
        window.setWindowTitle("اختبار تبويب المستندات المحدث")

        # التأكد من وجود الأزرار الجديدة
        assert hasattr(window, 'contract_add_attachment_btn'), "زر إضافة مرفق العقد غير موجود"
        assert hasattr(window, 'initial_designs_add_attachment_btn'), "زر إضافة مرفق التصاميم الأولية غير موجود"
        assert hasattr(window, 'final_design_add_attachment_btn'), "زر إضافة مرفق التصميم النهائي غير موجود"
        assert hasattr(window, 'other_attachments_add_attachment_btn'), "زر إضافة مرفق المرفقات الأخرى غير موجود"

        # التأكد من أن الحقول قابلة للتحرير الآن
        assert not window.contract_url_edit.isReadOnly(), "حقل رابط العقد يجب أن يكون قابل للتحرير"
        assert not window.initial_designs_url_edit.isReadOnly(), "حقل رابط التصاميم الأولية يجب أن يكون قابل للتحرير"
        assert not window.final_design_url_edit.isReadOnly(), "حقل رابط التصميم النهائي يجب أن يكون قابل للتحرير"
        assert not window.other_attachments_url_edit.isReadOnly(), "حقل رابط المرفقات الأخرى يجب أن يكون قابل للتحرير"

        # التأكد من النص الافتراضي للأزرار
        assert window.contract_add_attachment_btn.text() == "إضافة مرفق", f"النص الافتراضي خاطئ: {window.contract_add_attachment_btn.text()}"
        assert window.initial_designs_add_attachment_btn.text() == "إضافة مرفق", f"النص الافتراضي خاطئ: {window.initial_designs_add_attachment_btn.text()}"
        assert window.final_design_add_attachment_btn.text() == "إضافة مرفق", f"النص الافتراضي خاطئ: {window.final_design_add_attachment_btn.text()}"
        assert window.other_attachments_add_attachment_btn.text() == "إضافة مرفق", f"النص الافتراضي خاطئ: {window.other_attachments_add_attachment_btn.text()}"

        # اختبار دالة تحديث نص الزر
        window.update_attachment_button_text('contract', 'مرفق')
        assert window.contract_add_attachment_btn.text() == "مرفق", "فشل في تحديث نص زر العقد"

        window.update_attachment_button_text('contract', 'إضافة مرفق')
        assert window.contract_add_attachment_btn.text() == "إضافة مرفق", "فشل في إعادة تعيين نص زر العقد"

        # اختبار التحقق من صحة الروابط
        window.contract_url_edit.setText("https://example.com")
        window.validate_url_input(window.contract_url_edit)
        print("✅ تم اختبار التحقق من صحة الروابط")

        window.contract_url_edit.setText("invalid-url")
        window.validate_url_input(window.contract_url_edit)
        print("✅ تم اختبار التحقق من الروابط غير الصحيحة")

        print("✅ جميع الاختبارات نجحت!")
        print("✅ الحقول أصبحت قابلة للتحرير وتقبل الروابط فقط")
        print("✅ المرفقات منفصلة عن الروابط في نظام إدارة المرفقات")
        print("✅ تعمل دوال التحقق من صحة الروابط بشكل صحيح")

        # عرض النافذة للمراجعة البصرية
        window.show()
        return window

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🧪 بدء اختبار زر إضافة المرفق...")
    
    window = test_purchase_orders_window()
    
    if window:
        print("\n📋 تعليمات الاختبار اليدوي:")
        print("1. تأكد من أن حقول الروابط قابلة للتحرير مباشرة")
        print("2. أدخل رابط صحيح (يبدأ بـ http:// أو https://) وتأكد من تغيير لون الحقل للأخضر")
        print("3. أدخل نص غير صحيح وتأكد من تغيير لون الحقل للأحمر")
        print("4. اضغط على أي زر 'إضافة مرفق' لفتح نافذة إدارة المرفقات")
        print("5. أضف مرفقات في النافذة وتأكد من تغيير اسم الزر إلى 'مرفق'")
        print("6. تأكد من أن المرفقات منفصلة تماماً عن الروابط")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
    else:
        print("❌ فشل في إنشاء النافذة")
        sys.exit(1)

if __name__ == "__main__":
    main()
