#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة ShipmentItem.item_name
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, ShipmentItem, Item, Supplier

def test_shipment_items_access():
    """اختبار الوصول لأسماء الأصناف في الشحنات"""
    print("🧪 اختبار الوصول لأسماء الأصناف في الشحنات...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن شحنة تحتوي على أصناف
        shipments = session.query(Shipment).join(Supplier).filter(
            Shipment.is_active == True
        ).order_by(Shipment.created_at.desc()).limit(3).all()
        
        if not shipments:
            print("   ⚠️ لا توجد شحنات في قاعدة البيانات")
            return True
            
        print(f"📊 تم العثور على {len(shipments)} شحنة للاختبار")
        
        for i, shipment in enumerate(shipments, 1):
            print(f"\n   📦 الشحنة {i}: {shipment.shipment_number}")
            print(f"      - المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}")
            
            # اختبار الوصول للأصناف
            if hasattr(shipment, 'items') and shipment.items:
                print(f"      - عدد الأصناف: {len(shipment.items)}")
                
                item_names = []
                for j, shipment_item in enumerate(shipment.items):
                    try:
                        # اختبار الطريقة الخاطئة (التي كانت تسبب المشكلة)
                        try:
                            wrong_name = shipment_item.item_name
                            print(f"         ❌ الطريقة الخاطئة نجحت (غير متوقع): {wrong_name}")
                        except AttributeError:
                            print(f"         ✅ الطريقة الخاطئة فشلت كما هو متوقع")
                        
                        # اختبار الطريقة الصحيحة
                        if shipment_item.item and shipment_item.item.name:
                            item_name = shipment_item.item.name
                            item_names.append(item_name)
                            print(f"         ✅ الصنف {j+1}: {item_name}")
                            print(f"            - الكمية: {shipment_item.quantity}")
                            print(f"            - سعر الوحدة: {shipment_item.unit_price}")
                        else:
                            print(f"         ⚠️ الصنف {j+1}: بيانات غير مكتملة")
                            
                    except Exception as e:
                        print(f"         ❌ خطأ في الصنف {j+1}: {str(e)}")
                        return False
                
                # اختبار تجميع أسماء الأصناف (كما في الكود الأصلي)
                items_text = ", ".join(item_names[:3])
                if len(item_names) > 3:
                    items_text += f" (+{len(item_names) - 3} أخرى)"
                    
                print(f"      - تفاصيل الأصناف: {items_text}")
                
            else:
                print(f"      - لا توجد أصناف في هذه الشحنة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأصناف: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()

def test_shipment_items_relationship():
    """اختبار العلاقات بين الجداول"""
    print("\n🧪 اختبار العلاقات بين الجداول...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن ShipmentItem مع تحميل العلاقات
        shipment_items = session.query(ShipmentItem).join(
            Shipment
        ).join(
            Item
        ).limit(5).all()
        
        if not shipment_items:
            print("   ⚠️ لا توجد أصناف شحنات في قاعدة البيانات")
            return True
            
        print(f"📊 تم العثور على {len(shipment_items)} صنف شحنة للاختبار")
        
        for i, shipment_item in enumerate(shipment_items, 1):
            print(f"\n   📋 صنف الشحنة {i}:")
            
            # اختبار الوصول للشحنة
            if shipment_item.shipment:
                print(f"      - رقم الشحنة: {shipment_item.shipment.shipment_number}")
            else:
                print(f"      - ❌ لا يمكن الوصول للشحنة")
                return False
            
            # اختبار الوصول للصنف
            if shipment_item.item:
                print(f"      - اسم الصنف: {shipment_item.item.name}")
                print(f"      - كود الصنف: {shipment_item.item.code}")
            else:
                print(f"      - ❌ لا يمكن الوصول للصنف")
                return False
                
            # اختبار بيانات صنف الشحنة
            print(f"      - الكمية: {shipment_item.quantity}")
            print(f"      - سعر الوحدة: {shipment_item.unit_price}")
            print(f"      - السعر الإجمالي: {shipment_item.total_price}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العلاقات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()

if __name__ == "__main__":
    print("=== اختبار إصلاح مشكلة ShipmentItem.item_name ===")
    
    # اختبار العلاقات بين الجداول
    if not test_shipment_items_relationship():
        print("\n❌ فشل في اختبار العلاقات!")
        sys.exit(1)
    
    # اختبار الوصول لأسماء الأصناف
    if not test_shipment_items_access():
        print("\n❌ فشل في اختبار الوصول لأسماء الأصناف!")
        sys.exit(1)
    
    print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
    print("✅ العلاقات بين الجداول: تعمل بشكل صحيح")
    print("✅ الوصول لأسماء الأصناف: تم إصلاحه")
    print("✅ لا يوجد خطأ 'ShipmentItem' object has no attribute 'item_name'")
