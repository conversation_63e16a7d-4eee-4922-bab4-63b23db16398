#!/usr/bin/env python3
"""
اختبار شامل لنظام تعبئة البيانات المحسن مع البحث عبر الإنترنت
Comprehensive test for enhanced data filling system with web scraping
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.utils.shipment_data_filler import ShipmentDataFiller
    from src.services.web_scraping_service import WebScrapingService, ShipmentData
    print("✅ تم تحميل جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل الوحدات: {e}")
    sys.exit(1)

class EnhancedDataFillingTester:
    """فئة اختبار النظام المحسن"""
    
    def __init__(self):
        """تهيئة الاختبار"""
        self.filler = ShipmentDataFiller()
        self.web_scraper = WebScrapingService()
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """تسجيل نتيجة الاختبار"""
        result = {
            'test_name': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   📝 {details}")
    
    def test_basic_functionality(self):
        """اختبار الوظائف الأساسية"""
        print("\n🔧 اختبار الوظائف الأساسية...")
        
        # اختبار تهيئة النظام
        try:
            filler = ShipmentDataFiller()
            self.log_test("تهيئة نظام التعبئة", True, "تم إنشاء الكائن بنجاح")
        except Exception as e:
            self.log_test("تهيئة نظام التعبئة", False, f"خطأ: {e}")
        
        # اختبار تحليل البيانات المفقودة
        try:
            analysis = self.filler.analyze_missing_data()
            total_shipments = analysis.get('total_shipments', 0)
            self.log_test("تحليل البيانات المفقودة", True, 
                         f"تم تحليل {total_shipments} شحنة")
        except Exception as e:
            self.log_test("تحليل البيانات المفقودة", False, f"خطأ: {e}")
        
        # اختبار البحث في قاعدة البيانات
        try:
            similar = self.filler.find_similar_shipments({'shipping_company': 'MSC'})
            self.log_test("البحث في قاعدة البيانات", True, 
                         f"وجد {len(similar)} شحنة مشابهة")
        except Exception as e:
            self.log_test("البحث في قاعدة البيانات", False, f"خطأ: {e}")
    
    def test_web_scraping_service(self):
        """اختبار خدمة البحث عبر الإنترنت"""
        print("\n🌐 اختبار خدمة البحث عبر الإنترنت...")
        
        # اختبار تهيئة الخدمة
        try:
            scraper = WebScrapingService()
            self.log_test("تهيئة خدمة البحث", True, "تم إنشاء الخدمة بنجاح")
        except Exception as e:
            self.log_test("تهيئة خدمة البحث", False, f"خطأ: {e}")
            return
        
        # اختبار إنشاء بيانات الشحنة
        try:
            shipment_data = ShipmentData(
                container_number="MSKU1234567",
                carrier="Maersk",
                status="In Transit"
            )
            self.log_test("إنشاء بيانات الشحنة", True, 
                         f"رقم الحاوية: {shipment_data.container_number}")
        except Exception as e:
            self.log_test("إنشاء بيانات الشحنة", False, f"خطأ: {e}")
        
        # اختبار حساب درجة الثقة
        try:
            confidence = self.web_scraper.calculate_confidence_score(shipment_data)
            self.log_test("حساب درجة الثقة", True, f"درجة الثقة: {confidence:.1f}%")
        except Exception as e:
            self.log_test("حساب درجة الثقة", False, f"خطأ: {e}")
        
        # اختبار التحقق من جودة البيانات
        try:
            quality_issues = self.web_scraper.validate_data_quality(shipment_data)
            issue_count = len(quality_issues)
            self.log_test("التحقق من جودة البيانات", True, 
                         f"عدد المشاكل: {issue_count}")
        except Exception as e:
            self.log_test("التحقق من جودة البيانات", False, f"خطأ: {e}")
    
    async def test_web_search(self):
        """اختبار البحث عبر الإنترنت"""
        print("\n🔍 اختبار البحث عبر الإنترنت...")
        
        if not self.filler.web_scraping_enabled:
            self.log_test("البحث عبر الإنترنت", False, "الخدمة غير متوفرة")
            return
        
        # اختبار البحث برقم الحاوية
        try:
            result = await self.filler.search_web_data(
                container_number="MSKU1234567",
                carrier_name="Maersk"
            )
            
            if result['success']:
                result_count = len(result.get('results', []))
                self.log_test("البحث برقم الحاوية", True, 
                             f"عدد النتائج: {result_count}")
            else:
                self.log_test("البحث برقم الحاوية", False, 
                             result.get('error', 'خطأ غير معروف'))
        except Exception as e:
            self.log_test("البحث برقم الحاوية", False, f"خطأ: {e}")
        
        # اختبار البحث برقم بوليصة الشحن
        try:
            result = await self.filler.search_web_data(
                bill_of_lading="MAEU123456789",
                carrier_name="Maersk"
            )
            
            if result['success']:
                result_count = len(result.get('results', []))
                self.log_test("البحث برقم بوليصة الشحن", True, 
                             f"عدد النتائج: {result_count}")
            else:
                self.log_test("البحث برقم بوليصة الشحن", False, 
                             result.get('error', 'خطأ غير معروف'))
        except Exception as e:
            self.log_test("البحث برقم بوليصة الشحن", False, f"خطأ: {e}")
    
    def test_data_integration(self):
        """اختبار تكامل البيانات"""
        print("\n🔗 اختبار تكامل البيانات...")
        
        # إنشاء بيانات تجريبية
        existing_data = {
            'shipping_company': 'Maersk',
            'container_number': 'MSKU1234567',
            'vessel_name': '',  # فارغ
            'origin_port': '',  # فارغ
        }
        
        web_data = ShipmentData(
            container_number='MSKU1234567',
            carrier='Maersk',
            vessel_name='Maersk Vessel',
            origin_port='Shanghai',
            destination_port='Rotterdam'
        )
        
        try:
            merged_data = self.web_scraper.merge_shipment_data(existing_data, web_data)
            
            # التحقق من الدمج
            filled_fields = []
            for key, value in merged_data.items():
                if existing_data.get(key) == '' and value:
                    filled_fields.append(key)
            
            self.log_test("دمج البيانات", True, 
                         f"تم تعبئة {len(filled_fields)} حقل: {', '.join(filled_fields)}")
        except Exception as e:
            self.log_test("دمج البيانات", False, f"خطأ: {e}")
    
    def test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        # اختبار سرعة تحليل البيانات
        start_time = time.time()
        try:
            analysis = self.filler.analyze_missing_data()
            end_time = time.time()
            duration = end_time - start_time
            
            self.log_test("سرعة تحليل البيانات", True, 
                         f"استغرق {duration:.2f} ثانية")
        except Exception as e:
            self.log_test("سرعة تحليل البيانات", False, f"خطأ: {e}")
        
        # اختبار سرعة البحث في قاعدة البيانات
        start_time = time.time()
        try:
            similar = self.filler.find_similar_shipments({'shipping_company': 'MSC'})
            end_time = time.time()
            duration = end_time - start_time
            
            self.log_test("سرعة البحث في قاعدة البيانات", True, 
                         f"استغرق {duration:.2f} ثانية لإيجاد {len(similar)} نتيجة")
        except Exception as e:
            self.log_test("سرعة البحث في قاعدة البيانات", False, f"خطأ: {e}")
    
    def generate_report(self):
        """إنشاء تقرير الاختبار"""
        print("\n" + "="*60)
        print("📊 تقرير الاختبار الشامل")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📈 إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {passed_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"📊 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ الاختبارات الفاشلة:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['test_name']}: {result['details']}")
        
        print("\n" + "="*60)
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests/total_tests)*100,
            'results': self.test_results
        }

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء الاختبار الشامل لنظام تعبئة البيانات المحسن")
    print("="*60)
    
    tester = EnhancedDataFillingTester()
    
    # تشغيل الاختبارات
    tester.test_basic_functionality()
    tester.test_web_scraping_service()
    await tester.test_web_search()
    tester.test_data_integration()
    tester.test_performance()
    
    # إنشاء التقرير
    report = tester.generate_report()
    
    # حفظ التقرير
    try:
        import json
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print("💾 تم حفظ التقرير في test_report.json")
    except Exception as e:
        print(f"⚠️ لم يتم حفظ التقرير: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ في تشغيل الاختبار: {e}")
