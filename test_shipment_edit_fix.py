#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة رسالة "رقم الشحنة موجود مسبقاً" عند التعديل
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def test_shipment_edit_fix():
    """اختبار إصلاح مشكلة التعديل"""
    
    print("🔧 بدء اختبار إصلاح مشكلة رقم الشحنة عند التعديل...")
    
    try:
        # استيراد النافذة
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        from src.database.database_manager import DatabaseManager
        from src.database.models import Shipment
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # محاولة العثور على شحنة موجودة للاختبار
        session = db_manager.get_session()
        try:
            existing_shipment = session.query(Shipment).first()
            if existing_shipment:
                shipment_id = existing_shipment.id
                shipment_number = existing_shipment.shipment_number
                print(f"✅ تم العثور على شحنة للاختبار: ID={shipment_id}, Number={shipment_number}")
            else:
                print("⚠️ لا توجد شحنات في قاعدة البيانات للاختبار")
                # إنشاء شحنة تجريبية
                test_shipment = Shipment(
                    shipment_number="TEST-2025-001",
                    supplier_invoice="TEST-INV-001"
                )
                session.add(test_shipment)
                session.commit()
                shipment_id = test_shipment.id
                shipment_number = test_shipment.shipment_number
                print(f"✅ تم إنشاء شحنة تجريبية: ID={shipment_id}, Number={shipment_number}")
        finally:
            session.close()
        
        # اختبار 1: إنشاء نافذة في وضع التعديل
        print("\n📝 اختبار 1: إنشاء نافذة في وضع التعديل...")
        edit_window = NewShipmentWindow(shipment_id=shipment_id)
        
        # التحقق من أن النافذة في وضع التعديل
        if edit_window.is_edit_mode:
            print("✅ النافذة في وضع التعديل")
        else:
            print("❌ النافذة ليست في وضع التعديل")
        
        # التحقق من أن معرف الشحنة محفوظ
        if edit_window.current_shipment_id == shipment_id:
            print(f"✅ معرف الشحنة محفوظ: {edit_window.current_shipment_id}")
        else:
            print(f"❌ معرف الشحنة غير صحيح: {edit_window.current_shipment_id}")
        
        # اختبار 2: التحقق من رقم الشحنة
        print("\n🔍 اختبار 2: التحقق من دالة فحص رقم الشحنة...")
        
        # محاكاة رقم الشحنة الحالي
        edit_window.shipment_number_edit.setText(shipment_number)
        
        # اختبار دالة التحقق
        is_unique = edit_window.check_shipment_number_unique()
        
        if is_unique:
            print("✅ دالة التحقق تعمل بشكل صحيح - لا تظهر رسالة خطأ للشحنة الحالية")
        else:
            print("❌ دالة التحقق لا تعمل بشكل صحيح - تظهر رسالة خطأ للشحنة الحالية")
        
        # اختبار 3: اختبار رقم شحنة مختلف
        print("\n🔄 اختبار 3: اختبار رقم شحنة مختلف...")
        
        # تغيير رقم الشحنة إلى رقم مختلف
        edit_window.shipment_number_edit.setText("DIFFERENT-2025-999")
        
        # اختبار دالة التحقق مع رقم مختلف
        is_unique_different = edit_window.check_shipment_number_unique()
        
        if is_unique_different:
            print("✅ دالة التحقق تعمل بشكل صحيح مع رقم مختلف")
        else:
            print("❌ دالة التحقق لا تعمل بشكل صحيح مع رقم مختلف")
        
        # اختبار 4: عرض النافذة
        print("\n🖥️ اختبار 4: عرض النافذة للاختبار اليدوي...")
        
        # إعادة تعيين رقم الشحنة الأصلي
        edit_window.shipment_number_edit.setText(shipment_number)
        
        # عرض النافذة
        edit_window.show()
        
        print("\n✅ تم إكمال جميع الاختبارات!")
        print("\n📋 ملخص الإصلاحات:")
        print("   • تم تبسيط منطق التحقق من رقم الشحنة")
        print("   • تم إزالة المنطق المكرر والمتضارب")
        print("   • في وضع التعديل، يتم استثناء الشحنة الحالية من فحص التكرار")
        print("   • لا تظهر رسالة خطأ عند تعديل شحنة بنفس رقمها الأصلي")
        
        print("\n🧪 للاختبار اليدوي:")
        print("   1. جرب تعديل الشحنة بنفس رقمها - لا يجب أن تظهر رسالة")
        print("   2. جرب تغيير رقم الشحنة إلى رقم موجود - يجب أن تظهر رسالة")
        print("   3. جرب تغيير رقم الشحنة إلى رقم جديد - لا يجب أن تظهر رسالة")
        
        return edit_window
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        window = test_shipment_edit_fix()
        if window:
            sys.exit(app.exec())
        else:
            print("❌ فشل في إنشاء النافذة")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ خطأ في التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
