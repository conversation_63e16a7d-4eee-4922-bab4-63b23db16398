# نظام إدارة كميات طلبات الشراء

## نظرة عامة

تم تطوير نظام شامل لإدارة كميات طلبات الشراء يقوم بتتبع الكميات المطلوبة والمسلمة والمتبقية تلقائياً عند إنشاء الشحنات.

## الميزات الرئيسية

### 1. تحديث الكميات التلقائي
- **الوصف**: عند إنشاء شحنة جديدة من طلب شراء، يتم تحديث كميات طلب الشراء تلقائياً
- **الآلية**: 
  - زيادة الكمية المسلمة (`delivered_quantity`)
  - تقليل الكمية المتبقية (`remaining_quantity`)
  - الحفاظ على الكمية المطلوبة الأصلية (`quantity`)

### 2. التحقق من الكميات المتاحة
- **الوصف**: منع شحن كميات تتجاوز الكمية المتبقية في طلب الشراء
- **الآلية**: التحقق من الكميات قبل حفظ الشحنة وعرض رسالة خطأ في حالة التجاوز

### 3. استعادة الكميات عند التعديل/الحذف
- **الوصف**: استعادة الكميات المشحونة إلى طلب الشراء عند تعديل أو حذف الشحنات
- **الآلية**: إعادة حساب الكميات المسلمة والمتبقية

### 4. ربط الشحنات بطلبات الشراء
- **الوصف**: ربط كل صنف في الشحنة بالصنف المقابل في طلب الشراء
- **الآلية**: حفظ `purchase_order_item_id` في جدول `shipment_items`

## التغييرات التقنية

### 1. قاعدة البيانات

#### إضافة حقل الربط
```sql
ALTER TABLE shipment_items 
ADD COLUMN purchase_order_item_id INTEGER
```

#### الحقول الموجودة في `purchase_order_items`
- `delivered_quantity`: الكمية المسلمة
- `remaining_quantity`: الكمية المتبقية

### 2. نموذج البيانات

#### ShipmentItem
```python
class ShipmentItem(Base):
    # ... الحقول الموجودة ...
    purchase_order_item_id = Column(Integer, ForeignKey('purchase_order_items.id'), nullable=True)
    purchase_order_item = relationship("PurchaseOrderItem")
```

### 3. الدوال الجديدة

#### `update_purchase_order_quantities`
```python
def update_purchase_order_quantities(self, session, purchase_order_item_id, shipped_quantity):
    """تحديث كميات طلب الشراء عند الشحن"""
```

#### `restore_purchase_order_quantities`
```python
def restore_purchase_order_quantities(self, session, shipment_id):
    """استعادة كميات طلب الشراء عند التعديل"""
```

#### `validate_purchase_order_quantities`
```python
def validate_purchase_order_quantities(self, session):
    """التحقق من صحة كميات طلب الشراء قبل الحفظ"""
```

## سير العمل

### 1. إنشاء شحنة جديدة من طلب شراء

1. **تحميل البيانات**: تحميل أصناف طلب الشراء في نافذة الشحنة الجديدة
2. **حفظ الربط**: حفظ `purchase_order_item_id` لكل صنف
3. **التحقق من الكميات**: التأكد من عدم تجاوز الكميات المتاحة
4. **تحديث الكميات**: تحديث `delivered_quantity` و `remaining_quantity`
5. **حفظ الشحنة**: حفظ بيانات الشحنة مع الربط

### 2. تعديل شحنة موجودة

1. **استعادة الكميات**: استعادة الكميات من الشحنة القديمة
2. **حذف البيانات القديمة**: حذف أصناف الشحنة القديمة
3. **إضافة البيانات الجديدة**: إضافة الأصناف الجديدة
4. **تحديث الكميات**: تحديث كميات طلب الشراء بالبيانات الجديدة

### 3. حذف شحنة

1. **استعادة الكميات**: استعادة جميع الكميات المشحونة
2. **حذف البيانات**: حذف الشحنة وجميع البيانات المرتبطة

## الاختبارات

### 1. اختبار قاعدة البيانات
```bash
python test_purchase_order_quantity_updates.py
```

### 2. اختبار التكامل
```bash
python test_purchase_order_integration.py
```

### 3. اختبار شامل
```bash
python test_complete_purchase_order_system.py
```

## الملفات المتأثرة

### 1. ملفات النماذج
- `src/database/models.py`: إضافة حقل الربط

### 2. ملفات الواجهة
- `src/ui/shipments/new_shipment_window.py`: الدوال الجديدة
- `src/ui/suppliers/purchase_orders_window.py`: عرض الكميات المحدثة

### 3. ملفات الاختبار
- `test_purchase_order_quantity_updates.py`
- `test_purchase_order_integration.py`
- `test_complete_purchase_order_system.py`

### 4. ملفات الترحيل
- `add_purchase_order_link_field.py`

## الاستخدام

### 1. إنشاء طلب شراء
1. افتح نافذة إدارة الموردين
2. انقر على "طلبات الشراء"
3. أنشئ طلب شراء جديد مع الأصناف المطلوبة

### 2. إنشاء شحنة من طلب الشراء
1. افتح نافذة "شحنة جديدة"
2. انقر على "تحميل من طلب شراء"
3. اختر طلب الشراء المطلوب
4. تعديل الكميات حسب الحاجة (لا تتجاوز المتاح)
5. احفظ الشحنة

### 3. مراقبة الكميات
1. افتح نافذة طلبات الشراء
2. اعرض تفاصيل الطلب
3. راجع أعمدة "المسلم" و "المتبقي"

## الفوائد

1. **دقة في التتبع**: تتبع دقيق لحالة تنفيذ طلبات الشراء
2. **منع الأخطاء**: منع شحن كميات تتجاوز المطلوب
3. **تحديث تلقائي**: تحديث الكميات تلقائياً دون تدخل يدوي
4. **مرونة في التعديل**: إمكانية تعديل وحذف الشحنات مع استعادة الكميات
5. **تقارير دقيقة**: إمكانية إنتاج تقارير دقيقة عن حالة الطلبات

## ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية قبل تطبيق التحديثات
2. **الاختبار**: اختبر النظام على بيانات تجريبية قبل الاستخدام الفعلي
3. **التدريب**: تدريب المستخدمين على الميزات الجديدة
4. **المراقبة**: مراقبة النظام في الأيام الأولى للتأكد من عمله بشكل صحيح

## الدعم الفني

في حالة وجود أي مشاكل أو استفسارات:
1. راجع ملفات الاختبار للتأكد من سلامة النظام
2. تحقق من سجلات الأخطاء في وحدة التحكم
3. تأكد من تطبيق جميع تحديثات قاعدة البيانات
