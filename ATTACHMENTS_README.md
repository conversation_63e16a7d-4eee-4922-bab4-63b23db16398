# نظام المرفقات في إدارة الشحنات

## نظرة عامة
تم إضافة نظام شامل لإدارة المرفقات في تبويب المستندات لنافذة الشحنة الجديدة. يتيح هذا النظام للمستخدمين إضافة وإدارة الملفات المرفقة لكل نوع من أنواع المستندات.

## الميزات الجديدة

### 1. أزرار المرفقات
تم إضافة زر "إضافة مرفق" لكل نوع من أنواع المستندات:
- المستندات الأولية
- المستندات (DN)
- المستندات المرسلة للجمارك
- بوليصة الشحن
- صور الأصناف
- مستندات أخرى

### 2. نافذة إدارة المرفقات
نافذة شاملة تتيح:
- **إضافة ملفات**: اختيار ملفات متعددة من النظام
- **عرض الملفات**: قائمة بجميع الملفات المرفقة
- **حذف الملفات**: إزالة الملفات غير المرغوب فيها
- **فتح الملفات**: فتح الملفات بالتطبيق الافتراضي
- **فتح المجلد**: الوصول السريع لمجلد المرفقات
- **معلومات الملف**: عرض تفاصيل الملف المحدد

### 3. تنظيم الملفات
- **مجلدات منفصلة**: كل نوع مستند له مجلد منفصل
- **نسخ آمن**: الملفات تُنسخ إلى مجلد المرفقات
- **أسماء واضحة**: المجلدات تحمل أسماء واضحة باللغة العربية

### 4. قاعدة البيانات
تم إضافة 6 حقول جديدة لحفظ مسارات المرفقات:
- `initial_documents_files`
- `dn_documents_files`
- `customs_documents_files`
- `bill_of_lading_files`
- `items_images_files`
- `other_documents_files`

## كيفية الاستخدام

### إضافة مرفقات
1. افتح نافذة "شحنة جديدة"
2. انتقل إلى تبويب "المستندات"
3. اضغط على زر "إضافة مرفق" بجانب نوع المستند المطلوب
4. في النافذة المنبثقة، اضغط "إضافة ملفات"
5. اختر الملفات المطلوبة من النظام
6. اضغط "موافق" لحفظ المرفقات

### إدارة المرفقات
- **عرض الملفات**: ستظهر جميع الملفات في القائمة
- **حذف ملف**: حدد الملف واضغط "حذف"
- **فتح ملف**: انقر نقراً مزدوجاً أو اضغط "فتح"
- **فتح المجلد**: اضغط "فتح المجلد" للوصول للمجلد

### عداد المرفقات
- الأزرار تعرض عدد المرفقات: "إضافة مرفق (3)"
- اللون الأخضر: لا توجد مرفقات
- اللون البرتقالي: توجد مرفقات

## الملفات المضافة/المحدثة

### ملفات جديدة
- `src/ui/shipments/attachments_manager_dialog.py`: نافذة إدارة المرفقات
- `update_database_for_attachments.py`: سكريبت تحديث قاعدة البيانات
- `test_attachments.py`: ملف اختبار النظام

### ملفات محدثة
- `src/database/models.py`: إضافة حقول المرفقات
- `src/ui/shipments/new_shipment_window.py`: إضافة أزرار ووظائف المرفقات

## هيكل المجلدات
```
attachments/
├── المستندات_الأولية/
├── المستندات_dn/
├── المستندات_المرسلة_للجمارك/
├── بوليصة_الشحن/
├── صور_الأصناف/
└── مستندات_أخرى/
```

## المتطلبات التقنية
- PySide6
- SQLAlchemy
- Python 3.8+
- نظام ملفات يدعم Unicode

## الاختبار
لاختبار النظام:
```bash
python test_attachments.py
```

## ملاحظات مهمة
1. **النسخ الاحتياطي**: يُنصح بعمل نسخة احتياطية من قاعدة البيانات قبل التحديث
2. **المساحة**: تأكد من وجود مساحة كافية لحفظ المرفقات
3. **الصلاحيات**: تأكد من صلاحيات الكتابة في مجلد المشروع
4. **الأمان**: الملفات تُنسخ محلياً ولا تُرفع لخوادم خارجية

## استكشاف الأخطاء
- **خطأ في قاعدة البيانات**: تأكد من تشغيل سكريبت التحديث
- **خطأ في الملفات**: تحقق من صلاحيات المجلد
- **خطأ في الواجهة**: تأكد من تثبيت PySide6

## التطوير المستقبلي
- دعم المعاينة للصور
- ضغط الملفات الكبيرة
- تشفير الملفات الحساسة
- مزامنة مع التخزين السحابي
