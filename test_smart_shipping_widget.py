"""
اختبار الواجهة الذكية لشركات الشحن
"""

import sys
import os
sys.path.append('src')

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

from ui.widgets.smart_shipping_company_widget import SmartShippingCompanyWidget

class TestWindow(QMainWindow):
    """نافذة اختبار الواجهة الذكية"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار الواجهة الذكية لشركات الشحن")
        self.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("🚢 اختبار الواجهة الذكية لشركات الشحن")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # تعليمات
        instructions = QLabel("""
        تعليمات الاختبار:
        • اكتب أسماء شركات شحن مختلفة (مثل: MSC، ماريسك، DHL)
        • جرب أسماء غير صحيحة (مثل: MSK، Maersk Line، فيدكس)
        • لاحظ التحقق التلقائي والاقتراحات
        • انقر على الاقتراحات لتطبيقها
        """)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #7f8c8d; font-size: 12px; padding: 10px;")
        layout.addWidget(instructions)
        
        # الواجهة الذكية
        self.smart_widget = SmartShippingCompanyWidget()
        self.smart_widget.company_selected.connect(self.on_company_selected)
        self.smart_widget.company_validated.connect(self.on_company_validated)
        
        layout.addWidget(self.smart_widget)
        
        # معلومات الحالة
        self.status_label = QLabel("جاهز للاختبار...")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
            }
        """)
        layout.addWidget(self.status_label)
        
        layout.addStretch()
    
    def on_company_selected(self, company_info):
        """عند اختيار شركة"""
        message = f"✅ تم اختيار شركة: {company_info['arabic_name']}\n"
        message += f"   الدولة: {company_info['country']}\n"
        message += f"   النوع: {company_info['type']}"
        self.status_label.setText(message)
        print(f"🏢 شركة محددة: {company_info}")
    
    def on_company_validated(self, validation_result):
        """عند التحقق من الشركة"""
        if validation_result.get('is_valid'):
            confidence = validation_result.get('confidence', 0) * 100
            message = f"✅ شركة صحيحة (ثقة: {confidence:.0f}%)"
        elif validation_result.get('suggestions'):
            best_suggestion = validation_result['suggestions'][0]
            confidence = best_suggestion['similarity'] * 100
            message = f"💡 اقتراح: {best_suggestion['suggested_name']} (ثقة: {confidence:.0f}%)"
        else:
            message = "❌ شركة غير معروفة"
        
        self.status_label.setText(message)
        print(f"🔍 نتيجة التحقق: {validation_result}")

def main():
    """دالة رئيسية للاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه النص للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestWindow()
    window.show()
    
    print("🚀 بدء اختبار الواجهة الذكية لشركات الشحن")
    print("💡 جرب كتابة أسماء شركات مختلفة لاختبار النظام")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
