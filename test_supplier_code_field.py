#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QMessageBox
from PySide6.QtCore import Qt
from src.database.database_manager import DatabaseManager
from src.ui.suppliers.suppliers_data import SuppliersDataWidget

class TestSupplierCodeWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار حقل رقم المورد")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # إضافة واجهة إدارة الموردين
        self.suppliers_widget = SuppliersDataWidget(self.db_manager)
        layout.addWidget(self.suppliers_widget)
        
        # أزرار الاختبار
        test_layout = QVBoxLayout()
        
        # زر اختبار إضافة مورد
        add_test_btn = QPushButton("🧪 اختبار إضافة مورد")
        add_test_btn.clicked.connect(self.test_add_supplier)
        add_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        test_layout.addWidget(add_test_btn)
        
        # زر اختبار التحقق من التكرار
        duplicate_test_btn = QPushButton("🔍 اختبار التحقق من التكرار")
        duplicate_test_btn.clicked.connect(self.test_duplicate_check)
        duplicate_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: black;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        test_layout.addWidget(duplicate_test_btn)
        
        # زر اختبار الاستيراد
        import_test_btn = QPushButton("📥 اختبار الاستيراد")
        import_test_btn.clicked.connect(self.test_import)
        import_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        test_layout.addWidget(import_test_btn)
        
        layout.addLayout(test_layout)
        
        # تحميل البيانات
        self.suppliers_widget.load_data()
        
    def test_add_supplier(self):
        """اختبار إضافة مورد جديد"""
        try:
            # ملء النموذج ببيانات تجريبية
            self.suppliers_widget.code_edit.setText("TEST001")
            self.suppliers_widget.name_edit.setText("مورد اختبار 1")
            self.suppliers_widget.name_en_edit.setText("Test Supplier 1")
            self.suppliers_widget.supplier_type_combo.setCurrentText("شركة")
            self.suppliers_widget.phone_edit.setText("02-1234567")
            self.suppliers_widget.email_edit.setText("<EMAIL>")
            
            # محاولة إضافة المورد
            self.suppliers_widget.add_supplier()
            
            QMessageBox.information(self, "نجح الاختبار", "تم اختبار إضافة المورد بنجاح!")
            
        except Exception as e:
            QMessageBox.critical(self, "فشل الاختبار", f"حدث خطأ أثناء اختبار إضافة المورد:\n{str(e)}")
    
    def test_duplicate_check(self):
        """اختبار التحقق من التكرار"""
        try:
            # ملء النموذج برقم مورد موجود
            self.suppliers_widget.code_edit.setText("TEST001")
            self.suppliers_widget.name_edit.setText("مورد مكرر")
            
            # محاولة إضافة المورد (يجب أن يفشل)
            result = self.suppliers_widget.validate_form()
            
            if not result:
                QMessageBox.information(self, "نجح الاختبار", "تم اكتشاف التكرار بنجاح!")
            else:
                QMessageBox.warning(self, "فشل الاختبار", "لم يتم اكتشاف التكرار!")
                
        except Exception as e:
            QMessageBox.critical(self, "فشل الاختبار", f"حدث خطأ أثناء اختبار التكرار:\n{str(e)}")
    
    def test_import(self):
        """اختبار الاستيراد"""
        try:
            # التحقق من وجود ملف الاختبار
            test_file = "نموذج_استيراد_الموردين_محدث.xlsx"
            if os.path.exists(test_file):
                QMessageBox.information(
                    self, 
                    "ملف الاختبار جاهز", 
                    f"يمكنك الآن اختبار الاستيراد باستخدام الملف:\n{test_file}\n\nاضغط على زر 'استيراد من إكسيل' في الواجهة."
                )
            else:
                QMessageBox.warning(
                    self, 
                    "ملف الاختبار غير موجود", 
                    f"ملف الاختبار غير موجود: {test_file}\nيرجى تشغيل create_supplier_sample_with_code.py أولاً"
                )
                
        except Exception as e:
            QMessageBox.critical(self, "فشل الاختبار", f"حدث خطأ أثناء اختبار الاستيراد:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestSupplierCodeWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
