#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة تعبئة البيانات المبسطة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QWidget, QLabel, QTableWidget, 
                             QPushButton, QLineEdit, QMessageBox, QGroupBox,
                             QFormLayout, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.database.database_manager import DatabaseManager

class SimpleDataFillerDialog(QDialog):
    """نافذة تعبئة البيانات المبسطة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔧 تعبئة البيانات المفقودة - اختبار")
        self.setModal(True)
        self.resize(800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # شريط العنوان
        title_label = QLabel("🔧 نظام تعبئة البيانات المفقودة - اختبار")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # تبويب البحث
        self.create_search_tab()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def create_search_tab(self):
        """إنشاء تبويب البحث"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات التبويب
        info_label = QLabel("🔍 البحث الذكي والتعبئة التلقائية للبيانات المفقودة")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # منطقة البحث
        search_group = QGroupBox("🔍 البحث عن الشحنات")
        search_layout = QFormLayout(search_group)
        
        # حقل رقم الحاوية
        self.search_container_number = QLineEdit()
        self.search_container_number.setPlaceholderText("أدخل رقم الحاوية...")
        search_layout.addRow("رقم الحاوية:", self.search_container_number)
        
        # حقل شركة الشحن
        self.search_shipping_company = QLineEdit()
        self.search_shipping_company.setPlaceholderText("اسم شركة الشحن...")
        search_layout.addRow("شركة الشحن:", self.search_shipping_company)
        
        # زر البحث
        search_button = QPushButton("🔍 بحث")
        search_button.clicked.connect(self.search_shipments)
        search_layout.addRow("", search_button)
        
        layout.addWidget(search_group)
        
        # جدول النتائج
        results_group = QGroupBox("📋 نتائج البحث")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "شركة الشحن", "رقم الحاوية", "الحالة", "التاريخ"
        ])
        results_layout.addWidget(self.results_table)
        
        layout.addWidget(results_group)
        
        self.tab_widget.addTab(tab, "🔍 البحث")
    
    def search_shipments(self):
        """البحث عن الشحنات"""
        container_number = self.search_container_number.text().strip()
        shipping_company = self.search_shipping_company.text().strip()
        
        if not container_number and not shipping_company:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الحاوية أو شركة الشحن")
            return
        
        try:
            # البحث في قاعدة البيانات
            session = self.db_manager.get_session()
            from src.database.models import Shipment
            
            query = session.query(Shipment)
            
            if container_number:
                query = query.filter(Shipment.container_number.like(f"%{container_number}%"))
            
            if shipping_company:
                query = query.filter(Shipment.shipping_company.like(f"%{shipping_company}%"))
            
            shipments = query.limit(50).all()
            
            # عرض النتائج
            self.results_table.setRowCount(len(shipments))
            
            for row, shipment in enumerate(shipments):
                self.results_table.setItem(row, 0, QTableWidgetItem(str(shipment.id)))
                self.results_table.setItem(row, 1, QTableWidgetItem(shipment.shipping_company or ""))
                self.results_table.setItem(row, 2, QTableWidgetItem(shipment.container_number or ""))
                self.results_table.setItem(row, 3, QTableWidgetItem(shipment.status or ""))
                self.results_table.setItem(row, 4, QTableWidgetItem(str(shipment.created_at or "")))
            
            QMessageBox.information(self, "نتائج البحث", f"تم العثور على {len(shipments)} شحنة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث: {str(e)}")
        finally:
            if 'session' in locals():
                session.close()

def test_simple_data_filler():
    """اختبار النافذة المبسطة"""
    print("🧪 اختبار نافذة تعبئة البيانات المبسطة...")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء النافذة
        dialog = SimpleDataFillerDialog()
        
        print("✅ تم إنشاء النافذة المبسطة بنجاح")
        
        # عرض النافذة
        dialog.show()
        
        print("🎉 النافذة تعمل بنجاح!")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_data_filler()
