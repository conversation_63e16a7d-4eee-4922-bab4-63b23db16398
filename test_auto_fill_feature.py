#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة التعبئة التلقائية الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_auto_fill_feature():
    """اختبار ميزة التعبئة التلقائية"""
    print("🤖 اختبار ميزة التعبئة التلقائية")
    print("=" * 60)
    
    try:
        # 1. اختبار استيراد نافذة التعبئة التلقائية
        print("1️⃣ اختبار استيراد نافذة التعبئة التلقائية...")
        sys.path.append('src')
        from ui.dialogs.auto_fill_dialog import AutoFillDialog, AutoFillWorker
        print("   ✅ تم استيراد نافذة التعبئة التلقائية بنجاح")
        
        # 2. اختبار استيراد نافذة الشحنات المحدثة
        print("\n2️⃣ اختبار استيراد نافذة الشحنات المحدثة...")
        from ui.shipments.shipments_window import ShipmentsWindow
        print("   ✅ تم استيراد نافذة الشحنات المحدثة بنجاح")
        
        # 3. اختبار وجود الدالة الجديدة
        print("\n3️⃣ اختبار وجود دالة التعبئة التلقائية...")
        if hasattr(ShipmentsWindow, 'auto_fill_shipment_data'):
            print("   ✅ دالة auto_fill_shipment_data موجودة")
        else:
            print("   ❌ دالة auto_fill_shipment_data غير موجودة")
            return False
        
        # 4. اختبار تحديد شركة الشحن من رقم الحاوية
        print("\n4️⃣ اختبار تحديد شركة الشحن من رقم الحاوية...")
        worker = AutoFillWorker("MSCU1234567", 1)
        
        test_containers = [
            ("MSCU1234567", "MSC"),
            ("MSKU9876543", "MAERSK"),
            ("CMAU5555555", "CMA CGM"),
            ("COSU7777777", "COSCO"),
            ("EGLV3333333", "EVERGREEN"),
            ("HLBU4444444", "HAPAG-LLOYD"),
            ("ONEY2222222", "ONE"),
            ("YMLU8888888", "YANG MING"),
            ("HMMU6666666", "HMM"),
            ("ZIMU1111111", "ZIM"),
            ("ABCD1234567", "غير محدد")
        ]
        
        all_correct = True
        for container_num, expected_carrier in test_containers:
            detected_carrier = worker.detect_carrier_from_container(container_num)
            if detected_carrier == expected_carrier:
                print(f"   ✅ {container_num} -> {detected_carrier}")
            else:
                print(f"   ❌ {container_num} -> {detected_carrier} (متوقع: {expected_carrier})")
                all_correct = False
        
        if all_correct:
            print("   ✅ جميع اختبارات تحديد شركة الشحن نجحت")
        else:
            print("   ⚠️ بعض اختبارات تحديد شركة الشحن فشلت")
        
        # 5. اختبار معالجة النتائج
        print("\n5️⃣ اختبار معالجة نتائج البحث...")
        sample_search_results = {
            'success': True,
            'carrier': 'MSC',
            'container_number': 'MSCU1234567',
            'found_data': {
                'shipping_company': 'Mediterranean Shipping Company',
                'vessel_name': 'MSC OSCAR',
                'voyage_number': '001W',
                'port_of_loading': 'Shanghai',
                'port_of_discharge': 'Rotterdam',
                'bill_of_lading': 'MSCUSHA123456',
                'departure_date': '2024-01-15',
                'arrival_date': '2024-02-20',
                'shipment_status': 'In Transit',
                'container_type': '40HC',
                'container_size': '40',
                'seal_number': 'SL123456',
                'weight': '25000',
                'volume': '67.5'
            }
        }
        
        processed = worker.process_search_results(sample_search_results)
        
        if processed['success']:
            print("   ✅ معالجة النتائج نجحت")
            print(f"   📦 بيانات الشحن: {len(processed['shipping_data'])} حقل")
            print(f"   📋 بيانات الحاوية: {len(processed['container_data'])} حقل")
        else:
            print("   ❌ معالجة النتائج فشلت")
            return False
        
        # 6. اختبار أسماء الحقول للعرض
        print("\n6️⃣ اختبار أسماء الحقول للعرض...")
        dialog = AutoFillDialog()
        
        test_fields = [
            ('shipping_company', 'شركة الشحن'),
            ('vessel_name', 'اسم السفينة'),
            ('voyage_number', 'رقم الرحلة'),
            ('port_of_loading', 'ميناء التحميل'),
            ('port_of_discharge', 'ميناء التفريغ'),
            ('container_type', 'نوع الحاوية'),
            ('container_size', 'حجم الحاوية')
        ]
        
        for field_key, expected_name in test_fields:
            display_name = dialog.get_field_display_name(field_key)
            if display_name == expected_name:
                print(f"   ✅ {field_key} -> {display_name}")
            else:
                print(f"   ❌ {field_key} -> {display_name} (متوقع: {expected_name})")
        
        # النتيجة النهائية
        print("\n" + "=" * 60)
        print("🎉 تم اختبار ميزة التعبئة التلقائية بنجاح!")
        
        print("\n📋 الميزات المضافة:")
        print("   ✅ نافذة التعبئة التلقائية (AutoFillDialog)")
        print("   ✅ عامل البحث الخلفي (AutoFillWorker)")
        print("   ✅ تحديد شركة الشحن من رقم الحاوية")
        print("   ✅ البحث في مواقع شركات الشحن")
        print("   ✅ معالجة وعرض النتائج")
        print("   ✅ تطبيق البيانات على الشحنة")
        print("   ✅ إضافة أمر 'تعبئة تلقائية' لقائمة الزر الأيمن")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. افتح نافذة إدارة الشحنات")
        print("   2. انقر بالزر الأيمن على أي شحنة")
        print("   3. اختر '🤖 تعبئة تلقائية' من القائمة")
        print("   4. ستفتح نافذة البحث التلقائي")
        print("   5. سيتم البحث تلقائياً باستخدام رقم الحاوية")
        print("   6. اختر 'تطبيق البيانات' لحفظ النتائج")
        
        print("\n📝 ملاحظات:")
        print("   • يتطلب وجود رقم حاوية في الشحنة")
        print("   • يبحث في مواقع شركات الشحن المختلفة")
        print("   • يملأ الحقول الفارغة فقط")
        print("   • يدعم جميع شركات الشحن الرئيسية")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_auto_fill_feature()
    if success:
        print("\n✨ ميزة التعبئة التلقائية جاهزة للاستخدام!")
    else:
        print("\n💥 هناك مشاكل تحتاج إلى إصلاح")
