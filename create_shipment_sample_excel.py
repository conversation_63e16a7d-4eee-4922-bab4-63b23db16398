#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف إكسيل نموذجي لاختبار ميزة الاستيراد في شاشة الشحنة الجديدة
"""

import pandas as pd
from datetime import datetime, timed<PERSON>ta

def create_sample_excel():
    """إنشاء ملف إكسيل نموذجي"""
    try:
        # إنشاء بيانات نموذجية للاستيراد
        data = {
            'التاريخ': [datetime.now().strftime('%Y-%m-%d')],
            'المورد': ['شركة الإمارات للتجارة'],
            'بوليصة الشحن': ['BOL-2025-001'],
            'ملاحظات': ['شحنة مستوردة من ملف إكسيل - تحتوي على مواد إلكترونية'],
            'شركة الشحن': ['شركة الخليج للشحن'],
            'رقم DHL': ['DHL123456789'],
            'ميناء الوصول': ['ميناء الملك عبدالعزيز'],
            'تاريخ الوصول المتوقع': [(datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')],
            'رقم الحاوية': ['MSKU1234567']
        }

        # إنشاء DataFrame
        df = pd.DataFrame(data)

        # حفظ الملف
        filename = 'sample_shipment_data.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')

        print(f"✅ تم إنشاء ملف الإكسيل النموذجي: {filename}")
        print("\n📋 محتويات الملف:")
        print(df.to_string(index=False))
        
        return filename
        
    except ImportError as e:
        print("❌ خطأ: مكتبات مطلوبة غير مثبتة")
        print("يرجى تثبيت المكتبات المطلوبة:")
        print("pip install pandas openpyxl")
        return None
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {str(e)}")
        return None

if __name__ == "__main__":
    create_sample_excel()
