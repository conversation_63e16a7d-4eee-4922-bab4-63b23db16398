#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تحليل الأداء والتشخيص الشامل لحل مشكلة التعليق
Performance Analysis and Comprehensive Diagnostic Tool for Freeze Issues
"""

import sys
import os
import time
import traceback
import threading
import psutil
import gc
from datetime import datetime
from contextlib import contextmanager
import cProfile
import pstats
import io
from memory_profiler import profile
import objgraph
from pympler import tracker, muppy, summary

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PerformanceAnalyzer:
    """محلل الأداء الشامل"""
    
    def __init__(self):
        self.start_time = None
        self.memory_tracker = tracker.SummaryTracker()
        self.profiler = None
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
        print("🔍 بدء مراقبة الأداء...")
        
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        print("⏹️ تم إيقاف مراقبة الأداء")
        
    def _monitor_resources(self):
        """مراقبة الموارد في الخلفية"""
        while self.monitoring:
            try:
                # مراقبة الذاكرة
                memory_info = psutil.virtual_memory()
                process = psutil.Process()
                process_memory = process.memory_info()
                
                # مراقبة المعالج
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # طباعة المعلومات
                print(f"💾 الذاكرة: {memory_info.percent:.1f}% | "
                      f"العملية: {process_memory.rss / 1024 / 1024:.1f}MB | "
                      f"🖥️ المعالج: {cpu_percent:.1f}%")
                
                # فحص التعليق المحتمل
                if cpu_percent > 90:
                    print("⚠️ تحذير: استخدام عالي للمعالج!")
                    self._dump_stack_traces()
                    
                if memory_info.percent > 90:
                    print("⚠️ تحذير: استخدام عالي للذاكرة!")
                    self._analyze_memory_usage()
                    
                time.sleep(2)
                
            except Exception as e:
                print(f"خطأ في مراقبة الموارد: {e}")
                break
                
    def _dump_stack_traces(self):
        """تفريغ تتبع المكدس لجميع الخيوط"""
        print("\n📋 تتبع المكدس للخيوط النشطة:")
        for thread_id, frame in sys._current_frames().items():
            print(f"\n🧵 خيط {thread_id}:")
            traceback.print_stack(frame)
            
    def _analyze_memory_usage(self):
        """تحليل استخدام الذاكرة"""
        print("\n🔍 تحليل استخدام الذاكرة:")
        
        # عرض أكثر الكائنات استهلاكاً للذاكرة
        all_objects = muppy.get_objects()
        sum1 = summary.summarize(all_objects)
        summary.print_(sum1, limit=10)
        
        # عرض نمو الكائنات
        self.memory_tracker.print_diff()
        
    @contextmanager
    def profile_code(self, description="كود"):
        """مراقب السياق لتحليل الأداء"""
        print(f"🚀 بدء تحليل: {description}")
        
        # بدء التحليل
        profiler = cProfile.Profile()
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        profiler.enable()
        
        try:
            yield
        finally:
            profiler.disable()
            
            # حساب الإحصائيات
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            execution_time = end_time - start_time
            memory_diff = (end_memory - start_memory) / 1024 / 1024  # MB
            
            print(f"⏱️ وقت التنفيذ: {execution_time:.3f} ثانية")
            print(f"💾 تغيير الذاكرة: {memory_diff:+.2f} MB")
            
            # حفظ تقرير التحليل
            s = io.StringIO()
            ps = pstats.Stats(profiler, stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(20)  # أفضل 20 دالة
            
            # حفظ التقرير في ملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_tools/profile_{description}_{timestamp}.txt"
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"تحليل الأداء: {description}\n")
                f.write(f"وقت التنفيذ: {execution_time:.3f} ثانية\n")
                f.write(f"تغيير الذاكرة: {memory_diff:+.2f} MB\n")
                f.write("="*50 + "\n")
                f.write(s.getvalue())
                
            print(f"💾 تم حفظ التقرير: {filename}")
            
    def analyze_database_connections(self):
        """تحليل اتصالات قاعدة البيانات"""
        print("\n🗄️ تحليل اتصالات قاعدة البيانات:")
        
        try:
            from src.database.database_manager import DatabaseManager
            
            # فحص حالة قاعدة البيانات
            db_manager = DatabaseManager()
            
            # عدد الاتصالات النشطة
            try:
                if hasattr(db_manager.engine.pool, 'checkedout'):
                    checked_out = db_manager.engine.pool.checkedout()
                    active_connections = len(checked_out) if hasattr(checked_out, '__len__') else checked_out
                else:
                    active_connections = 0
                pool_size = db_manager.engine.pool.size() if hasattr(db_manager.engine.pool, 'size') else 0
            except Exception as e:
                print(f"خطأ في فحص الاتصالات: {e}")
                active_connections = 0
                pool_size = 0
            
            print(f"📊 الاتصالات النشطة: {active_connections}")
            print(f"📊 حجم المجموعة: {pool_size}")
            
            # فحص الاستعلامات البطيئة
            print("🐌 فحص الاستعلامات البطيئة...")
            
        except Exception as e:
            print(f"خطأ في تحليل قاعدة البيانات: {e}")
            
    def detect_deadlocks(self):
        """كشف التعليق المحتمل"""
        print("\n🔒 فحص التعليق المحتمل:")
        
        # فحص الخيوط المعلقة
        threads = threading.enumerate()
        print(f"📊 عدد الخيوط النشطة: {len(threads)}")
        
        for thread in threads:
            if thread.is_alive():
                print(f"🧵 {thread.name}: {'نشط' if thread.is_alive() else 'متوقف'}")
                
        # فحص الأقفال
        print("\n🔐 فحص الأقفال:")
        locks = []
        try:
            for obj in gc.get_objects():
                if isinstance(obj, (threading.Lock, threading.RLock, type(threading.Lock()))):
                    locks.append(obj)
        except Exception as e:
            print(f"خطأ في فحص الأقفال: {e}")

        print(f"📊 عدد الأقفال: {len(locks)}")
        
    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"debug_tools/comprehensive_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("تقرير التشخيص الشامل لمشكلة التعليق\n")
            f.write("=" * 60 + "\n")
            f.write(f"التاريخ والوقت: {datetime.now()}\n\n")
            
            # معلومات النظام
            f.write("🖥️ معلومات النظام:\n")
            f.write(f"نظام التشغيل: {psutil.os.name}\n")
            f.write(f"إصدار Python: {sys.version}\n")
            f.write(f"عدد المعالجات: {psutil.cpu_count()}\n")
            f.write(f"إجمالي الذاكرة: {psutil.virtual_memory().total / 1024**3:.2f} GB\n\n")
            
            # معلومات العملية
            process = psutil.Process()
            f.write("📊 معلومات العملية:\n")
            f.write(f"معرف العملية: {process.pid}\n")
            f.write(f"استخدام الذاكرة: {process.memory_info().rss / 1024**2:.2f} MB\n")
            f.write(f"استخدام المعالج: {process.cpu_percent():.2f}%\n")
            f.write(f"عدد الخيوط: {process.num_threads()}\n\n")
            
        print(f"📋 تم إنشاء التقرير الشامل: {report_file}")
        return report_file

# مثيل عام للمحلل
analyzer = PerformanceAnalyzer()

def debug_new_shipment_window():
    """تشخيص مشكلة نافذة الشحنة الجديدة"""
    print("🔍 بدء تشخيص نافذة الشحنة الجديدة...")
    
    analyzer.start_monitoring()
    
    try:
        # محاكاة فتح النافذة للتعديل
        with analyzer.profile_code("فتح_نافذة_التعديل"):
            from src.ui.shipments.new_shipment_window import NewShipmentWindow
            from PySide6.QtWidgets import QApplication
            
            if not QApplication.instance():
                app = QApplication(sys.argv)
            
            # فتح النافذة للتعديل
            window = NewShipmentWindow(edit_mode=True, shipment_id=1)
            window.show()
            
            # محاكاة عملية الحفظ
            with analyzer.profile_code("عملية_الحفظ"):
                # محاكاة الحفظ
                time.sleep(2)  # محاكاة وقت المعالجة
                
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        traceback.print_exc()
        
    finally:
        analyzer.stop_monitoring()
        analyzer.analyze_database_connections()
        analyzer.detect_deadlocks()
        report_file = analyzer.generate_comprehensive_report()
        
        print(f"\n✅ تم الانتهاء من التشخيص")
        print(f"📋 التقرير الشامل: {report_file}")

if __name__ == "__main__":
    debug_new_shipment_window()
