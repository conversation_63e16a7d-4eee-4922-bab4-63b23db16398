#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لميزة استيراد الحاويات المتعددة من الإكسيل
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_parse_multiple_containers():
    """اختبار دالة تحليل الحاويات المتعددة"""
    try:
        print("🔍 اختبار دالة تحليل الحاويات المتعددة...")
        
        # استيراد الفئة
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        # إنشاء مثيل للاختبار
        import os
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        from PySide6.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = NewShipmentWindow()
        
        # اختبارات مختلفة للحاويات
        test_cases = [
            {
                'input': 'MSKU1234567, TCLU9876543, HJMU5555666',
                'expected_count': 3,
                'separator': 'فاصلة عادية'
            },
            {
                'input': 'ABCD1111111; EFGH2222222; IJKL3333333',
                'expected_count': 3,
                'separator': 'فاصلة منقوطة'
            },
            {
                'input': 'MNOP4444444/QRST5555555/UVWX6666666',
                'expected_count': 3,
                'separator': 'خط مائل'
            },
            {
                'input': 'ZYXW7777777',
                'expected_count': 1,
                'separator': 'حاوية واحدة'
            },
            {
                'input': 'CONT1111111|CONT2222222|CONT3333333|CONT4444444',
                'expected_count': 4,
                'separator': 'خط عمودي'
            },
            {
                'input': 'SHIP1111111، SHIP2222222، SHIP3333333',
                'expected_count': 3,
                'separator': 'فاصلة عربية'
            },
            {
                'input': 'LOAD1111111؛ LOAD2222222؛ LOAD3333333',
                'expected_count': 3,
                'separator': 'فاصلة منقوطة عربية'
            },
            {
                'input': 'BULK1111111-BULK2222222-BULK3333333',
                'expected_count': 3,
                'separator': 'شرطة'
            },
            {
                'input': '  TRIM1111111  ,  TRIM2222222  ,  TRIM3333333  ',
                'expected_count': 3,
                'separator': 'مع مسافات إضافية'
            },
            {
                'input': '',
                'expected_count': 0,
                'separator': 'نص فارغ'
            }
        ]
        
        print("\n📋 نتائج الاختبارات:")
        print("=" * 80)
        
        all_passed = True
        for i, test_case in enumerate(test_cases, 1):
            try:
                result = window.parse_multiple_containers(test_case['input'])
                actual_count = len([c for c in result if c.strip()])
                expected_count = test_case['expected_count']
                
                status = "✅ نجح" if actual_count == expected_count else "❌ فشل"
                
                print(f"اختبار {i}: {test_case['separator']}")
                print(f"  المدخل: '{test_case['input']}'")
                print(f"  المتوقع: {expected_count} حاوية")
                print(f"  الفعلي: {actual_count} حاوية")
                print(f"  النتيجة: {status}")
                
                if result:
                    print(f"  الحاويات: {result}")
                
                if actual_count != expected_count:
                    all_passed = False
                    
                print("-" * 40)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {i}: {e}")
                all_passed = False
        
        app.quit()
        
        if all_passed:
            print("🎉 جميع الاختبارات نجحت!")
            return True
        else:
            print("❌ بعض الاختبارات فشلت!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_feature_exists():
    """اختبار وجود الميزة في النافذة"""
    try:
        print("\n🔍 اختبار وجود ميزة الاستيراد...")
        
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        # التحقق من وجود الدوال المطلوبة
        required_methods = [
            'import_from_excel',
            'parse_multiple_containers',
            'add_container_from_import',
            'update_containers_count'
        ]
        
        all_exist = True
        for method in required_methods:
            if hasattr(NewShipmentWindow, method):
                print(f"✅ الدالة {method} موجودة")
            else:
                print(f"❌ الدالة {method} غير موجودة")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزة: {e}")
        return False

def create_test_excel_file():
    """إنشاء ملف إكسيل للاختبار"""
    try:
        print("\n🔄 إنشاء ملف إكسيل للاختبار...")
        
        import pandas as pd
        
        # بيانات اختبار مع حاويات متعددة
        data = {
            'التاريخ': ['2025-07-06'],
            'المورد': ['شركة الاختبار'],
            'بوليصة الشحن': ['TEST-001'],
            'ملاحظات': ['اختبار الحاويات المتعددة'],
            'حالة الشحنة': ['في الطريق'],
            'حالة الإفراج': ['مع الافراج'],
            'شركة الشحن': ['شركة الاختبار للشحن'],
            'رقم DHL': ['TEST123456'],
            'ميناء الوصول': ['ميناء الاختبار'],
            'تاريخ الوصول المتوقع': ['2025-08-06'],
            'رقم الحاوية': ['TEST1111111, TEST2222222, TEST3333333']
        }
        
        df = pd.DataFrame(data)
        filename = 'test_multiple_containers.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف الاختبار: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الاختبار: {e}")
        return None

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لميزة الحاويات المتعددة")
    print("=" * 60)
    
    # اختبار وجود الميزة
    feature_exists = test_import_feature_exists()
    
    if not feature_exists:
        print("❌ الميزة غير مكتملة!")
        return False
    
    # اختبار دالة التحليل
    parsing_works = test_parse_multiple_containers()
    
    if not parsing_works:
        print("❌ دالة التحليل لا تعمل بشكل صحيح!")
        return False
    
    # إنشاء ملف اختبار
    test_file = create_test_excel_file()
    
    if test_file:
        print(f"\n📁 ملف الاختبار جاهز: {test_file}")
    
    print("\n🎯 الميزات الجديدة:")
    print("• دعم الحاويات المتعددة في حقل واحد")
    print("• دعم فواصل متعددة: , ، ; ؛ / | - \\n")
    print("• إزالة المسافات الإضافية تلقائياً")
    print("• إزالة التكرارات")
    print("• معالجة الأخطاء المحسنة")
    print("• رسائل تأكيد محسنة تظهر عدد الحاويات")
    print("• دعم استيراد حالة الشحنة")
    print("• دعم استيراد حالة الإفراج")
    print("• إضافة الحالات الجديدة تلقائياً إذا لم تكن موجودة")
    
    print("\n✅ جميع الاختبارات نجحت! الميزة جاهزة للاستخدام.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
