#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التحسينات الجديدة لطلبات الشراء
- أزرار إدارة المرفقات
- نظام الحالة التفاعلية
- تحديث الحالة التلقائي
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def test_enhancements():
    """اختبار التحسينات الجديدة"""
    
    print("🔧 بدء اختبار التحسينات الجديدة...")
    
    try:
        # استيراد النافذة
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        from src.database.database_manager import DatabaseManager
        
        # إنشاء النافذة في وضع الإدخال
        window = PurchaseOrdersWindow(maximize_on_start=False, mode="entry")
        
        print("✅ تم إنشاء نافذة طلبات الشراء بنجاح")
        
        # اختبار وجود أزرار إدارة المرفقات
        print("\n📎 اختبار أزرار إدارة المرفقات...")
        
        attachments_buttons = [
            "contract_manage_files_btn",
            "initial_designs_manage_files_btn", 
            "final_design_manage_files_btn",
            "other_attachments_manage_files_btn"
        ]
        
        for button_name in attachments_buttons:
            if hasattr(window, button_name):
                button = getattr(window, button_name)
                if button and hasattr(button, 'clicked'):
                    print(f"✅ {button_name}: موجود ومتصل")
                else:
                    print(f"❌ {button_name}: موجود لكن غير متصل")
            else:
                print(f"❌ {button_name}: غير موجود")
        
        # اختبار دوال إدارة المرفقات
        print("\n🔧 اختبار دوال إدارة المرفقات...")
        
        functions_to_test = [
            "manage_file_attachments",
            "add_file_attachment", 
            "view_file_attachments",
            "get_document_path",
            "get_manage_button",
            "open_url"
        ]
        
        for func_name in functions_to_test:
            if hasattr(window, func_name):
                func = getattr(window, func_name)
                if callable(func):
                    print(f"✅ {func_name}: موجودة وقابلة للاستدعاء")
                else:
                    print(f"❌ {func_name}: موجودة لكن غير قابلة للاستدعاء")
            else:
                print(f"❌ {func_name}: غير موجودة")
        
        # اختبار دوال نظام الحالة التفاعلية
        print("\n🎯 اختبار دوال نظام الحالة التفاعلية...")
        
        status_functions = [
            "update_order_status",
            "update_status_color",
            "check_order_completion_status", 
            "auto_update_order_status",
            "mark_order_as_sent",
            "update_item_delivery_status"
        ]
        
        for func_name in status_functions:
            if hasattr(window, func_name):
                func = getattr(window, func_name)
                if callable(func):
                    print(f"✅ {func_name}: موجودة وقابلة للاستدعاء")
                else:
                    print(f"❌ {func_name}: موجودة لكن غير قابلة للاستدعاء")
            else:
                print(f"❌ {func_name}: غير موجودة")
        
        # اختبار تحديث الحالة
        print("\n🔄 اختبار تحديث الحالة...")
        
        try:
            statuses_to_test = ['confirmed', 'sent', 'partial', 'complete']
            for status in statuses_to_test:
                window.update_order_status(status)
                print(f"✅ تحديث الحالة إلى '{status}': نجح")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار تحديث الحالة: {str(e)}")
        
        # اختبار دوال إدارة المستندات
        print("\n📁 اختبار دوال إدارة المستندات...")
        
        try:
            # اختبار الحصول على مسار المستند
            for doc_type in ['contract', 'initial_designs', 'final_design', 'other_attachments']:
                path = window.get_document_path(doc_type)
                print(f"✅ مسار {doc_type}: '{path}'")
                
                button = window.get_manage_button(doc_type)
                if button:
                    print(f"✅ زر إدارة {doc_type}: موجود")
                else:
                    print(f"❌ زر إدارة {doc_type}: غير موجود")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار دوال إدارة المستندات: {str(e)}")
        
        # عرض النافذة
        print("\n🖥️ عرض النافذة للاختبار اليدوي...")
        window.show()
        
        print("\n✅ تم إكمال جميع الاختبارات!")
        print("\n📋 ملخص التحسينات:")
        print("   • أزرار إدارة المرفقات لجميع حقول المستندات")
        print("   • دعم إضافة واستعراض المرفقات") 
        print("   • نظام الحالة التفاعلية مع الألوان")
        print("   • تحديث الحالة التلقائي عند إضافة مستندات")
        print("   • تحديث الحالة عند إرسال الطلب للشحنات")
        print("   • تحديث الحالة بناءً على استخدام الأصناف")
        
        return window
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        window = test_enhancements()
        if window:
            sys.exit(app.exec())
        else:
            print("❌ فشل في إنشاء النافذة")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ خطأ في التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
