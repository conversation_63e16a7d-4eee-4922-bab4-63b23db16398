#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام المرفقات في إدارة الشحنات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.ui.shipments.attachments_manager_dialog import AttachmentsManagerDialog

class TestWindow(QMainWindow):
    """نافذة اختبار نظام المرفقات"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نظام المرفقات")
        self.setGeometry(100, 100, 400, 300)
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # زر اختبار نافذة إدارة المرفقات
        self.test_attachments_button = QPushButton("اختبار نافذة إدارة المرفقات")
        self.test_attachments_button.clicked.connect(self.test_attachments_dialog)
        layout.addWidget(self.test_attachments_button)
        
        # زر اختبار نافذة الشحنة الجديدة
        self.test_shipment_button = QPushButton("اختبار نافذة الشحنة الجديدة")
        self.test_shipment_button.clicked.connect(self.test_shipment_window)
        layout.addWidget(self.test_shipment_button)
        
        # زر إنشاء ملفات تجريبية
        self.create_test_files_button = QPushButton("إنشاء ملفات تجريبية")
        self.create_test_files_button.clicked.connect(self.create_test_files)
        layout.addWidget(self.create_test_files_button)
        
    def test_attachments_dialog(self):
        """اختبار نافذة إدارة المرفقات"""
        try:
            dialog = AttachmentsManagerDialog(self, "اختبار المرفقات", [])
            dialog.exec()
        except Exception as e:
            print(f"خطأ في اختبار نافذة المرفقات: {e}")
    
    def test_shipment_window(self):
        """اختبار نافذة الشحنة الجديدة"""
        try:
            window = NewShipmentWindow()
            window.show()
        except Exception as e:
            print(f"خطأ في اختبار نافذة الشحنة: {e}")
    
    def create_test_files(self):
        """إنشاء ملفات تجريبية للاختبار"""
        try:
            # إنشاء مجلد الاختبار
            test_folder = Path("test_files")
            test_folder.mkdir(exist_ok=True)
            
            # إنشاء ملفات تجريبية
            test_files = [
                "مستند_تجريبي_1.txt",
                "مستند_تجريبي_2.pdf", 
                "صورة_تجريبية.jpg",
                "جدول_بيانات.xlsx",
                "مستند_word.docx"
            ]
            
            for filename in test_files:
                file_path = test_folder / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"هذا ملف تجريبي: {filename}\n")
                    f.write("تم إنشاؤه لاختبار نظام المرفقات\n")
                    f.write("يمكن استخدامه لتجربة إضافة المرفقات\n")
            
            print(f"تم إنشاء {len(test_files)} ملف تجريبي في مجلد: {test_folder}")
            
        except Exception as e:
            print(f"خطأ في إنشاء الملفات التجريبية: {e}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق الستايل العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
