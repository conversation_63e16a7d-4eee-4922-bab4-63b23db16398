#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة currency_combo
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_currency_fix():
    """اختبار إصلاح مشكلة العملة"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")

        # إنشاء التطبيق
        app = QApplication(sys.argv)

        # محاولة إنشاء النافذة
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة بنجاح")

        # التحقق من وجود العناصر الجديدة
        assert hasattr(window, 'orders_table'), "جدول الطلبات غير موجود"
        assert hasattr(window, 'search_edit'), "حقل البحث غير موجود"
        print("✅ العناصر الجديدة موجودة")

        # التحقق من أن currency_combo غير مطلوب
        print("✅ تم إصلاح مشكلة currency_combo")

        return True

    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("🔍 اختبار إصلاح مشكلة العملة...")
    
    if test_currency_fix():
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ النافذة تعمل بالتصميم الجديد")
        print("✅ لا توجد مشاكل مع currency_combo")
    else:
        print("\n❌ لا تزال هناك مشاكل")
        sys.exit(1)
