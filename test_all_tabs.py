#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار جميع التبويبات في نافذة الشحنة الجديدة
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_all_tabs():
    """اختبار جميع التبويبات"""
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = NewShipmentWindow()
    
    print("=== اختبار جميع التبويبات ===")
    print(f"عنوان النافذة: {window.windowTitle()}")
    
    # فحص التبويبات
    if hasattr(window, 'tab_widget'):
        tab_count = window.tab_widget.count()
        print(f"\n📋 عدد التبويبات: {tab_count}")
        
        expected_tabs = [
            "📋 البيانات الأساسية",
            "📦 الأصناف", 
            "💰 البيانات المالية",
            "🚢 الشحن",
            "📦 الحاويات",
            "📄 المستندات"
        ]
        
        print("\n🔍 التبويبات الموجودة:")
        for i in range(tab_count):
            tab_text = window.tab_widget.tabText(i)
            status = "✅" if tab_text in expected_tabs else "⚠️"
            print(f"   {i+1}. {status} {tab_text}")
        
        print(f"\n📊 التبويبات المتوقعة: {len(expected_tabs)}")
        print(f"📊 التبويبات الموجودة: {tab_count}")
        
        if tab_count == len(expected_tabs):
            print("✅ جميع التبويبات موجودة!")
        else:
            print("⚠️ بعض التبويبات مفقودة")
            missing_tabs = []
            existing_tab_texts = [window.tab_widget.tabText(i) for i in range(tab_count)]
            for expected_tab in expected_tabs:
                if expected_tab not in existing_tab_texts:
                    missing_tabs.append(expected_tab)
            
            if missing_tabs:
                print(f"❌ التبويبات المفقودة: {missing_tabs}")
    else:
        print("❌ لا توجد تبويبات في النافذة")
    
    # فحص الأزرار
    print("\n🔘 فحص أزرار التحكم:")
    button_names = ['new_button', 'save_button', 'edit_button', 'exit_button']
    buttons_found = 0
    
    for btn_name in button_names:
        if hasattr(window, btn_name):
            btn = getattr(window, btn_name)
            print(f"   ✅ {btn_name}: {btn.text()}")
            buttons_found += 1
        else:
            print(f"   ❌ {btn_name}: غير موجود")
    
    print(f"\n📊 الأزرار الموجودة: {buttons_found}/4")
    
    # عرض النافذة
    window.show()
    
    print("\n🎯 النتيجة النهائية:")
    if tab_count >= 6 and buttons_found == 4:
        print("🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ النافذة تحتوي على:")
        print("   • جميع التبويبات الستة المطلوبة")
        print("   • أزرار التحكم الأربعة")
        print("   • تصميم احترافي ومنظم")
    elif tab_count >= 6:
        print("✅ تم إضافة جميع التبويبات المفقودة")
        print("⚠️ مشكلة رؤية الأزرار لا تزال موجودة (لكنها وظيفية)")
    else:
        print("❌ لا تزال هناك تبويبات مفقودة")
    
    print(f"\n🔍 للتحقق البصري: تصفح التبويبات الستة في النافذة")
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    test_all_tabs()
