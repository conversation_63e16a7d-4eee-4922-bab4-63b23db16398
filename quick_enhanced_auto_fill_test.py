#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتعبئة التلقائية المحسنة
Quick Enhanced Auto-Fill Test
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_web_scraping_service_fields():
    """اختبار حقول خدمة البحث الجديدة"""
    try:
        from src.services.web_scraping_service import WebScrapingService, ShipmentData
        
        print("✅ تم استيراد WebScrapingService بنجاح")
        
        # التحقق من وجود الحقول الجديدة في ShipmentData
        sample_data = ShipmentData()
        
        new_fields = ['shipping_method', 'shipping_type', 'final_destination']
        missing_fields = []
        
        for field in new_fields:
            if not hasattr(sample_data, field):
                missing_fields.append(field)
        
        if not missing_fields:
            print("✅ جميع الحقول الجديدة موجودة في ShipmentData")
        else:
            print(f"❌ حقول مفقودة: {missing_fields}")
            return False
        
        # اختبار إنشاء بيانات تجريبية
        web_service = WebScrapingService()
        test_data = web_service._create_fallback_data_searates("TEST123", "BL001")
        
        if test_data.shipping_method and test_data.shipping_type and test_data.final_destination:
            print("✅ البيانات التجريبية تحتوي على الحقول الجديدة")
        else:
            print("❌ البيانات التجريبية لا تحتوي على الحقول الجديدة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة البحث: {e}")
        return False

def test_auto_fill_dialog_enhancements():
    """اختبار تحسينات نافذة التعبئة التلقائية"""
    try:
        from src.ui.dialogs.auto_fill_dialog import AutoFillDialog
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تم استيراد AutoFillDialog بنجاح")
        
        # إنشاء نافذة تجريبية
        dialog = AutoFillDialog(parent=None, shipment_id=1, container_number="TEST123")
        
        # التحقق من وجود خانة الاختيار الجديدة
        if hasattr(dialog, 'replace_existing_checkbox'):
            print("✅ خانة استبدال البيانات الموجودة متاحة")
        else:
            print("❌ خانة استبدال البيانات غير موجودة")
            return False
        
        # التحقق من الحالة الافتراضية
        if dialog.replace_existing_checkbox.isChecked():
            print("✅ خانة الاستبدال مفعلة افتراضياً")
        else:
            print("❌ خانة الاستبدال غير مفعلة افتراضياً")
            return False
        
        dialog.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة التعبئة: {e}")
        return False

def test_field_mapping_completeness():
    """اختبار اكتمال تعيين الحقول"""
    try:
        # محاكاة field_mapping المحسن
        field_mapping = {
            # بيانات الشحن الأساسية
            'shipping_company': 'shipping_company',
            'shipping_method': 'shipping_method',
            'shipping_type': 'shipping_type',
            
            # الموانئ والوجهات
            'port_of_loading': 'port_of_loading',
            'port_of_discharge': 'port_of_discharge',
            'final_destination': 'final_destination',
            
            # تعيينات بديلة
            'carrier': 'shipping_company',
            'service_type': 'shipping_method',
            'origin_port': 'port_of_loading',
            'destination_port': 'port_of_discharge',
            'delivery_location': 'final_destination'
        }
        
        # الحقول المطلوبة
        required_fields = [
            'shipping_company', 'shipping_method', 'shipping_type',
            'port_of_loading', 'port_of_discharge', 'final_destination'
        ]
        
        # التحقق من وجود جميع الحقول المطلوبة
        missing_fields = []
        for field in required_fields:
            if field not in field_mapping:
                missing_fields.append(field)
        
        if not missing_fields:
            print("✅ جميع الحقول المطلوبة معينة في field_mapping")
        else:
            print(f"❌ حقول غير معينة: {missing_fields}")
            return False
        
        # التحقق من التعيينات البديلة
        alternative_mappings = ['carrier', 'service_type', 'origin_port', 'destination_port', 'delivery_location']
        missing_alternatives = []
        
        for alt_field in alternative_mappings:
            if alt_field not in field_mapping:
                missing_alternatives.append(alt_field)
        
        if not missing_alternatives:
            print("✅ جميع التعيينات البديلة موجودة")
        else:
            print(f"❌ تعيينات بديلة مفقودة: {missing_alternatives}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تعيين الحقول: {e}")
        return False

def test_files_existence():
    """اختبار وجود الملفات المطورة"""
    files_to_check = [
        "src/ui/dialogs/auto_fill_dialog.py",
        "src/services/web_scraping_service.py",
        "test_enhanced_auto_fill.py",
        "ENHANCED_AUTO_FILL_DOCUMENTATION.md"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} موجود")
        else:
            print(f"❌ {file_path} غير موجود")
            all_exist = False
    
    return all_exist

def main():
    """الدالة الرئيسية للاختبار السريع"""
    print("🔍 اختبار سريع للتعبئة التلقائية المحسنة")
    print("=" * 60)
    
    tests = [
        ("فحص وجود الملفات", test_files_existence),
        ("اختبار حقول خدمة البحث", test_web_scraping_service_fields),
        ("اختبار تحسينات نافذة التعبئة", test_auto_fill_dialog_enhancements),
        ("اختبار اكتمال تعيين الحقول", test_field_mapping_completeness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        print("-" * 50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار السريع:")
    print(f"• الاختبارات الناجحة: {passed}/{total}")
    print(f"• معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 جميع التحسينات تعمل بشكل صحيح!")
        print("📋 الميزات المحسنة:")
        print("• ✅ تعبئة جميع الحقول المطلوبة")
        print("• ✅ خيار استبدال البيانات الموجودة")
        print("• ✅ تعيين شامل للحقول")
        print("• ✅ بيانات محسنة من خدمة البحث")
        print("\n🎯 التعبئة التلقائية المحسنة جاهزة للاستخدام!")
    else:
        print("\n⚠️ بعض التحسينات تحتاج مراجعة")
        print("يرجى مراجعة النتائج أعلاه")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
