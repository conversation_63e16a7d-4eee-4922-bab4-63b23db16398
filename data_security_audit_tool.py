#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص ومراجعة أمان البيانات
Data Security Audit Tool - Comprehensive security analysis
"""

import sys
import os
import hashlib
import sqlite3
from datetime import datetime
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DataSecurityAuditTool:
    """أداة فحص أمان البيانات"""
    
    def __init__(self):
        """تهيئة الأداة"""
        self.audit_log = []
        self.issues_found = []
        self.security_metrics = {}
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الفحص"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status in ["ERROR", "WARNING"]:
            self.issues_found.append({
                'category': category,
                'message': message,
                'status': status,
                'timestamp': timestamp
            })
    
    def check_database_file_security(self):
        """فحص أمان ملف قاعدة البيانات"""
        self.log_audit("أمان ملف قاعدة البيانات", "بدء فحص أمان ملف قاعدة البيانات")
        
        db_files = ["shipment_management.db", "database.db", "data.db"]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                self.log_audit("أمان ملف قاعدة البيانات", f"ملف قاعدة البيانات موجود: {db_file}", "INFO")
                
                # فحص أذونات الملف
                try:
                    file_stat = os.stat(db_file)
                    file_permissions = oct(file_stat.st_mode)[-3:]
                    
                    self.security_metrics[f'{db_file}_permissions'] = file_permissions
                    
                    if file_permissions in ['600', '644']:
                        self.log_audit("أمان ملف قاعدة البيانات", f"أذونات {db_file}: {file_permissions} - آمنة", "SUCCESS")
                    else:
                        self.log_audit("أمان ملف قاعدة البيانات", f"أذونات {db_file}: {file_permissions} - قد تكون غير آمنة", "WARNING")
                    
                    # فحص حجم الملف
                    file_size_mb = file_stat.st_size / (1024 * 1024)
                    self.security_metrics[f'{db_file}_size_mb'] = file_size_mb
                    
                    if file_size_mb < 100:
                        self.log_audit("أمان ملف قاعدة البيانات", f"حجم {db_file}: {file_size_mb:.1f}MB - طبيعي", "SUCCESS")
                    elif file_size_mb < 500:
                        self.log_audit("أمان ملف قاعدة البيانات", f"حجم {db_file}: {file_size_mb:.1f}MB - كبير", "INFO")
                    else:
                        self.log_audit("أمان ملف قاعدة البيانات", f"حجم {db_file}: {file_size_mb:.1f}MB - كبير جداً", "WARNING")
                    
                    # فحص تشفير قاعدة البيانات
                    try:
                        conn = sqlite3.connect(db_file)
                        cursor = conn.cursor()
                        
                        # محاولة قراءة البيانات بدون كلمة مرور
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        
                        if tables:
                            self.log_audit("أمان ملف قاعدة البيانات", f"{db_file} غير مشفرة - يمكن قراءتها بدون كلمة مرور", "WARNING")
                            self.security_metrics[f'{db_file}_encrypted'] = False
                        
                        conn.close()
                        
                    except sqlite3.DatabaseError:
                        self.log_audit("أمان ملف قاعدة البيانات", f"{db_file} قد تكون مشفرة أو تالفة", "INFO")
                        self.security_metrics[f'{db_file}_encrypted'] = True
                        
                except Exception as e:
                    self.log_audit("أمان ملف قاعدة البيانات", f"خطأ في فحص {db_file}: {str(e)}", "ERROR")
            else:
                self.log_audit("أمان ملف قاعدة البيانات", f"ملف قاعدة البيانات غير موجود: {db_file}", "INFO")
    
    def check_sensitive_data_exposure(self):
        """فحص تعرض البيانات الحساسة"""
        self.log_audit("البيانات الحساسة", "بدء فحص تعرض البيانات الحساسة")
        
        # فحص ملفات التكوين
        config_files = [
            "config.py", "settings.py", "database_config.py", 
            ".env", "config.ini", "settings.ini"
        ]
        
        sensitive_patterns = [
            "password", "passwd", "pwd", "secret", "key", "token",
            "api_key", "database_url", "connection_string"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                self.log_audit("البيانات الحساسة", f"فحص ملف التكوين: {config_file}", "INFO")
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read().lower()
                    
                    found_sensitive = []
                    for pattern in sensitive_patterns:
                        if pattern in content:
                            found_sensitive.append(pattern)
                    
                    if found_sensitive:
                        self.log_audit("البيانات الحساسة", 
                                     f"{config_file} يحتوي على بيانات حساسة: {', '.join(found_sensitive)}", 
                                     "WARNING")
                        self.security_metrics[f'{config_file}_sensitive_data'] = found_sensitive
                    else:
                        self.log_audit("البيانات الحساسة", f"{config_file} لا يحتوي على بيانات حساسة واضحة", "SUCCESS")
                        
                except Exception as e:
                    self.log_audit("البيانات الحساسة", f"خطأ في قراءة {config_file}: {str(e)}", "ERROR")
    
    def check_backup_security(self):
        """فحص أمان النسخ الاحتياطية"""
        self.log_audit("أمان النسخ الاحتياطية", "بدء فحص أمان النسخ الاحتياطية")
        
        backup_dirs = ["backup", "backups", "data_backup", "db_backup"]
        backup_files = []
        
        for backup_dir in backup_dirs:
            if os.path.exists(backup_dir):
                self.log_audit("أمان النسخ الاحتياطية", f"مجلد النسخ الاحتياطية موجود: {backup_dir}", "INFO")
                
                try:
                    for root, dirs, files in os.walk(backup_dir):
                        for file in files:
                            if file.endswith(('.db', '.sql', '.bak', '.backup')):
                                backup_files.append(os.path.join(root, file))
                except Exception as e:
                    self.log_audit("أمان النسخ الاحتياطية", f"خطأ في فحص {backup_dir}: {str(e)}", "ERROR")
        
        if backup_files:
            self.log_audit("أمان النسخ الاحتياطية", f"تم العثور على {len(backup_files)} ملف نسخ احتياطية", "INFO")
            self.security_metrics['backup_files_count'] = len(backup_files)
            
            # فحص أذونات ملفات النسخ الاحتياطية
            insecure_backups = 0
            for backup_file in backup_files[:5]:  # فحص أول 5 ملفات فقط
                try:
                    file_stat = os.stat(backup_file)
                    file_permissions = oct(file_stat.st_mode)[-3:]
                    
                    if file_permissions not in ['600', '644']:
                        insecure_backups += 1
                        
                except Exception:
                    pass
            
            if insecure_backups > 0:
                self.log_audit("أمان النسخ الاحتياطية", 
                             f"{insecure_backups} ملف نسخ احتياطية قد تكون غير آمنة", 
                             "WARNING")
            else:
                self.log_audit("أمان النسخ الاحتياطية", "أذونات ملفات النسخ الاحتياطية آمنة", "SUCCESS")
        else:
            self.log_audit("أمان النسخ الاحتياطية", "لم يتم العثور على ملفات نسخ احتياطية", "INFO")
    
    def check_log_file_security(self):
        """فحص أمان ملفات السجلات"""
        self.log_audit("أمان ملفات السجلات", "بدء فحص أمان ملفات السجلات")
        
        log_files = []
        log_dirs = ["logs", "log", "audit"]
        
        # البحث عن ملفات السجلات
        for log_dir in log_dirs:
            if os.path.exists(log_dir):
                try:
                    for root, dirs, files in os.walk(log_dir):
                        for file in files:
                            if file.endswith(('.log', '.txt')):
                                log_files.append(os.path.join(root, file))
                except Exception as e:
                    self.log_audit("أمان ملفات السجلات", f"خطأ في فحص {log_dir}: {str(e)}", "ERROR")
        
        # البحث في المجلد الحالي
        for file in os.listdir('.'):
            if file.endswith('.log'):
                log_files.append(file)
        
        if log_files:
            self.log_audit("أمان ملفات السجلات", f"تم العثور على {len(log_files)} ملف سجل", "INFO")
            self.security_metrics['log_files_count'] = len(log_files)
            
            # فحص محتوى ملفات السجلات للبيانات الحساسة
            sensitive_logs = 0
            for log_file in log_files[:3]:  # فحص أول 3 ملفات فقط
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read(1000).lower()  # قراءة أول 1000 حرف فقط
                    
                    sensitive_patterns = ["password", "secret", "key", "token"]
                    if any(pattern in content for pattern in sensitive_patterns):
                        sensitive_logs += 1
                        
                except Exception:
                    pass
            
            if sensitive_logs > 0:
                self.log_audit("أمان ملفات السجلات", 
                             f"{sensitive_logs} ملف سجل قد يحتوي على بيانات حساسة", 
                             "WARNING")
            else:
                self.log_audit("أمان ملفات السجلات", "ملفات السجلات لا تحتوي على بيانات حساسة واضحة", "SUCCESS")
        else:
            self.log_audit("أمان ملفات السجلات", "لم يتم العثور على ملفات سجلات", "INFO")
    
    def check_code_security(self):
        """فحص أمان الكود"""
        self.log_audit("أمان الكود", "بدء فحص أمان الكود")
        
        # البحث عن ملفات Python
        python_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        if python_files:
            self.log_audit("أمان الكود", f"تم العثور على {len(python_files)} ملف Python", "INFO")
            
            # فحص عينة من الملفات للممارسات غير الآمنة
            unsafe_practices = {
                'eval(': 'استخدام eval() غير آمن',
                'exec(': 'استخدام exec() غير آمن',
                'os.system(': 'استخدام os.system() قد يكون غير آمن',
                'subprocess.call(': 'استخدام subprocess.call() يحتاج مراجعة',
                'password = "': 'كلمة مرور مكتوبة في الكود',
                'secret = "': 'سر مكتوب في الكود'
            }
            
            issues_found = {}
            files_checked = 0
            
            for py_file in python_files[:10]:  # فحص أول 10 ملفات فقط
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    files_checked += 1
                    for pattern, description in unsafe_practices.items():
                        if pattern in content:
                            if description not in issues_found:
                                issues_found[description] = []
                            issues_found[description].append(py_file)
                            
                except Exception:
                    pass
            
            self.security_metrics['python_files_checked'] = files_checked
            self.security_metrics['code_security_issues'] = len(issues_found)
            
            if issues_found:
                for issue, files in issues_found.items():
                    self.log_audit("أمان الكود", f"{issue} في {len(files)} ملف", "WARNING")
            else:
                self.log_audit("أمان الكود", "لم يتم العثور على ممارسات غير آمنة واضحة", "SUCCESS")
        else:
            self.log_audit("أمان الكود", "لم يتم العثور على ملفات Python", "INFO")
    
    def run_complete_audit(self):
        """تشغيل الفحص الشامل"""
        self.log_audit("فحص شامل", "بدء الفحص الشامل لأمان البيانات")
        
        # 1. فحص أمان ملف قاعدة البيانات
        self.check_database_file_security()
        
        # 2. فحص تعرض البيانات الحساسة
        self.check_sensitive_data_exposure()
        
        # 3. فحص أمان النسخ الاحتياطية
        self.check_backup_security()
        
        # 4. فحص أمان ملفات السجلات
        self.check_log_file_security()
        
        # 5. فحص أمان الكود
        self.check_code_security()
        
        # تقرير نهائي
        self.log_audit("تقرير نهائي", "=" * 50)
        
        if not self.issues_found:
            self.log_audit("تقرير نهائي", "✅ لم يتم العثور على مشاكل أمنية", "SUCCESS")
        else:
            self.log_audit("تقرير نهائي", f"⚠️ تم العثور على {len(self.issues_found)} مشكلة أمنية", "WARNING")
            for issue in self.issues_found:
                print(f"[مشكلة أمنية] {issue['category']}: {issue['message']} [{issue['status']}]")
        
        # عرض المقاييس الأمنية
        self.log_audit("مقاييس الأمان", "المقاييس الأمنية:", "INFO")
        for metric, value in self.security_metrics.items():
            self.log_audit("مقاييس الأمان", f"{metric}: {value}", "INFO")
        
        return {
            'success': len(self.issues_found) == 0,
            'issues_count': len(self.issues_found),
            'issues': self.issues_found,
            'audit_log': self.audit_log,
            'security_metrics': self.security_metrics
        }

def main():
    """الدالة الرئيسية"""
    print("🔒 أداة فحص أمان البيانات")
    print("=" * 50)

    try:
        audit_tool = DataSecurityAuditTool()
        result = audit_tool.run_complete_audit()

        print("\n" + "=" * 50)
        if result['success']:
            print("✅ تم اجتياز جميع فحوصات أمان البيانات!")
        else:
            print(f"⚠️ تم العثور على {result['issues_count']} مشكلة أمنية")

        return result
    except Exception as e:
        print(f"❌ خطأ في تشغيل أداة الفحص: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"خطأ عام: {e}")
        import traceback
        traceback.print_exc()
