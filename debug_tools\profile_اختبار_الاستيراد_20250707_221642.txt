تحليل الأداء: اختبار_الاستيراد
وقت التنفيذ: 12.270 ثانية
تغيير الذاكرة: +40.00 MB
==================================================
         555946 function calls (544284 primitive calls) in 12.257 seconds

   Ordered by: cumulative time
   List reduced from 2681 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
    200/1    0.062    0.000   12.269   12.269 <frozen importlib._bootstrap>:1349(_find_and_load)
    457/3    0.046    0.000   12.260    4.087 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
    196/4    0.013    0.000   12.230    3.058 <frozen importlib._bootstrap>:911(_load_unlocked)
    167/4    0.006    0.000   12.224    3.056 <frozen importlib._bootstrap_external>:1020(exec_module)
    199/1    0.015    0.000   12.223   12.223 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
    365/4    0.062    0.000   12.219    3.055 {built-in method builtins.exec}
        1    0.000    0.000    6.535    6.535 E:\project\backup\‏‏ProShipment1\src\ui\shipments\new_shipment_window.py:1(<module>)
    77/18    0.002    0.000    6.399    0.356 {built-in method builtins.__import__}
  711/239    0.018    0.000    6.085    0.025 <frozen importlib._bootstrap>:1390(_handle_fromlist)
1176/1145    0.113    0.000    4.736    0.004 {built-in method builtins.__build_class__}
  196/178    0.011    0.000    3.306    0.019 <frozen importlib._bootstrap>:806(module_from_spec)
      263    0.010    0.000    3.202    0.012 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\enum.py:695(__call__)
    13/11    0.000    0.000    3.152    0.287 <frozen importlib._bootstrap_external>:1318(create_module)
    13/11    0.027    0.002    3.134    0.285 {built-in method _imp.create_dynamic}
      202    0.093    0.000    2.959    0.015 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\enum.py:842(_create_)
        1    0.002    0.002    2.623    2.623 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\__init__.py:1(<module>)
        1    0.000    0.000    2.318    2.318 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PySide6\__init__.py:1(<module>)
        1    0.000    0.000    2.252    2.252 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PySide6\__init__.py:41(_setupQtDirectories)
      321    0.013    0.000    2.246    0.007 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\re\__init__.py:330(_compile)
        1    0.000    0.000    2.224    2.224 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\shiboken6\__init__.py:1(<module>)


