#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة مراجعة نظام التعبئة التلقائية
Auto-Fill System Audit Tool - Comprehensive review of auto-fill functionality
"""

import sys
import os
import time
import threading
from datetime import datetime, date
from typing import Dict, List, Any, Tuple

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import (
        Shipment, ShipmentItem, Container, ShipmentDocument,
        Supplier, Item, Base
    )
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد النماذج: {e}")

class AutoFillSystemAuditor:
    """مراجع نظام التعبئة التلقائية"""
    
    def __init__(self, db_manager=None):
        """تهيئة مراجع نظام التعبئة التلقائية"""
        self.db_manager = db_manager or DatabaseManager()
        self.audit_log = []
        self.issues_found = []
        self.score = 0
        self.max_score = 100
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج المراجعة"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status == "ERROR":
            self.issues_found.append(f"{category}: {message}")
    
    def audit_auto_fill_dialog(self) -> int:
        """مراجعة نافذة التعبئة التلقائية"""
        self.log_audit("نافذة التعبئة التلقائية", "بدء مراجعة نافذة التعبئة التلقائية")
        score = 0
        
        try:
            # فحص ملف auto_fill_dialog.py
            dialog_file = "src/ui/dialogs/auto_fill_dialog.py"
            if os.path.exists(dialog_file):
                with open(dialog_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود فئة AutoFillWorker
                if "class AutoFillWorker" in content:
                    self.log_audit("نافذة التعبئة التلقائية", "✅ فئة AutoFillWorker موجودة")
                    score += 10
                else:
                    self.log_audit("نافذة التعبئة التلقائية", "❌ فئة AutoFillWorker غير موجودة", "ERROR")
                
                # فحص وجود فئة AutoFillDialog
                if "class AutoFillDialog" in content:
                    self.log_audit("نافذة التعبئة التلقائية", "✅ فئة AutoFillDialog موجودة")
                    score += 10
                else:
                    self.log_audit("نافذة التعبئة التلقائية", "❌ فئة AutoFillDialog غير موجودة", "ERROR")
                
                # فحص وجود دالة البحث الرئيسية
                if "def search_shipping_websites" in content:
                    self.log_audit("نافذة التعبئة التلقائية", "✅ دالة البحث في مواقع الشحن موجودة")
                    score += 15
                else:
                    self.log_audit("نافذة التعبئة التلقائية", "❌ دالة البحث في مواقع الشحن غير موجودة", "ERROR")
                
                # فحص تحديد شركة الشحن من رقم الحاوية
                if "def detect_carrier_from_container" in content:
                    self.log_audit("نافذة التعبئة التلقائية", "✅ دالة تحديد شركة الشحن موجودة")
                    score += 10
                else:
                    self.log_audit("نافذة التعبئة التلقائية", "❌ دالة تحديد شركة الشحن غير موجودة", "ERROR")
                
                # فحص معالجة النتائج
                if "def process_search_results" in content:
                    self.log_audit("نافذة التعبئة التلقائية", "✅ دالة معالجة النتائج موجودة")
                    score += 10
                else:
                    self.log_audit("نافذة التعبئة التلقائية", "❌ دالة معالجة النتائج غير موجودة", "ERROR")
                
                # فحص البيانات التجريبية
                if "_create_demo_data" in content:
                    self.log_audit("نافذة التعبئة التلقائية", "✅ نظام البيانات التجريبية موجود")
                    score += 5
                else:
                    self.log_audit("نافذة التعبئة التلقائية", "❌ نظام البيانات التجريبية غير موجود", "ERROR")
            else:
                self.log_audit("نافذة التعبئة التلقائية", "❌ ملف auto_fill_dialog.py غير موجود", "ERROR")
        
        except Exception as e:
            self.log_audit("نافذة التعبئة التلقائية", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("نافذة التعبئة التلقائية", f"نتيجة مراجعة نافذة التعبئة التلقائية: {score}/60")
        return score
    
    def audit_web_scraping_service(self) -> int:
        """مراجعة خدمة البحث عبر الإنترنت"""
        self.log_audit("خدمة البحث", "بدء مراجعة خدمة البحث عبر الإنترنت")
        score = 0
        
        try:
            # فحص ملف web_scraping_service.py
            service_file = "src/services/web_scraping_service.py"
            if os.path.exists(service_file):
                with open(service_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود فئة WebScrapingService
                if "class WebScrapingService" in content:
                    self.log_audit("خدمة البحث", "✅ فئة WebScrapingService موجودة")
                    score += 10
                else:
                    self.log_audit("خدمة البحث", "❌ فئة WebScrapingService غير موجودة", "ERROR")
                
                # فحص دعم شركات الشحن المختلفة
                shipping_companies = ["maersk", "msc", "cosco", "evergreen", "oocl"]
                supported_companies = 0
                
                for company in shipping_companies:
                    if company in content.lower():
                        supported_companies += 1
                
                if supported_companies >= 3:
                    self.log_audit("خدمة البحث", f"✅ دعم {supported_companies} شركة شحن")
                    score += 15
                elif supported_companies > 0:
                    self.log_audit("خدمة البحث", f"⚠️ دعم محدود: {supported_companies} شركة شحن")
                    score += 5
                else:
                    self.log_audit("خدمة البحث", "❌ لا يوجد دعم لشركات الشحن", "ERROR")
                
                # فحص البحث غير المتزامن
                if "async def" in content or "asyncio" in content:
                    self.log_audit("خدمة البحث", "✅ البحث غير المتزامن مدعوم")
                    score += 10
                else:
                    self.log_audit("خدمة البحث", "❌ البحث غير المتزامن غير مدعوم", "ERROR")
                
                # فحص معالجة الأخطاء
                if "try:" in content and "except" in content:
                    self.log_audit("خدمة البحث", "✅ معالجة الأخطاء موجودة")
                    score += 5
                else:
                    self.log_audit("خدمة البحث", "❌ معالجة الأخطاء غير كافية", "ERROR")
            else:
                self.log_audit("خدمة البحث", "❌ ملف web_scraping_service.py غير موجود", "ERROR")
        
        except Exception as e:
            self.log_audit("خدمة البحث", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("خدمة البحث", f"نتيجة مراجعة خدمة البحث: {score}/40")
        return score
    
    def audit_smart_widgets(self) -> int:
        """مراجعة الأدوات الذكية"""
        self.log_audit("الأدوات الذكية", "بدء مراجعة الأدوات الذكية")
        score = 0
        
        try:
            # فحص ملف smart_shipping_company_widget.py
            widget_file = "src/ui/widgets/smart_shipping_company_widget.py"
            if os.path.exists(widget_file):
                with open(widget_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود فئة SmartShippingCompanyWidget
                if "class SmartShippingCompanyWidget" in content:
                    self.log_audit("الأدوات الذكية", "✅ أداة شركة الشحن الذكية موجودة")
                    score += 10
                else:
                    self.log_audit("الأدوات الذكية", "❌ أداة شركة الشحن الذكية غير موجودة", "ERROR")
                
                # فحص التحقق من صحة البيانات
                if "ShippingCompanyValidator" in content:
                    self.log_audit("الأدوات الذكية", "✅ مُحقق شركة الشحن موجود")
                    score += 10
                else:
                    self.log_audit("الأدوات الذكية", "❌ مُحقق شركة الشحن غير موجود", "ERROR")
                
                # فحص الاقتراحات التلقائية
                if "show_suggestions" in content:
                    self.log_audit("الأدوات الذكية", "✅ نظام الاقتراحات التلقائية موجود")
                    score += 10
                else:
                    self.log_audit("الأدوات الذكية", "❌ نظام الاقتراحات التلقائية غير موجود", "ERROR")
                
                # فحص الإشارات
                if "Signal" in content and "company_selected" in content:
                    self.log_audit("الأدوات الذكية", "✅ نظام الإشارات موجود")
                    score += 5
                else:
                    self.log_audit("الأدوات الذكية", "❌ نظام الإشارات غير موجود", "ERROR")
            else:
                self.log_audit("الأدوات الذكية", "❌ ملف smart_shipping_company_widget.py غير موجود", "ERROR")
            
            # فحص ملف shipping_data_enhancer.py
            enhancer_file = "src/utils/shipping_data_enhancer.py"
            if os.path.exists(enhancer_file):
                with open(enhancer_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص تحسين البيانات
                if "enhance_shipping_companies" in content:
                    self.log_audit("الأدوات الذكية", "✅ نظام تحسين البيانات موجود")
                    score += 10
                else:
                    self.log_audit("الأدوات الذكية", "❌ نظام تحسين البيانات غير موجود", "ERROR")
                
                # فحص التحليل التلقائي
                if "analyze_shipping_companies" in content:
                    self.log_audit("الأدوات الذكية", "✅ نظام التحليل التلقائي موجود")
                    score += 5
                else:
                    self.log_audit("الأدوات الذكية", "❌ نظام التحليل التلقائي غير موجود", "ERROR")
            else:
                self.log_audit("الأدوات الذكية", "❌ ملف shipping_data_enhancer.py غير موجود", "ERROR")
        
        except Exception as e:
            self.log_audit("الأدوات الذكية", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("الأدوات الذكية", f"نتيجة مراجعة الأدوات الذكية: {score}/50")
        return score
    
    def audit_system_integration(self) -> int:
        """مراجعة تكامل النظام"""
        self.log_audit("تكامل النظام", "بدء مراجعة تكامل نظام التعبئة التلقائية")
        score = 0
        
        try:
            # فحص التكامل مع نافذة الشحنة الجديدة
            window_file = "src/ui/shipments/new_shipment_window.py"
            if os.path.exists(window_file):
                with open(window_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص استيراد نافذة التعبئة التلقائية
                if "auto_fill_dialog" in content or "AutoFillDialog" in content:
                    self.log_audit("تكامل النظام", "✅ التكامل مع نافذة الشحنة موجود")
                    score += 15
                else:
                    self.log_audit("تكامل النظام", "❌ التكامل مع نافذة الشحنة غير موجود", "ERROR")
                
                # فحص استخدام الأدوات الذكية
                if "smart_shipping_company_widget" in content or "SmartShippingCompanyWidget" in content:
                    self.log_audit("تكامل النظام", "✅ استخدام الأدوات الذكية موجود")
                    score += 10
                else:
                    self.log_audit("تكامل النظام", "❌ استخدام الأدوات الذكية غير موجود", "ERROR")
            
            # فحص ملفات الاختبار
            test_files = [
                "test_specific_container.py",
                "test_auto_fill.py"
            ]
            
            test_files_found = 0
            for test_file in test_files:
                if os.path.exists(test_file):
                    test_files_found += 1
            
            if test_files_found > 0:
                self.log_audit("تكامل النظام", f"✅ ملفات الاختبار موجودة: {test_files_found}")
                score += 10
            else:
                self.log_audit("تكامل النظام", "❌ ملفات الاختبار غير موجودة", "ERROR")
            
            # فحص التوافق مع قاعدة البيانات
            try:
                session = self.db_manager.get_session()
                
                # اختبار إنشاء شحنة مع بيانات تلقائية
                supplier = session.query(Supplier).first()
                if supplier:
                    test_shipment = Shipment(
                        shipment_number="AUTO-FILL-TEST-001",
                        shipment_date=date.today(),
                        supplier_id=supplier.id,
                        shipping_company="MAERSK LINE",  # شركة شحن معروفة
                        shipment_status="تحت الطلب"
                    )
                    session.add(test_shipment)
                    session.commit()
                    
                    self.log_audit("تكامل النظام", "✅ التوافق مع قاعدة البيانات يعمل")
                    score += 15
                    
                    # تنظيف البيانات التجريبية
                    session.delete(test_shipment)
                    session.commit()
                
                session.close()
                
            except Exception as e:
                self.log_audit("تكامل النظام", f"❌ مشكلة في التوافق مع قاعدة البيانات: {str(e)}", "ERROR")
        
        except Exception as e:
            self.log_audit("تكامل النظام", f"خطأ في مراجعة التكامل: {str(e)}", "ERROR")
        
        self.log_audit("تكامل النظام", f"نتيجة مراجعة تكامل النظام: {score}/50")
        return score
    
    def run_comprehensive_audit(self) -> Dict:
        """تشغيل المراجعة الشاملة لنظام التعبئة التلقائية"""
        self.log_audit("مراجعة شاملة", "بدء المراجعة الشاملة لنظام التعبئة التلقائية")
        
        # تشغيل جميع المراجعات
        dialog_score = self.audit_auto_fill_dialog()
        service_score = self.audit_web_scraping_service()
        widgets_score = self.audit_smart_widgets()
        integration_score = self.audit_system_integration()
        
        # حساب النتيجة الإجمالية
        total_score = dialog_score + service_score + widgets_score + integration_score
        max_possible = 200  # 60 + 40 + 50 + 50
        percentage = (total_score / max_possible) * 100
        
        # تحديد التقييم
        if percentage >= 90:
            grade = "ممتاز"
        elif percentage >= 80:
            grade = "جيد جداً"
        elif percentage >= 70:
            grade = "جيد"
        elif percentage >= 60:
            grade = "مقبول"
        else:
            grade = "يحتاج تحسين"
        
        self.log_audit("مراجعة شاملة", f"النتيجة الإجمالية: {total_score}/{max_possible} ({percentage:.1f}%) - {grade}")
        
        return {
            'total_score': total_score,
            'max_score': max_possible,
            'percentage': percentage,
            'grade': grade,
            'detailed_scores': {
                'auto_fill_dialog': dialog_score,
                'web_scraping_service': service_score,
                'smart_widgets': widgets_score,
                'system_integration': integration_score
            },
            'issues_found': self.issues_found,
            'audit_log': self.audit_log
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("🤖 أداة مراجعة نظام التعبئة التلقائية")
        print("=" * 50)
        
        # إنشاء مراجع نظام التعبئة التلقائية
        auditor = AutoFillSystemAuditor()
        
        # تشغيل المراجعة الشاملة
        results = auditor.run_comprehensive_audit()
        
        print("\n" + "=" * 50)
        print("📊 تقرير المراجعة الشاملة:")
        print(f"• النتيجة الإجمالية: {results['total_score']}/{results['max_score']}")
        print(f"• النسبة المئوية: {results['percentage']:.1f}%")
        print(f"• التقييم: {results['grade']}")
        
        print("\n📋 النتائج التفصيلية:")
        for category, score in results['detailed_scores'].items():
            print(f"• {category}: {score}")
        
        if results['issues_found']:
            print(f"\n⚠️ المشاكل المكتشفة ({len(results['issues_found'])}):")
            for issue in results['issues_found']:
                print(f"  - {issue}")
        else:
            print("\n✅ لم يتم اكتشاف مشاكل")
        
        print("\n" + "=" * 50)
        if results['percentage'] >= 80:
            print("✅ نظام التعبئة التلقائية في حالة جيدة!")
        else:
            print("⚠️ نظام التعبئة التلقائية يحتاج إلى تحسين")
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في مراجعة نظام التعبئة التلقائية: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
