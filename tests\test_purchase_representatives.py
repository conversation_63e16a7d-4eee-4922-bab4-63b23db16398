# -*- coding: utf-8 -*-
"""
اختبارات ميزة مندوبي المشتريات
Purchase Representatives Feature Tests
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.database.database_manager import DatabaseManager
from src.database.models import PurchaseRepresentative, Supplier, SupplierRepresentative
from datetime import datetime, date


def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔗 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # اختبار بسيط
        count = session.query(PurchaseRepresentative).count()
        print(f"✅ تم الاتصال بنجاح - عدد المندوبين: {count}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {str(e)}")
        return False


def test_purchase_representatives_crud():
    """اختبار عمليات CRUD لمندوبي المشتريات"""
    print("\n📝 اختبار عمليات مندوبي المشتريات...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # إنشاء مندوب جديد
        print("➕ إنشاء مندوب جديد...")
        representative = PurchaseRepresentative(
            code="TEST001",
            name="مندوب اختبار",
            name_en="Test Representative",
            phone="0112345678",
            mobile="0501234567",
            email="<EMAIL>",
            department="المشتريات",
            position="مندوب اختبار",
            hire_date=date.today(),
            salary=5000.0,
            commission_rate=2.0,
            city="الرياض",
            address="عنوان اختبار",
            notes="مندوب للاختبار فقط"
        )
        
        session.add(representative)
        session.commit()
        print(f"✅ تم إنشاء المندوب بمعرف: {representative.id}")
        
        # قراءة المندوب
        print("📖 قراءة بيانات المندوب...")
        found_rep = session.query(PurchaseRepresentative).filter_by(code="TEST001").first()
        if found_rep:
            print(f"✅ تم العثور على المندوب: {found_rep.name}")
        else:
            print("❌ لم يتم العثور على المندوب")
            return False
        
        # تحديث المندوب
        print("✏️ تحديث بيانات المندوب...")
        found_rep.salary = 6000.0
        found_rep.commission_rate = 2.5
        session.commit()
        print("✅ تم تحديث البيانات")
        
        # حذف ناعم
        print("🗑️ حذف ناعم للمندوب...")
        found_rep.is_active = False
        session.commit()
        print("✅ تم إلغاء تفعيل المندوب")
        
        # التحقق من الحذف الناعم
        active_reps = session.query(PurchaseRepresentative).filter_by(is_active=True).count()
        inactive_reps = session.query(PurchaseRepresentative).filter_by(is_active=False).count()
        print(f"📊 المندوبين النشطين: {active_reps}, غير النشطين: {inactive_reps}")
        
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في اختبار CRUD: {str(e)}")
        return False
        
    finally:
        session.close()


def test_supplier_representative_assignment():
    """اختبار ربط الموردين بالمندوبين"""
    print("\n🤝 اختبار ربط الموردين بالمندوبين...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن مورد ومندوب موجودين
        supplier = session.query(Supplier).filter_by(is_active=True).first()
        representative = session.query(PurchaseRepresentative).filter_by(is_active=True).first()
        
        if not supplier:
            print("❌ لا يوجد موردين في قاعدة البيانات")
            return False
            
        if not representative:
            print("❌ لا يوجد مندوبين في قاعدة البيانات")
            return False
        
        print(f"🏢 المورد: {supplier.name}")
        print(f"👤 المندوب: {representative.name}")
        
        # التحقق من عدم وجود ربط مسبق
        existing = session.query(SupplierRepresentative).filter_by(
            supplier_id=supplier.id,
            representative_id=representative.id,
            is_active=True
        ).first()
        
        if existing:
            print("ℹ️ الربط موجود بالفعل")
            return True
        
        # إنشاء ربط جديد
        print("➕ إنشاء ربط جديد...")
        assignment = SupplierRepresentative(
            supplier_id=supplier.id,
            representative_id=representative.id,
            is_primary=True,
            assigned_date=date.today(),
            notes="ربط اختبار"
        )
        
        session.add(assignment)
        session.commit()
        print(f"✅ تم إنشاء الربط بمعرف: {assignment.id}")
        
        # التحقق من الربط
        print("🔍 التحقق من الربط...")
        found_assignment = session.query(SupplierRepresentative).get(assignment.id)
        if found_assignment:
            print(f"✅ تم العثور على الربط - المورد: {found_assignment.supplier.name}")
            print(f"   المندوب: {found_assignment.representative.name}")
            print(f"   رئيسي: {'نعم' if found_assignment.is_primary else 'لا'}")
        else:
            print("❌ لم يتم العثور على الربط")
            return False
        
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في اختبار الربط: {str(e)}")
        return False
        
    finally:
        session.close()


def test_search_functionality():
    """اختبار وظائف البحث"""
    print("\n🔍 اختبار وظائف البحث...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث في المندوبين
        print("👥 البحث في المندوبين...")
        
        # البحث بالاسم
        name_results = session.query(PurchaseRepresentative).filter(
            PurchaseRepresentative.name.ilike('%أحمد%'),
            PurchaseRepresentative.is_active == True
        ).all()
        print(f"📝 نتائج البحث بالاسم 'أحمد': {len(name_results)}")
        
        # البحث بالكود
        code_results = session.query(PurchaseRepresentative).filter(
            PurchaseRepresentative.code.ilike('%REP%'),
            PurchaseRepresentative.is_active == True
        ).all()
        print(f"📝 نتائج البحث بالكود 'REP': {len(code_results)}")
        
        # البحث بالقسم
        dept_results = session.query(PurchaseRepresentative).filter(
            PurchaseRepresentative.department.ilike('%مشتريات%'),
            PurchaseRepresentative.is_active == True
        ).all()
        print(f"📝 نتائج البحث بالقسم 'مشتريات': {len(dept_results)}")
        
        # البحث في الموردين
        print("🏢 البحث في الموردين...")
        supplier_results = session.query(Supplier).filter(
            Supplier.is_active == True
        ).limit(5).all()
        print(f"📝 عدد الموردين النشطين (أول 5): {len(supplier_results)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث: {str(e)}")
        return False
        
    finally:
        session.close()


def test_data_integrity():
    """اختبار سلامة البيانات"""
    print("\n🛡️ اختبار سلامة البيانات...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # التحقق من الفهارس الفريدة
        print("🔑 التحقق من الفهارس الفريدة...")
        
        # عدد المندوبين بأكواد فريدة
        total_reps = session.query(PurchaseRepresentative).count()
        unique_codes = session.query(PurchaseRepresentative.code).distinct().count()
        print(f"📊 إجمالي المندوبين: {total_reps}")
        print(f"📊 الأكواد الفريدة: {unique_codes}")
        
        if total_reps == unique_codes:
            print("✅ جميع أكواد المندوبين فريدة")
        else:
            print("⚠️ يوجد تكرار في أكواد المندوبين")
        
        # التحقق من العلاقات
        print("🔗 التحقق من العلاقات...")
        
        # عدد الروابط النشطة
        active_assignments = session.query(SupplierRepresentative).filter_by(is_active=True).count()
        print(f"📊 الروابط النشطة: {active_assignments}")
        
        # التحقق من المندوبين الرئيسيين
        primary_assignments = session.query(SupplierRepresentative).filter_by(
            is_primary=True, is_active=True
        ).count()
        print(f"📊 المندوبين الرئيسيين: {primary_assignments}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سلامة البيانات: {str(e)}")
        return False
        
    finally:
        session.close()


def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات ميزة مندوبي المشتريات")
    print("=" * 60)
    
    tests = [
        ("اختبار الاتصال بقاعدة البيانات", test_database_connection),
        ("اختبار عمليات CRUD", test_purchase_representatives_crud),
        ("اختبار ربط الموردين بالمندوبين", test_supplier_representative_assignment),
        ("اختبار وظائف البحث", test_search_functionality),
        ("اختبار سلامة البيانات", test_data_integrity)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
                failed += 1
        except Exception as e:
            print(f"💥 {test_name} - خطأ: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"✅ الاختبارات الناجحة: {passed}")
    print(f"❌ الاختبارات الفاشلة: {failed}")
    print(f"📈 معدل النجاح: {(passed / (passed + failed)) * 100:.1f}%")
    
    if failed == 0:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ يوجد اختبارات فاشلة تحتاج إلى مراجعة")


if __name__ == "__main__":
    run_all_tests()
