#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة تتبع الشحنات المحدثة
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.shipment_tracking_window import ShipmentTrackingWindow

def test_tracking_window():
    """اختبار شاشة تتبع الشحنات"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار شاشة تتبع الشحنات المحدثة ===")
        
        window = ShipmentTrackingWindow()
        
        print(f"✅ عدد الأعمدة: {window.tracking_table.columnCount()}")
        print("✅ أسماء الأعمدة:")
        for i in range(window.tracking_table.columnCount()):
            header_text = window.tracking_table.horizontalHeaderItem(i).text()
            print(f"   {i}: {header_text}")
        
        print(f"✅ عرض عمود المورد: {window.tracking_table.horizontalHeader().sectionSize(1)} بكسل")
        print(f"✅ عدد الصفوف: {window.tracking_table.rowCount()}")
        
        # اختبار البيانات
        if window.tracking_table.rowCount() > 0:
            print("✅ البيانات الموجودة:")
            for row in range(min(3, window.tracking_table.rowCount())):  # أول 3 صفوف
                shipment_num = window.tracking_table.item(row, 0).text()
                supplier = window.tracking_table.item(row, 1).text()
                status = window.tracking_table.item(row, 3).text()
                print(f"   الصف {row}: {shipment_num} - {supplier} - {status}")
        
        print("✅ تم تحديث شاشة تتبع الشحنات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tracking_window()
