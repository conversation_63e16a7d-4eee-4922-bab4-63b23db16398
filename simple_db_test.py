#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_database_simple():
    """اختبار بسيط لقاعدة البيانات"""
    db_path = "data/proshipment.db"
    
    if not os.path.exists(db_path):
        print(f"ملف قاعدة البيانات غير موجود: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عرض الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("الجداول الموجودة:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # عدد الشحنات
        cursor.execute("SELECT COUNT(*) FROM shipments WHERE is_active = 1;")
        shipments_count = cursor.fetchone()[0]
        print(f"\nعدد الشحنات النشطة: {shipments_count}")
        
        # عرض أول 3 شحنات
        cursor.execute("""
            SELECT shipment_number, status, created_at 
            FROM shipments 
            WHERE is_active = 1 
            ORDER BY created_at DESC 
            LIMIT 3;
        """)
        shipments = cursor.fetchall()
        
        print("\nأول 3 شحنات:")
        for i, (number, status, created_at) in enumerate(shipments, 1):
            print(f"  {i}. رقم: {number}, الحالة: {status}, التاريخ: {created_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    test_database_simple()
