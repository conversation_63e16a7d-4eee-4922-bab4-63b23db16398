#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = PurchaseOrdersWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
