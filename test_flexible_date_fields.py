#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حقول التاريخ المرنة في النظام
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import QDate
from src.ui.widgets.flexible_date_edit import FlexibleDateEdit

class TestWindow(QMainWindow):
    """نافذة اختبار حقول التاريخ المرنة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار حقول التاريخ المرنة")
        self.setGeometry(100, 100, 600, 400)
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # إضافة تسميات توضيحية
        layout.addWidget(QLabel("اختبر إدخال التواريخ بالطرق التالية:"))
        layout.addWidget(QLabel("• dd/mm/yyyy (مثل: 15/03/2024)"))
        layout.addWidget(QLabel("• ddmmyyyy (مثل: 15032024)"))
        layout.addWidget(QLabel("• dd-mm-yyyy (مثل: 15-03-2024)"))
        layout.addWidget(QLabel("• dd.mm.yyyy (مثل: 15.03.2024)"))
        
        # حقل التاريخ الأول
        layout.addWidget(QLabel("حقل التاريخ الأول:"))
        self.date_field1 = FlexibleDateEdit()
        self.date_field1.dateChanged.connect(self.on_date1_changed)
        layout.addWidget(self.date_field1)
        
        # حقل التاريخ الثاني
        layout.addWidget(QLabel("حقل التاريخ الثاني:"))
        self.date_field2 = FlexibleDateEdit()
        self.date_field2.dateChanged.connect(self.on_date2_changed)
        layout.addWidget(self.date_field2)
        
        # حقل التاريخ الثالث
        layout.addWidget(QLabel("حقل التاريخ الثالث:"))
        self.date_field3 = FlexibleDateEdit()
        self.date_field3.dateChanged.connect(self.on_date3_changed)
        layout.addWidget(self.date_field3)
        
        # تسميات لعرض النتائج
        self.result_label1 = QLabel("النتيجة 1: ")
        self.result_label2 = QLabel("النتيجة 2: ")
        self.result_label3 = QLabel("النتيجة 3: ")
        
        layout.addWidget(self.result_label1)
        layout.addWidget(self.result_label2)
        layout.addWidget(self.result_label3)
        
        # تعيين تواريخ افتراضية
        self.date_field1.setDate(QDate.currentDate())
        self.date_field2.setDate(QDate.currentDate().addDays(30))
        self.date_field3.setDate(QDate.currentDate().addDays(-30))
        
    def on_date1_changed(self, date):
        """معالج تغيير التاريخ الأول"""
        self.result_label1.setText(f"النتيجة 1: {date.toString('dd/MM/yyyy')}")
        
    def on_date2_changed(self, date):
        """معالج تغيير التاريخ الثاني"""
        self.result_label2.setText(f"النتيجة 2: {date.toString('dd/MM/yyyy')}")
        
    def on_date3_changed(self, date):
        """معالج تغيير التاريخ الثالث"""
        self.result_label3.setText(f"النتيجة 3: {date.toString('dd/MM/yyyy')}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(2)  # RTL
    
    window = TestWindow()
    window.show()
    
    print("🧪 اختبار حقول التاريخ المرنة")
    print("=" * 50)
    print("✅ تم فتح نافذة الاختبار")
    print("📝 جرب إدخال التواريخ بالطرق التالية:")
    print("   • 15/03/2024")
    print("   • 15032024")
    print("   • 15-03-2024")
    print("   • 15.03.2024")
    print("🔄 سيتم تحويل التاريخ تلقائياً إلى التنسيق الصحيح")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
