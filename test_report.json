{"total_tests": 12, "passed_tests": 12, "failed_tests": 0, "success_rate": 100.0, "results": [{"test_name": "تهيئة نظام التعبئة", "success": true, "details": "تم إنشاء الكائن بنجاح", "timestamp": "2025-07-07T14:43:58.091014"}, {"test_name": "تحليل البيانات المفقودة", "success": true, "details": "تم تحليل 210 شحنة", "timestamp": "2025-07-07T14:43:58.243376"}, {"test_name": "البحث في قاعدة البيانات", "success": true, "details": "وجد 15 شحنة مشابهة", "timestamp": "2025-07-07T14:43:58.254620"}, {"test_name": "تهيئة خدمة البحث", "success": true, "details": "تم إنشاء الخدمة بنجاح", "timestamp": "2025-07-07T14:43:58.351552"}, {"test_name": "إنشاء بيانات الشحنة", "success": true, "details": "رقم الحاوية: MSKU1234567", "timestamp": "2025-07-07T14:43:58.356846"}, {"test_name": "حساب درجة الثقة", "success": true, "details": "درجة الثقة: 29.0%", "timestamp": "2025-07-07T14:43:58.375974"}, {"test_name": "التحقق من جودة البيانات", "success": true, "details": "ع<PERSON><PERSON> المشاكل: 0", "timestamp": "2025-07-07T14:43:58.376615"}, {"test_name": "البحث برقم الحاوية", "success": true, "details": "ع<PERSON><PERSON> النتائج: 1", "timestamp": "2025-07-07T14:43:58.772934"}, {"test_name": "البحث برقم بوليصة الشحن", "success": true, "details": "ع<PERSON><PERSON> النتائج: 1", "timestamp": "2025-07-07T14:43:59.317569"}, {"test_name": "دمج البيانات", "success": true, "details": "تم تعبئة 2 حقل: vessel_name, origin_port", "timestamp": "2025-07-07T14:43:59.319628"}, {"test_name": "سرعة تحليل البيانات", "success": true, "details": "استغرق 0.01 ثانية", "timestamp": "2025-07-07T14:43:59.334264"}, {"test_name": "سرعة البحث في قاعدة البيانات", "success": true, "details": "استغرق 0.00 ثانية لإيجاد 15 نتيجة", "timestamp": "2025-07-07T14:43:59.338789"}]}