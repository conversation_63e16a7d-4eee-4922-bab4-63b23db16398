#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام النهائي
Final System Test - Comprehensive final validation of the entire system
"""

import sys
import os
import time
from datetime import datetime, date
from typing import Dict, List, Any, Tuple

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import (
        Shipment, ShipmentItem, Container, ShipmentDocument,
        Supplier, Item, Base
    )
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد النماذج: {e}")

class FinalSystemTester:
    """مختبر النظام النهائي"""
    
    def __init__(self, db_manager=None):
        """تهيئة مختبر النظام النهائي"""
        self.db_manager = db_manager or DatabaseManager()
        self.test_log = []
        self.test_results = {}
        self.overall_score = 0
        self.max_score = 0
        
    def log_test(self, category: str, test_name: str, result: str, score: int, max_score: int):
        """تسجيل نتائج الاختبار"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {category} - {test_name}: {result} ({score}/{max_score})"
        self.test_log.append(log_entry)
        print(log_entry)
        
        if category not in self.test_results:
            self.test_results[category] = {'tests': [], 'total_score': 0, 'max_score': 0}
        
        self.test_results[category]['tests'].append({
            'name': test_name,
            'result': result,
            'score': score,
            'max_score': max_score
        })
        self.test_results[category]['total_score'] += score
        self.test_results[category]['max_score'] += max_score
        
        self.overall_score += score
        self.max_score += max_score
    
    def test_database_functionality(self) -> None:
        """اختبار وظائف قاعدة البيانات"""
        print("\n🗄️ اختبار وظائف قاعدة البيانات")
        print("-" * 40)
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار الاتصال
            try:
                suppliers_count = session.query(Supplier).count()
                self.log_test("قاعدة البيانات", "الاتصال بقاعدة البيانات", "✅ ناجح", 10, 10)
            except Exception as e:
                self.log_test("قاعدة البيانات", "الاتصال بقاعدة البيانات", f"❌ فاشل: {str(e)}", 0, 10)
            
            # اختبار الاستعلامات
            try:
                start_time = time.time()
                shipments = session.query(Shipment).limit(10).all()
                items = session.query(Item).limit(10).all()
                suppliers = session.query(Supplier).limit(10).all()
                query_time = time.time() - start_time
                
                if query_time < 1.0:
                    self.log_test("قاعدة البيانات", "سرعة الاستعلامات", f"✅ ممتاز ({query_time:.3f}s)", 15, 15)
                elif query_time < 3.0:
                    self.log_test("قاعدة البيانات", "سرعة الاستعلامات", f"⚠️ مقبول ({query_time:.3f}s)", 10, 15)
                else:
                    self.log_test("قاعدة البيانات", "سرعة الاستعلامات", f"❌ بطيء ({query_time:.3f}s)", 5, 15)
            except Exception as e:
                self.log_test("قاعدة البيانات", "سرعة الاستعلامات", f"❌ فاشل: {str(e)}", 0, 15)
            
            # اختبار العلاقات
            try:
                shipments_with_suppliers = session.query(Shipment).join(Supplier).count()
                self.log_test("قاعدة البيانات", "العلاقات بين الجداول", "✅ تعمل بشكل صحيح", 10, 10)
            except Exception as e:
                self.log_test("قاعدة البيانات", "العلاقات بين الجداول", f"❌ فاشل: {str(e)}", 0, 10)
            
            session.close()
            
        except Exception as e:
            self.log_test("قاعدة البيانات", "اختبار عام", f"❌ خطأ عام: {str(e)}", 0, 35)
    
    def test_shipment_procedures(self) -> None:
        """اختبار إجراءات الشحنات"""
        print("\n📦 اختبار إجراءات الشحنات")
        print("-" * 40)
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار إنشاء الشحنات
            try:
                supplier = session.query(Supplier).first()
                if supplier:
                    test_shipment = Shipment(
                        shipment_number="FINAL-TEST-001",
                        shipment_date=date.today(),
                        supplier_id=supplier.id,
                        shipment_status="تحت الطلب",
                        notes="شحنة اختبار نهائي"
                    )
                    session.add(test_shipment)
                    session.commit()
                    
                    self.log_test("إجراءات الشحنات", "إنشاء الشحنات", "✅ يعمل بشكل صحيح", 15, 15)
                    
                    # اختبار تعديل الشحنات
                    setattr(test_shipment, 'notes', 'تم تعديل الشحنة في الاختبار النهائي')
                    session.commit()
                    self.log_test("إجراءات الشحنات", "تعديل الشحنات", "✅ يعمل بشكل صحيح", 15, 15)
                    
                    # اختبار حذف الشحنات
                    session.delete(test_shipment)
                    session.commit()
                    self.log_test("إجراءات الشحنات", "حذف الشحنات", "✅ يعمل بشكل صحيح", 15, 15)
                else:
                    self.log_test("إجراءات الشحنات", "إنشاء الشحنات", "❌ لا يوجد موردين للاختبار", 0, 45)
            except Exception as e:
                self.log_test("إجراءات الشحنات", "إجراءات CRUD", f"❌ فاشل: {str(e)}", 0, 45)
            
            session.close()
            
        except Exception as e:
            self.log_test("إجراءات الشحنات", "اختبار عام", f"❌ خطأ عام: {str(e)}", 0, 45)
    
    def test_data_validation(self) -> None:
        """اختبار التحقق من صحة البيانات"""
        print("\n🔍 اختبار التحقق من صحة البيانات")
        print("-" * 40)
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار منع الأرقام المكررة
            try:
                supplier = session.query(Supplier).first()
                if supplier:
                    # إنشاء شحنة
                    shipment1 = Shipment(
                        shipment_number="DUPLICATE-FINAL-TEST",
                        shipment_date=date.today(),
                        supplier_id=supplier.id,
                        shipment_status="تحت الطلب"
                    )
                    session.add(shipment1)
                    session.commit()
                    
                    # محاولة إنشاء شحنة بنفس الرقم
                    try:
                        shipment2 = Shipment(
                            shipment_number="DUPLICATE-FINAL-TEST",
                            shipment_date=date.today(),
                            supplier_id=supplier.id,
                            shipment_status="تحت الطلب"
                        )
                        session.add(shipment2)
                        session.commit()
                        
                        # إذا نجح، فهناك مشكلة
                        self.log_test("التحقق من البيانات", "منع الأرقام المكررة", "❌ لا يعمل", 0, 15)
                        session.delete(shipment1)
                        session.delete(shipment2)
                        session.commit()
                    except:
                        # هذا متوقع
                        session.rollback()
                        session.delete(shipment1)
                        session.commit()
                        self.log_test("التحقق من البيانات", "منع الأرقام المكررة", "✅ يعمل بشكل صحيح", 15, 15)
                else:
                    self.log_test("التحقق من البيانات", "منع الأرقام المكررة", "❌ لا يوجد موردين للاختبار", 0, 15)
            except Exception as e:
                self.log_test("التحقق من البيانات", "منع الأرقام المكررة", f"❌ خطأ: {str(e)}", 0, 15)
            
            # اختبار العلاقات الخارجية
            try:
                invalid_shipment = Shipment(
                    shipment_number="INVALID-SUPPLIER-FINAL",
                    shipment_date=date.today(),
                    supplier_id=99999,  # مورد غير موجود
                    shipment_status="تحت الطلب"
                )
                session.add(invalid_shipment)
                session.commit()
                
                # إذا نجح، فهناك مشكلة
                self.log_test("التحقق من البيانات", "التحقق من العلاقات الخارجية", "❌ لا يعمل", 0, 10)
                session.delete(invalid_shipment)
                session.commit()
            except:
                # هذا متوقع
                session.rollback()
                self.log_test("التحقق من البيانات", "التحقق من العلاقات الخارجية", "✅ يعمل بشكل صحيح", 10, 10)
            
            session.close()
            
        except Exception as e:
            self.log_test("التحقق من البيانات", "اختبار عام", f"❌ خطأ عام: {str(e)}", 0, 25)
    
    def test_system_files(self) -> None:
        """اختبار ملفات النظام"""
        print("\n📁 اختبار ملفات النظام")
        print("-" * 40)
        
        # الملفات الأساسية
        essential_files = {
            "قاعدة البيانات": [
                "src/database/database_manager.py",
                "src/database/models.py"
            ],
            "واجهة الشحنات": [
                "src/ui/shipments/shipments_window.py",
                "src/ui/shipments/new_shipment_window.py"
            ],
            "التعبئة التلقائية": [
                "src/ui/dialogs/auto_fill_dialog.py",
                "src/services/web_scraping_service.py"
            ],
            "الأدوات المساعدة": [
                "src/utils/shipping_data_enhancer.py",
                "src/ui/widgets/smart_shipping_company_widget.py"
            ]
        }
        
        for category, files in essential_files.items():
            missing_files = []
            for file_path in files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
            
            if not missing_files:
                self.log_test("ملفات النظام", f"ملفات {category}", "✅ جميع الملفات موجودة", 5, 5)
            else:
                self.log_test("ملفات النظام", f"ملفات {category}", f"❌ ملفات مفقودة: {len(missing_files)}", 0, 5)
    
    def test_auto_fill_system(self) -> None:
        """اختبار نظام التعبئة التلقائية"""
        print("\n🤖 اختبار نظام التعبئة التلقائية")
        print("-" * 40)
        
        try:
            # فحص وجود الملفات
            auto_fill_files = [
                "src/ui/dialogs/auto_fill_dialog.py",
                "src/services/web_scraping_service.py",
                "src/ui/widgets/smart_shipping_company_widget.py"
            ]
            
            missing_files = [f for f in auto_fill_files if not os.path.exists(f)]
            
            if not missing_files:
                self.log_test("نظام التعبئة التلقائية", "ملفات النظام", "✅ جميع الملفات موجودة", 10, 10)
                
                # اختبار استيراد المكونات
                try:
                    # محاولة استيراد AutoFillDialog
                    from src.ui.dialogs.auto_fill_dialog import AutoFillDialog, AutoFillWorker
                    self.log_test("نظام التعبئة التلقائية", "استيراد AutoFillDialog", "✅ ناجح", 10, 10)
                except Exception as e:
                    self.log_test("نظام التعبئة التلقائية", "استيراد AutoFillDialog", f"❌ فاشل: {str(e)}", 0, 10)
                
                try:
                    # محاولة استيراد WebScrapingService
                    from src.services.web_scraping_service import WebScrapingService
                    self.log_test("نظام التعبئة التلقائية", "استيراد WebScrapingService", "✅ ناجح", 10, 10)
                except Exception as e:
                    self.log_test("نظام التعبئة التلقائية", "استيراد WebScrapingService", f"❌ فاشل: {str(e)}", 0, 10)
                
                try:
                    # محاولة استيراد SmartShippingCompanyWidget
                    from src.ui.widgets.smart_shipping_company_widget import SmartShippingCompanyWidget
                    self.log_test("نظام التعبئة التلقائية", "استيراد SmartShippingCompanyWidget", "✅ ناجح", 5, 5)
                except Exception as e:
                    self.log_test("نظام التعبئة التلقائية", "استيراد SmartShippingCompanyWidget", f"❌ فاشل: {str(e)}", 0, 5)
            else:
                self.log_test("نظام التعبئة التلقائية", "ملفات النظام", f"❌ ملفات مفقودة: {len(missing_files)}", 0, 35)
                
        except Exception as e:
            self.log_test("نظام التعبئة التلقائية", "اختبار عام", f"❌ خطأ عام: {str(e)}", 0, 35)
    
    def run_final_system_test(self) -> Dict:
        """تشغيل الاختبار النهائي للنظام"""
        print("🎯 اختبار النظام النهائي")
        print("=" * 50)
        
        start_time = time.time()
        
        # تشغيل جميع الاختبارات
        self.test_database_functionality()
        self.test_shipment_procedures()
        self.test_data_validation()
        self.test_system_files()
        self.test_auto_fill_system()
        
        end_time = time.time()
        test_duration = end_time - start_time
        
        # حساب النتائج
        percentage = (self.overall_score / self.max_score * 100) if self.max_score > 0 else 0
        
        # تحديد التقييم النهائي
        if percentage >= 95:
            grade = "ممتاز"
            status = "✅ النظام جاهز للإنتاج"
        elif percentage >= 85:
            grade = "جيد جداً"
            status = "✅ النظام جاهز مع تحسينات طفيفة"
        elif percentage >= 75:
            grade = "جيد"
            status = "⚠️ النظام يحتاج بعض التحسينات"
        elif percentage >= 60:
            grade = "مقبول"
            status = "⚠️ النظام يحتاج تحسينات مهمة"
        else:
            grade = "غير مقبول"
            status = "❌ النظام يحتاج إعادة عمل"
        
        return {
            'overall_score': self.overall_score,
            'max_score': self.max_score,
            'percentage': percentage,
            'grade': grade,
            'status': status,
            'test_duration': test_duration,
            'test_results': self.test_results,
            'test_log': self.test_log
        }

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء مختبر النظام النهائي
        tester = FinalSystemTester()
        
        # تشغيل الاختبار النهائي
        results = tester.run_final_system_test()
        
        print("\n" + "=" * 50)
        print("📊 تقرير الاختبار النهائي للنظام")
        print("=" * 50)
        print(f"• النتيجة الإجمالية: {results['overall_score']}/{results['max_score']}")
        print(f"• النسبة المئوية: {results['percentage']:.1f}%")
        print(f"• التقييم: {results['grade']}")
        print(f"• مدة الاختبار: {results['test_duration']:.2f} ثانية")
        print(f"• الحالة: {results['status']}")
        
        print("\n📋 النتائج التفصيلية:")
        for category, data in results['test_results'].items():
            category_percentage = (data['total_score'] / data['max_score'] * 100) if data['max_score'] > 0 else 0
            print(f"  • {category}: {data['total_score']}/{data['max_score']} ({category_percentage:.1f}%)")
        
        print("\n" + "=" * 50)
        print(results['status'])
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار النهائي: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
