#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدخال البيانات وتعبئة حقول الشحنات
Test Data Integration System for Shipments

هذا الملف يوضح كيفية عمل النظام المطور لإدخال البيانات من البحث عبر الإنترنت
وتطبيقها على الشحنات في قاعدة البيانات
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة مسار المشروع
sys.path.append('src')

def test_web_scraping_service():
    """اختبار خدمة البحث عبر الإنترنت"""
    print("🔍 اختبار خدمة البحث عبر الإنترنت")
    print("=" * 50)
    
    try:
        from services.web_scraping_service import WebScrapingService
        
        async def run_search():
            service = WebScrapingService()
            
            # اختبار أرقام حاويات مختلفة
            test_containers = [
                'OOCU7496892',  # OOCL
                'MSKU1234567',  # Maersk
                'PILU9876543',  # PIL
            ]
            
            for container in test_containers:
                print(f"\n📦 اختبار رقم الحاوية: {container}")
                results = await service.search_all_carriers(container_number=container)
                
                if results:
                    result = results[0]
                    print(f"   ✅ تم العثور على بيانات:")
                    print(f"      🏢 الشركة: {result.carrier}")
                    print(f"      📋 الحالة: {result.status}")
                    print(f"      🚢 السفينة: {result.vessel_name}")
                    print(f"      🔢 الرحلة: {result.voyage_number}")
                    print(f"      🏃 من: {result.origin_port}")
                    print(f"      🏁 إلى: {result.destination_port}")
                else:
                    print(f"   ❌ لم يتم العثور على بيانات")
        
        asyncio.run(run_search())
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث: {str(e)}")
        return False

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    print("\n💾 اختبار تكامل قاعدة البيانات")
    print("=" * 50)
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Shipment
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # إحصائيات الشحنات
        total_shipments = session.query(Shipment).count()
        active_shipments = session.query(Shipment).filter(Shipment.is_active == True).count()
        
        print(f"📊 إحصائيات الشحنات:")
        print(f"   📦 إجمالي الشحنات: {total_shipments}")
        print(f"   ✅ الشحنات النشطة: {active_shipments}")
        
        # اختبار الحقول المطلوبة للتحديث
        sample_shipment = session.query(Shipment).first()
        if sample_shipment:
            print(f"\n🔧 الحقول المتاحة للتحديث:")
            
            fields_to_check = [
                ('shipping_company', 'شركة الشحن'),
                ('shipment_status', 'حالة الشحنة'),
                ('vessel_name', 'اسم السفينة'),
                ('voyage_number', 'رقم الرحلة'),
                ('port_of_loading', 'ميناء التحميل'),
                ('port_of_discharge', 'ميناء التفريغ'),
                ('container_number', 'رقم الحاوية'),
                ('bill_of_lading', 'بوليصة الشحن'),
                ('estimated_departure_date', 'تاريخ المغادرة المتوقع'),
                ('estimated_arrival_date', 'تاريخ الوصول المتوقع'),
                ('actual_departure_date', 'تاريخ المغادرة الفعلي'),
                ('notes', 'الملاحظات')
            ]
            
            for field_name, arabic_name in fields_to_check:
                has_field = hasattr(sample_shipment, field_name)
                status = "✅" if has_field else "❌"
                print(f"   {status} {arabic_name} ({field_name})")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def simulate_data_application():
    """محاكاة تطبيق البيانات على الشحنات"""
    print("\n🎯 محاكاة تطبيق البيانات")
    print("=" * 50)
    
    # بيانات وهمية من البحث عبر الإنترنت
    sample_web_data = {
        'carrier': 'OOCL',
        'status': 'Vessel Departure',
        'vessel_name': 'OOCL Hong Kong',
        'voyage_number': 'OC2406',
        'origin_port': 'Hong Kong',
        'destination_port': 'Long Beach, USA',
        'departure_date': '2024-02-05',
        'arrival_date': '2024-02-25',
        'container_number': 'OOCU7496892'
    }
    
    print("📋 البيانات المراد تطبيقها:")
    for key, value in sample_web_data.items():
        print(f"   • {key}: {value}")
    
    print(f"\n🔄 خطوات التطبيق:")
    print(f"   1️⃣ البحث عن الشحنات المطابقة")
    print(f"   2️⃣ اختيار الشحنة المراد تحديثها")
    print(f"   3️⃣ تطبيق البيانات على الحقول المناسبة")
    print(f"   4️⃣ إضافة ملاحظة عن التحديث")
    print(f"   5️⃣ حفظ التغييرات في قاعدة البيانات")
    
    # محاكاة التطبيق
    field_mapping = {
        'carrier': 'shipping_company',
        'status': 'shipment_status',
        'vessel_name': 'vessel_name',
        'voyage_number': 'voyage_number',
        'origin_port': 'port_of_loading',
        'destination_port': 'port_of_discharge',
        'departure_date': 'actual_departure_date',
        'arrival_date': 'estimated_arrival_date'
    }
    
    print(f"\n🗺️ تطبيق الحقول:")
    for web_field, db_field in field_mapping.items():
        value = sample_web_data.get(web_field, 'غير محدد')
        print(f"   {web_field} → {db_field}: {value}")
    
    return True

def test_user_interface():
    """اختبار واجهة المستخدم"""
    print("\n🖥️ اختبار واجهة المستخدم")
    print("=" * 50)
    
    try:
        from ui.dialogs.shipment_data_filler_dialog import ShipmentDataFillerDialog
        print("✅ تم تحميل نافذة تعبئة البيانات بنجاح")
        
        print(f"\n🎛️ الميزات المتاحة في الواجهة:")
        print(f"   🔍 البحث بواسطة رقم الحاوية")
        print(f"   📋 البحث بواسطة بوليصة الشحن")
        print(f"   🏢 اختيار شركة الملاحة")
        print(f"   📊 عرض النتائج في جدول")
        print(f"   ✅ تطبيق على شحنة محددة")
        print(f"   🔄 تطبيق على الشحنات المطابقة")
        print(f"   📤 تصدير النتائج")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نظام إدخال البيانات وتعبئة حقول الشحنات")
    print("=" * 70)
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تشغيل الاختبارات
    tests = [
        ("خدمة البحث عبر الإنترنت", test_web_scraping_service),
        ("تكامل قاعدة البيانات", test_database_integration),
        ("محاكاة تطبيق البيانات", simulate_data_application),
        ("واجهة المستخدم", test_user_interface)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n📊 نتائج الاختبارات")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n📋 كيفية الاستخدام:")
        print("   1. افتح نافذة إدارة الشحنات")
        print("   2. اضغط على زر 'تعبئة البيانات الذكية'")
        print("   3. أدخل رقم الحاوية أو بوليصة الشحن")
        print("   4. اضغط 'بحث' للعثور على البيانات")
        print("   5. اختر النتيجة المناسبة")
        print("   6. اضغط 'تطبيق على شحنة محددة' أو 'تطبيق على الشحنات المطابقة'")
        print("   7. اختر الشحنة المراد تحديثها")
        print("   8. تأكد من التطبيق")
    else:
        print(f"\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
