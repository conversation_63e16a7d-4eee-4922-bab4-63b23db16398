#!/usr/bin/env python3
"""
اختبار نافذة تتبع طلبات الشراء المتقدمة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🔍 اختبار نافذة تتبع طلبات الشراء المتقدمة...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        # 1. اختبار استيراد النافذة
        print("\n📦 اختبار الاستيراد...")
        try:
            from src.ui.suppliers.purchase_orders_tracking_window import PurchaseOrdersTrackingWindow
            print("✅ تم استيراد PurchaseOrdersTrackingWindow بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد PurchaseOrdersTrackingWindow: {e}")
            return False
        
        # 2. اختبار إنشاء النافذة
        print("\n🏗️ اختبار إنشاء النافذة...")
        try:
            tracking_window = PurchaseOrdersTrackingWindow()
            print("✅ تم إنشاء نافذة التتبع بنجاح")
        except Exception as e:
            print(f"❌ فشل في إنشاء نافذة التتبع: {e}")
            return False
        
        # 3. فحص خصائص النافذة
        print("\n🔍 فحص خصائص النافذة...")
        
        # فحص العنوان
        expected_title = "تتبع طلبات الشراء المتقدم - ProShipment"
        if tracking_window.windowTitle() == expected_title:
            print("✅ عنوان النافذة صحيح")
        else:
            print(f"❌ عنوان النافذة غير صحيح: {tracking_window.windowTitle()}")
        
        # فحص الحد الأدنى للحجم
        min_size = tracking_window.minimumSize()
        if min_size.width() >= 1600 and min_size.height() >= 900:
            print("✅ الحد الأدنى للحجم صحيح")
        else:
            print(f"❌ الحد الأدنى للحجم غير صحيح: {min_size.width()}x{min_size.height()}")
        
        # 4. فحص المكونات الرئيسية
        print("\n🧩 فحص المكونات الرئيسية...")
        
        components = [
            ("orders_table", "جدول الطلبات"),
            ("search_edit", "حقل البحث"),
            ("status_filter", "فلتر الحالة"),
            ("supplier_filter", "فلتر المورد"),
            ("date_from", "تاريخ من"),
            ("date_to", "تاريخ إلى"),
            ("value_from", "قيمة من"),
            ("value_to", "قيمة إلى"),
            ("stats_cards", "بطاقات الإحصائيات"),
            ("status_bar", "شريط الحالة")
        ]
        
        missing_components = []
        for component, description in components:
            if hasattr(tracking_window, component):
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - غير موجود")
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ مكونات مفقودة: {missing_components}")
            return False
        
        # 5. فحص شريط الأدوات
        print("\n🔧 فحص شريط الأدوات...")
        
        toolbar = tracking_window.findChild(tracking_window.__class__.__bases__[0], "أدوات التتبع")
        if toolbar or len(tracking_window.findChildren(tracking_window.toolBar().__class__)) > 0:
            print("✅ شريط الأدوات موجود")
            
            # فحص الأزرار
            toolbars = tracking_window.findChildren(tracking_window.toolBar().__class__)
            if toolbars:
                actions = toolbars[0].actions()
                action_texts = [action.text() for action in actions if action.text()]
                print(f"✅ أزرار شريط الأدوات: {action_texts}")
            
        else:
            print("❌ شريط الأدوات غير موجود")
        
        # 6. فحص جدول الطلبات
        print("\n📊 فحص جدول الطلبات...")
        
        table = tracking_window.orders_table
        expected_columns = 15
        actual_columns = table.columnCount()
        
        if actual_columns == expected_columns:
            print(f"✅ عدد الأعمدة صحيح: {actual_columns}")
        else:
            print(f"❌ عدد الأعمدة غير صحيح: {actual_columns} (متوقع: {expected_columns})")
        
        # فحص عناوين الأعمدة
        headers = []
        for col in range(table.columnCount()):
            header_item = table.horizontalHeaderItem(col)
            if header_item:
                headers.append(header_item.text())
        
        expected_headers = [
            "رقم الطلب", "تاريخ الطلب", "المورد", "حالة الطلب", 
            "عدد الأصناف", "إجمالي الكمية", "إجمالي القيمة", "العملة",
            "تاريخ التسليم المتوقع", "الكمية المسلمة", "الكمية المتبقية",
            "نسبة الإنجاز", "الأيام المتبقية", "حالة التأخير", "ملاحظات"
        ]
        
        if len(headers) == len(expected_headers):
            print("✅ عناوين الأعمدة مكتملة")
        else:
            print(f"❌ عناوين الأعمدة غير مكتملة: {len(headers)}/{len(expected_headers)}")
        
        # 7. فحص بطاقات الإحصائيات
        print("\n📈 فحص بطاقات الإحصائيات...")
        
        expected_cards = [
            "total_orders", "active_orders", "completed_orders", 
            "overdue_orders", "total_value", "avg_delivery_time"
        ]
        
        stats_cards = tracking_window.stats_cards
        missing_cards = []
        
        for card_key in expected_cards:
            if card_key in stats_cards:
                print(f"✅ بطاقة {card_key}")
            else:
                print(f"❌ بطاقة {card_key} - غير موجودة")
                missing_cards.append(card_key)
        
        if missing_cards:
            print(f"❌ بطاقات مفقودة: {missing_cards}")
            return False
        
        # 8. اختبار الدوال الرئيسية
        print("\n⚙️ اختبار الدوال الرئيسية...")
        
        functions_to_test = [
            ("refresh_all_data", "تحديث جميع البيانات"),
            ("load_orders_data", "تحميل بيانات الطلبات"),
            ("update_statistics", "تحديث الإحصائيات"),
            ("perform_search", "تنفيذ البحث"),
            ("clear_filters", "مسح الفلاتر"),
            ("export_tracking_report", "تصدير التقرير")
        ]
        
        for func_name, description in functions_to_test:
            if hasattr(tracking_window, func_name):
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - غير موجودة")
        
        # 9. اختبار تحميل البيانات الأولية
        print("\n📊 اختبار تحميل البيانات الأولية...")
        try:
            tracking_window.load_initial_data()
            print("✅ تم تحميل البيانات الأولية بنجاح")
        except Exception as e:
            print(f"⚠️ تحذير في تحميل البيانات الأولية: {e}")
        
        # 10. اختبار فتح النافذة من النافذة الرئيسية
        print("\n🔗 اختبار التكامل مع النافذة الرئيسية...")
        try:
            from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
            
            main_window = PurchaseOrdersWindow(mode="list")
            if hasattr(main_window, 'open_tracking_window'):
                print("✅ دالة فتح التتبع موجودة في النافذة الرئيسية")
            else:
                print("❌ دالة فتح التتبع غير موجودة في النافذة الرئيسية")
                
        except Exception as e:
            print(f"⚠️ تحذير في اختبار التكامل: {e}")
        
        print("\n🎉 جميع الاختبارات اكتملت!")
        print("✅ نافذة تتبع طلبات الشراء المتقدمة جاهزة للاستخدام!")
        
        # عرض النافذة للمراجعة البصرية
        print("\n👁️ عرض النافذة للمراجعة البصرية...")
        tracking_window.show()
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار العام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 نافذة تتبع طلبات الشراء المتقدمة مكتملة وجاهزة!")
    else:
        print("\n❌ نافذة التتبع تحتاج مراجعة إضافية")
