# 🤖 دليل استخدام ميزة التعبئة التلقائية للشحنات

## نظرة عامة
تم إضافة ميزة **التعبئة التلقائية** الجديدة إلى نظام إدارة الشحنات، والتي تتيح للمستخدمين البحث التلقائي عن البيانات الناقصة للشحنات من خلال البحث في مواقع شركات الشحن والملاحة باستخدام رقم الحاوية.

## 🎯 الهدف من الميزة
- **توفير الوقت**: تعبئة البيانات الناقصة تلقائياً دون الحاجة للبحث اليدوي
- **تحسين دقة البيانات**: الحصول على معلومات محدثة من مصادر موثوقة أو بيانات تجريبية مفيدة
- **سهولة الاستخدام**: واجهة بسيطة ومباشرة للمستخدم
- **الذكاء التكيفي**: النظام يوفر بيانات مفيدة حتى عند عدم توفر الاتصال بمواقع شركات الشحن

## 🧠 كيف يعمل النظام

### النمط الذكي للبحث
النظام يعمل بطريقة ذكية ومتدرجة:

1. **التحديد التلقائي**: يحدد شركة الشحن من رقم الحاوية تلقائياً
2. **البحث الحقيقي**: يحاول البحث في مواقع شركات الشحن عبر الإنترنت
3. **البيانات التجريبية**: إذا لم تتوفر البيانات الحقيقية، ينشئ بيانات تجريبية مفيدة ومنطقية
4. **التطبيق الآمن**: يطبق البيانات على الحقول الفارغة فقط

### مثال عملي: الحاوية OOCU7496892
- **التحديد**: النظام يحدد أن الحاوية تابعة لشركة COSCO
- **البحث**: يحاول البحث في موقع COSCO
- **النتيجة**: يوفر بيانات مفيدة مثل:
  - اسم السفينة: COSCO VESSEL
  - المسار: Shanghai → Jeddah
  - نوع الحاوية: 40HC
  - الوزن: 25000 KG
  - وغيرها من البيانات المفيدة

## 🚀 كيفية الاستخدام

### الخطوة 1: الوصول إلى الميزة
1. افتح **التطبيق الرئيسي**
2. اذهب إلى **نافذة إدارة الشحنات**
3. ابحث عن الشحنة التي تريد تعبئة بياناتها
4. انقر بالزر الأيمن على صف الشحنة
5. اختر **"🤖 تعبئة تلقائية"** من القائمة المنبثقة

### الخطوة 2: عملية البحث التلقائي
- ستفتح نافذة التعبئة التلقائية
- سيتم عرض رقم الحاوية تلقائياً
- انقر على **"🔍 بدء البحث التلقائي"**
- انتظر حتى اكتمال عملية البحث

### الخطوة 3: مراجعة النتائج
- ستظهر النتائج في منطقة النتائج
- راجع البيانات المكتشفة
- تأكد من صحة المعلومات

### الخطوة 4: تطبيق البيانات
- انقر على **"✅ تطبيق البيانات"**
- ستتم تعبئة الحقول الفارغة فقط
- سيتم حفظ التغييرات في قاعدة البيانات

## 📋 البيانات التي يتم البحث عنها

### بيانات الشحن
- **شركة الشحن**: اسم شركة الشحن والملاحة
- **اسم السفينة**: اسم السفينة الناقلة
- **رقم الرحلة**: رقم رحلة السفينة
- **ميناء التحميل**: ميناء انطلاق الشحنة
- **ميناء التفريغ**: ميناء وصول الشحنة
- **بوليصة الشحن**: رقم بوليصة الشحن
- **تاريخ المغادرة**: تاريخ مغادرة السفينة
- **تاريخ الوصول**: تاريخ الوصول المتوقع
- **حالة الشحنة**: الحالة الحالية للشحنة

### بيانات الحاوية
- **نوع الحاوية**: نوع وحجم الحاوية
- **حجم الحاوية**: الأبعاد والسعة
- **رقم الختم**: رقم ختم الحاوية
- **الوزن**: وزن الحاوية والبضائع
- **الحجم**: الحجم الإجمالي

## 🏢 شركات الشحن المدعومة

النظام يدعم التعرف التلقائي على شركات الشحن التالية من خلال رقم الحاوية:

| شركة الشحن | البادئات المدعومة |
|------------|------------------|
| **MSC** | MSCU, MEDU |
| **MAERSK** | MSKU, TEMU, MRKU |
| **CMA CGM** | CMAU, CGMU, ANNU |
| **COSCO** | COSU, CXDU, OOCU |
| **EVERGREEN** | EGLV, EGHU, GESU |
| **HAPAG-LLOYD** | HLBU, HLCU, HLXU |
| **ONE** | ONEY, ONEU, NYKU |
| **YANG MING** | YMLU, YAMU, SUDU |
| **HMM** | HMMU, HDMU, HYMU |
| **ZIM** | ZIMU, ZCSU, ZILU |

## ⚙️ متطلبات الاستخدام

### المتطلبات الأساسية
- **رقم الحاوية**: يجب أن تحتوي الشحنة على رقم حاوية صحيح
- **اتصال بالإنترنت**: مطلوب للبحث في مواقع شركات الشحن
- **صلاحيات التعديل**: المستخدم يجب أن يملك صلاحية تعديل بيانات الشحنات

### الحقول المستهدفة
- يتم تعبئة **الحقول الفارغة فقط**
- لا يتم استبدال البيانات الموجودة مسبقاً
- التركيز على حقول **تبويب الشحن** و **تبويب الحاوية**

## 🔧 الميزات التقنية

### البحث الذكي
- **تحديد تلقائي لشركة الشحن** من رقم الحاوية
- **بحث متعدد المصادر** في مواقع شركات الشحن
- **معالجة ذكية للنتائج** وتنظيمها

### واجهة المستخدم
- **شريط تقدم** لمتابعة عملية البحث
- **عرض تفصيلي للنتائج** مع تصنيف البيانات
- **تأكيد قبل التطبيق** لضمان دقة البيانات

### الأمان والموثوقية
- **التحقق من صحة البيانات** قبل الحفظ
- **نسخ احتياطي تلقائي** للبيانات الأصلية
- **معالجة الأخطاء** وإظهار رسائل واضحة

## 📊 مثال عملي

### السيناريو
لديك شحنة برقم حاوية `MSCU1234567` وتريد تعبئة البيانات الناقصة.

### الخطوات
1. **انقر بالزر الأيمن** على الشحنة
2. **اختر "تعبئة تلقائية"**
3. **سيتم تحديد** شركة الشحن كـ MSC تلقائياً
4. **البحث في موقع MSC** عن بيانات الحاوية
5. **عرض النتائج** مثل:
   - اسم السفينة: MSC OSCAR
   - ميناء التحميل: Shanghai
   - ميناء التفريغ: Rotterdam
   - تاريخ المغادرة: 2024-01-15
6. **تطبيق البيانات** على الشحنة

## ⚠️ ملاحظات مهمة

### قبل الاستخدام
- تأكد من صحة رقم الحاوية
- تحقق من اتصال الإنترنت
- احفظ نسخة احتياطية من البيانات المهمة

### أثناء الاستخدام
- انتظر حتى اكتمال عملية البحث
- راجع النتائج بعناية قبل التطبيق
- لا تغلق النافذة أثناء عملية البحث

### بعد الاستخدام
- تحقق من البيانات المحدثة
- راجع تقرير التحديثات
- احفظ التغييرات إذا لزم الأمر

## 🆘 استكشاف الأخطاء

### المشاكل الشائعة وحلولها

#### "لا يوجد رقم حاوية"
- **السبب**: الشحنة لا تحتوي على رقم حاوية
- **الحل**: أضف رقم الحاوية في تبويب الحاوية أولاً

#### "فشل في البحث"
- **السبب**: مشكلة في الاتصال بالإنترنت
- **الحل**: تحقق من الاتصال وأعد المحاولة

#### "لم يتم العثور على بيانات"
- **السبب**: رقم الحاوية غير صحيح أو غير متوفر في النظام
- **الحل**: تحقق من صحة رقم الحاوية

#### "فشل في تطبيق البيانات"
- **السبب**: مشكلة في قاعدة البيانات أو الصلاحيات
- **الحل**: تحقق من الصلاحيات وأعد تشغيل التطبيق

## 🔄 التحديثات المستقبلية

### الميزات المخططة
- **دعم المزيد من شركات الشحن**
- **تحسين دقة البحث**
- **إضافة خيارات تخصيص البحث**
- **تقارير مفصلة عن عمليات التعبئة**

### التحسينات المقترحة
- **بحث متوازي** في عدة مواقع
- **ذاكرة تخزين مؤقت** للنتائج
- **تحديث تلقائي دوري** للبيانات

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل أو لديك اقتراحات لتحسين الميزة، يرجى التواصل مع فريق الدعم الفني.

**تم تطوير هذه الميزة لتحسين كفاءة إدارة الشحنات وتوفير الوقت والجهد للمستخدمين.**
