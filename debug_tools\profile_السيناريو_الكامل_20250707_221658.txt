تحليل الأداء: السيناريو_الكامل
وقت التنفيذ: 6.026 ثانية
تغيير الذاكرة: +0.01 MB
==================================================
         5728 function calls (5651 primitive calls) in 6.027 seconds

   Ordered by: cumulative time
   List reduced from 292 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        2    0.000    0.000    1.007    0.503 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py:1757(cpu_percent)
        1    0.000    0.000    0.196    0.196 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:65(_handle_freeze_detection)
        1    0.016    0.016    0.152    0.152 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:78(_dump_all_stacks)
       40    0.005    0.000    0.082    0.002 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:31(log_activity)
        5    0.000    0.000    0.078    0.016 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:242(format_stack)
        6    0.001    0.000    0.070    0.012 E:\project\backup\‏‏ProShipment1\src\database\database_manager.py:20(__init__)
        5    0.002    0.000    0.067    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:33(format_list)
        5    0.001    0.000    0.064    0.013 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:738(format)
       22    0.015    0.001    0.063    0.003 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:522(format_frame_summary)
        5    0.001    0.000    0.056    0.011 E:\project\backup\‏‏ProShipment1\debug_tools\database_monitor.py:59(_check_connections)
    54/51    0.055    0.001    0.053    0.001 {built-in method builtins.print}
        6    0.000    0.000    0.051    0.009 <string>:1(create_engine)
     18/6    0.002    0.000    0.051    0.008 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py:249(warned)
        6    0.003    0.000    0.046    0.008 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\create.py:92(create_engine)
        1    0.001    0.001    0.033    0.033 E:\project\backup\‏‏ProShipment1\debug_tools\edit_mode_debugger.py:106(_check_database_locks)
       52    0.008    0.000    0.028    0.001 {method 'strftime' of 'datetime.date' objects}
       44    0.001    0.000    0.021    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\traceback.py:363(_dedented_lines)
        7    0.000    0.000    0.020    0.003 E:\project\backup\‏‏ProShipment1\debug_tools\database_monitor.py:29(log)
       22    0.002    0.000    0.020    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\textwrap.py:470(indent)
       22    0.001    0.000    0.020    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\textwrap.py:419(dedent)


