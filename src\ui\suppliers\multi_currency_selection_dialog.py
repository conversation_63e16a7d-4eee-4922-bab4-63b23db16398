# -*- coding: utf-8 -*-
"""
نافذة اختيار العملات المتعددة
Multi Currency Selection Dialog
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QCheckBox,
                               QAbstractItemView, QDialogButtonBox, QDoubleSpinBox, QTextEdit)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import Currency


class MultiCurrencySelectionDialog(QDialog):
    """نافذة اختيار العملات المتعددة"""
    
    # إشارة لإرسال العملات المحددة
    currencies_selected = Signal(list)  # List of currency data dicts
    
    def __init__(self, parent=None, selected_currencies=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.selected_currencies_data = selected_currencies or []
        self.setup_ui()
        self.load_currencies()
        self.restore_selections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختيار العملات المتعددة")
        self.setMinimumSize(900, 650)
        self.resize(1000, 750)
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان النافذة
        title_label = QLabel("اختر العملات المطلوبة للمورد")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # جدول العملات
        self.create_currencies_table(main_layout)
        
        # أزرار النافذة
        self.create_dialog_buttons(main_layout)
        
        # تطبيق الأنماط
        self.apply_styles()
    
    def create_currencies_table(self, layout):
        """إنشاء جدول العملات"""
        table_group = QGroupBox("العملات المتاحة")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)
        
        # الجدول
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(7)
        self.currencies_table.setHorizontalHeaderLabels([
            "اختيار", "العملة", "الرمز", "الرمز المرئي", "السعر الافتراضي", 
            "مفضلة", "سعر مخصص"
        ])
        
        # إعدادات الجدول
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        # ضبط عرض الأعمدة
        header = self.currencies_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اختيار
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # العملة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الرمز
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الرمز المرئي
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السعر الافتراضي
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # مفضلة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # سعر مخصص
        
        table_layout.addWidget(self.currencies_table)
        
        # معلومات إضافية
        info_label = QLabel("💡 يمكنك اختيار عدة عملات وتحديد العملة المفضلة وأسعار الصرف المخصصة")
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-style: italic;
                padding: 5px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
        """)
        table_layout.addWidget(info_label)
        
        layout.addWidget(table_group)
    
    def create_dialog_buttons(self, layout):
        """إنشاء أزرار النافذة"""
        # تخطيط الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(0, 15, 0, 0)

        # أزرار التحديد
        select_all_button = QPushButton("☑️ تحديد الكل")
        select_all_button.clicked.connect(self.select_all)
        select_all_button.setMinimumWidth(120)

        deselect_all_button = QPushButton("☐ إلغاء تحديد الكل")
        deselect_all_button.clicked.connect(self.deselect_all)
        deselect_all_button.setMinimumWidth(140)

        # أزرار الحفظ والإلغاء
        save_button = QPushButton("💾 حفظ الاختيار")
        save_button.clicked.connect(self.accept_selection)
        save_button.setMinimumWidth(120)
        save_button.setDefault(True)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setMinimumWidth(100)

        # ترتيب الأزرار
        buttons_layout.addWidget(select_all_button)
        buttons_layout.addWidget(deselect_all_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
                min-height: 35px;
                margin: 2px;
            }
            QPushButton:default {
                background-color: #27ae60;
            }
            QPushButton:default:hover {
                background-color: #229954;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #27ae60;
                background-color: #27ae60;
                border-radius: 3px;
            }
            QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 4px;
                min-height: 25px;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """)
    
    def load_currencies(self):
        """تحميل العملات من قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()
            
            self.currencies_table.setRowCount(len(currencies))
            
            for row, currency in enumerate(currencies):
                # عمود الاختيار
                select_checkbox = QCheckBox()
                select_checkbox.setProperty("currency_id", currency.id)
                select_checkbox.stateChanged.connect(self.on_selection_changed)
                self.currencies_table.setCellWidget(row, 0, select_checkbox)
                
                # اسم العملة
                name_item = QTableWidgetItem(currency.name)
                name_item.setData(Qt.UserRole, currency.id)
                self.currencies_table.setItem(row, 1, name_item)
                
                # رمز العملة
                code_item = QTableWidgetItem(currency.code)
                self.currencies_table.setItem(row, 2, code_item)
                
                # الرمز المرئي
                symbol_item = QTableWidgetItem(currency.symbol or "")
                self.currencies_table.setItem(row, 3, symbol_item)
                
                # السعر الافتراضي
                rate_item = QTableWidgetItem(f"{currency.exchange_rate:.4f}")
                rate_item.setTextAlignment(Qt.AlignCenter)
                self.currencies_table.setItem(row, 4, rate_item)
                
                # مفضلة
                preferred_checkbox = QCheckBox()
                preferred_checkbox.setProperty("currency_id", currency.id)
                preferred_checkbox.stateChanged.connect(self.on_preferred_changed)
                self.currencies_table.setCellWidget(row, 5, preferred_checkbox)
                
                # سعر مخصص
                custom_rate_spin = QDoubleSpinBox()
                custom_rate_spin.setRange(0.001, 999999.999)
                custom_rate_spin.setDecimals(4)
                custom_rate_spin.setValue(0.0)
                custom_rate_spin.setSpecialValueText("افتراضي")
                custom_rate_spin.setProperty("currency_id", currency.id)
                self.currencies_table.setCellWidget(row, 6, custom_rate_spin)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل العملات: {str(e)}")
        finally:
            session.close()
    
    def restore_selections(self):
        """استعادة الاختيارات السابقة"""
        if not self.selected_currencies_data:
            return
        
        for row in range(self.currencies_table.rowCount()):
            currency_id = self.currencies_table.item(row, 1).data(Qt.UserRole)
            
            # البحث عن العملة في الاختيارات السابقة
            for currency_data in self.selected_currencies_data:
                if currency_data.get('currency_id') == currency_id:
                    # تحديد الاختيار
                    select_checkbox = self.currencies_table.cellWidget(row, 0)
                    select_checkbox.setChecked(True)
                    
                    # تحديد المفضلة
                    preferred_checkbox = self.currencies_table.cellWidget(row, 5)
                    preferred_checkbox.setChecked(currency_data.get('is_preferred', False))
                    
                    # تحديد السعر المخصص
                    custom_rate_spin = self.currencies_table.cellWidget(row, 6)
                    custom_rate = currency_data.get('exchange_rate_override', 0.0)
                    if custom_rate and custom_rate > 0:
                        custom_rate_spin.setValue(custom_rate)
                    
                    break
    
    def on_selection_changed(self, state):
        """عند تغيير اختيار العملة"""
        # يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        pass
    
    def on_preferred_changed(self, state):
        """عند تغيير العملة المفضلة"""
        if state == Qt.Checked:
            sender = self.sender()
            currency_id = sender.property("currency_id")
            
            # إلغاء تفضيل العملات الأخرى
            for row in range(self.currencies_table.rowCount()):
                preferred_checkbox = self.currencies_table.cellWidget(row, 5)
                if preferred_checkbox != sender:
                    preferred_checkbox.setChecked(False)
    
    def select_all(self):
        """تحديد جميع العملات"""
        for row in range(self.currencies_table.rowCount()):
            select_checkbox = self.currencies_table.cellWidget(row, 0)
            select_checkbox.setChecked(True)
    
    def deselect_all(self):
        """إلغاء تحديد جميع العملات"""
        for row in range(self.currencies_table.rowCount()):
            select_checkbox = self.currencies_table.cellWidget(row, 0)
            select_checkbox.setChecked(False)
            
            preferred_checkbox = self.currencies_table.cellWidget(row, 5)
            preferred_checkbox.setChecked(False)
    
    def accept_selection(self):
        """قبول الاختيار"""
        selected_currencies = []
        
        for row in range(self.currencies_table.rowCount()):
            select_checkbox = self.currencies_table.cellWidget(row, 0)
            
            if select_checkbox.isChecked():
                currency_id = self.currencies_table.item(row, 1).data(Qt.UserRole)
                currency_name = self.currencies_table.item(row, 1).text()
                currency_code = self.currencies_table.item(row, 2).text()
                currency_symbol = self.currencies_table.item(row, 3).text()
                
                preferred_checkbox = self.currencies_table.cellWidget(row, 5)
                is_preferred = preferred_checkbox.isChecked()
                
                custom_rate_spin = self.currencies_table.cellWidget(row, 6)
                custom_rate = custom_rate_spin.value() if custom_rate_spin.value() > 0 else None
                
                selected_currencies.append({
                    'currency_id': currency_id,
                    'currency_name': currency_name,
                    'currency_code': currency_code,
                    'currency_symbol': currency_symbol,
                    'is_preferred': is_preferred,
                    'exchange_rate_override': custom_rate
                })
        
        if not selected_currencies:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة واحدة على الأقل")
            return
        
        # التحقق من وجود عملة مفضلة واحدة فقط
        preferred_count = sum(1 for curr in selected_currencies if curr['is_preferred'])
        if preferred_count > 1:
            QMessageBox.warning(self, "تحذير", "يمكن اختيار عملة مفضلة واحدة فقط")
            return
        
        self.currencies_selected.emit(selected_currencies)
        self.accept()
    
    def get_selected_currencies(self):
        """الحصول على العملات المحددة"""
        # هذه الدالة للاستخدام إذا لم تستخدم الإشارات
        selected_currencies = []
        
        for row in range(self.currencies_table.rowCount()):
            select_checkbox = self.currencies_table.cellWidget(row, 0)
            
            if select_checkbox.isChecked():
                currency_id = self.currencies_table.item(row, 1).data(Qt.UserRole)
                currency_name = self.currencies_table.item(row, 1).text()
                currency_code = self.currencies_table.item(row, 2).text()
                currency_symbol = self.currencies_table.item(row, 3).text()
                
                preferred_checkbox = self.currencies_table.cellWidget(row, 5)
                is_preferred = preferred_checkbox.isChecked()
                
                custom_rate_spin = self.currencies_table.cellWidget(row, 6)
                custom_rate = custom_rate_spin.value() if custom_rate_spin.value() > 0 else None
                
                selected_currencies.append({
                    'currency_id': currency_id,
                    'currency_name': currency_name,
                    'currency_code': currency_code,
                    'currency_symbol': currency_symbol,
                    'is_preferred': is_preferred,
                    'exchange_rate_override': custom_rate
                })
        
        return selected_currencies
