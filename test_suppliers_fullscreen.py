#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.suppliers.suppliers_window import SuppliersWindow

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء نافذة إدارة الموردين في وضع ملء الشاشة
    window = SuppliersWindow()
    window.show()
    
    print("تم فتح نافذة إدارة الموردين في وضع ملء الشاشة")
    print("المميزات الجديدة:")
    print("- النافذة تفتح في وضع ملء الشاشة تلقائياً")
    print("- تصميم محسن للتبويبات")
    print("- أزرار تحكم محسنة مع أيقونات")
    print("- شريط أدوات محسن")
    print("- شريط حالة معلوماتي")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
