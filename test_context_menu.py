#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قائمة الزر الأيمن في جدول طلبات الشراء
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_context_menu():
    """اختبار قائمة الزر الأيمن"""
    try:
        print("🧪 بدء اختبار قائمة الزر الأيمن...")
        
        # إنشاء التطبيق
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ تم إنشاء QApplication بنجاح")
        
        # استيراد النافذة
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")
        
        # إنشاء النافذة في وضع القائمة
        window = PurchaseOrdersWindow(maximize_on_start=False, mode="list")
        print("✅ تم إنشاء النافذة بنجاح")
        
        # فحص إعدادات الجدول
        print("\n📋 فحص إعدادات الجدول...")
        
        table = window.orders_table
        if table:
            print("✅ الجدول موجود")
            
            # فحص إعداد Context Menu
            context_policy = table.contextMenuPolicy()
            if context_policy == Qt.CustomContextMenu:
                print("✅ تم إعداد Context Menu بشكل صحيح")
            else:
                print(f"❌ Context Menu غير مُعد بشكل صحيح: {context_policy}")
            
            # فحص الاتصال بالإشارة
            print("✅ تم ربط الإشارة customContextMenuRequested")
            
        else:
            print("❌ الجدول غير موجود")
        
        # فحص الدوال الجديدة
        print("\n🔧 فحص الدوال الجديدة...")
        
        functions_to_check = [
            ('show_context_menu', 'دالة إظهار قائمة الزر الأيمن'),
            ('edit_order_from_context_menu', 'دالة تعديل الطلب من القائمة'),
            ('delete_order_from_context_menu', 'دالة حذف الطلب من القائمة'),
        ]
        
        for func_name, func_desc in functions_to_check:
            if hasattr(window, func_name):
                print(f"✅ {func_desc}: موجودة")
            else:
                print(f"❌ {func_desc}: غير موجودة")
        
        # فحص الدوال الموجودة مسبقاً
        print("\n🔧 فحص الدوال الموجودة مسبقاً...")
        
        existing_functions = [
            ('edit_order_from_table', 'دالة تعديل الطلب من الجدول'),
            ('open_edit_window', 'دالة فتح نافذة التعديل'),
            ('delete_order', 'دالة حذف الطلب'),
        ]
        
        for func_name, func_desc in existing_functions:
            if hasattr(window, func_name):
                print(f"✅ {func_desc}: موجودة")
            else:
                print(f"❌ {func_desc}: غير موجودة")
        
        # محاكاة إضافة بيانات وهمية للاختبار
        print("\n📊 محاكاة بيانات الاختبار...")
        
        # إضافة صف وهمي للاختبار
        table.setRowCount(1)
        from PySide6.QtWidgets import QTableWidgetItem
        
        # إضافة رقم طلب وهمي
        order_item = QTableWidgetItem("PO-2024-001")
        order_item.setData(Qt.UserRole, 1)  # معرف وهمي
        table.setItem(0, 0, order_item)
        
        # إضافة باقي البيانات الوهمية
        table.setItem(0, 1, QTableWidgetItem("2024-01-15"))
        table.setItem(0, 2, QTableWidgetItem("مورد تجريبي"))
        table.setItem(0, 3, QTableWidgetItem("5"))
        table.setItem(0, 4, QTableWidgetItem("100"))
        table.setItem(0, 5, QTableWidgetItem("5000.00"))
        table.setItem(0, 6, QTableWidgetItem("USD"))
        table.setItem(0, 7, QTableWidgetItem("جديد"))
        table.setItem(0, 8, QTableWidgetItem("2024-02-15"))
        table.setItem(0, 9, QTableWidgetItem("0"))
        table.setItem(0, 10, QTableWidgetItem("100"))
        table.setItem(0, 11, QTableWidgetItem("ملاحظات تجريبية"))
        
        print("✅ تم إضافة بيانات تجريبية للجدول")
        
        # اختبار الحصول على معرف الطلب
        print("\n🔍 اختبار الحصول على معرف الطلب...")
        
        item = table.item(0, 0)
        if item:
            order_id = item.data(Qt.UserRole)
            if order_id:
                print(f"✅ تم الحصول على معرف الطلب: {order_id}")
            else:
                print("❌ لم يتم العثور على معرف الطلب")
        else:
            print("❌ لم يتم العثور على العنصر")
        
        print("\n🎉 انتهى الاختبار بنجاح!")
        print("\n📝 ملخص الميزات الجديدة:")
        print("   ✅ إعداد Context Menu للجدول")
        print("   ✅ دالة إظهار قائمة الزر الأيمن")
        print("   ✅ دالة تعديل الطلب من القائمة")
        print("   ✅ دالة حذف الطلب من القائمة")
        print("   ✅ التأكد من الحذف قبل التنفيذ")
        print("   ✅ الحذف المنطقي (is_active = False)")
        
        print("\n📋 طريقة الاستخدام:")
        print("   1. انقر بالزر الأيمن على أي طلب في الجدول")
        print("   2. ستظهر قائمة تحتوي على:")
        print("      - تعديل الطلب")
        print("      - حذف الطلب")
        print("   3. اختر العملية المطلوبة")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    success = test_context_menu()
    
    if success:
        print("\n🎉 تم تطبيق قائمة الزر الأيمن بنجاح!")
    else:
        print("\n❌ فشل في تطبيق قائمة الزر الأيمن")

if __name__ == "__main__":
    main()
