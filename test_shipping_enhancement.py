"""
اختبار نظام تحسين بيانات شركات الشحن
"""

import sys
import os
sys.path.append('src')

from utils.shipping_data_enhancer import ShippingDataEnhancer
from utils.shipping_company_validator import ShippingCompanyValidator

def test_validator():
    """اختبار مدقق شركات الشحن"""
    print("🔍 اختبار مدقق شركات الشحن")
    print("=" * 40)
    
    validator = ShippingCompanyValidator()
    
    # اختبار أسماء مختلفة
    test_names = [
        "MSC",
        "ماريسك",
        "Maersk Line",
        "MSK",  # خطأ شائع
        "فيدكس",
        "FedEx Express",
        "DHL",
        "شركة الخليج للشحن",
        "COSCO",
        "كوسكو",
        "شركة غير موجودة"
    ]
    
    for name in test_names:
        print(f"\n📝 اختبار: '{name}'")
        result = validator.validate_and_correct(name)
        
        if result["is_valid"]:
            print(f"   ✅ صحيح - {result['company_info']['arabic_name']}")
            print(f"   🌍 الدولة: {result['company_info']['country']}")
            print(f"   📦 النوع: {result['company_info']['type']}")
        elif result["suggestions"]:
            best = result["suggestions"][0]
            confidence = int(best["similarity"] * 100)
            print(f"   💡 اقتراح: {best['suggested_name']} ({confidence}%)")
            print(f"   🌍 الدولة: {best['company_data']['country']}")
        else:
            print(f"   ❌ غير معروف")

def test_enhancer():
    """اختبار محسن البيانات"""
    print("\n\n🔧 اختبار محسن البيانات")
    print("=" * 40)
    
    enhancer = ShippingDataEnhancer()
    
    # تحليل البيانات
    print("📊 تحليل البيانات الحالية...")
    analysis = enhancer.analyze_shipping_companies()
    
    print(f"📈 النتائج:")
    print(f"   إجمالي الشحنات: {analysis['total_shipments']}")
    print(f"   شركات فريدة: {len(analysis['unique_companies'])}")
    print(f"   شركات صحيحة: {len(analysis['valid_companies'])}")
    print(f"   شركات قابلة للتصحيح: {len(analysis['correctable_companies'])}")
    print(f"   شركات غير معروفة: {len(analysis['invalid_companies'])}")
    print(f"   شحنات بدون شركة: {analysis['empty_companies']}")
    
    # عرض الشركات القابلة للتصحيح
    if analysis['correctable_companies']:
        print(f"\n💡 الشركات القابلة للتصحيح:")
        for company in analysis['correctable_companies'][:5]:  # أول 5
            if company.get('best_match'):
                original = company['original']
                suggested = company['best_match']['suggested_name']
                confidence = int(company['best_match']['similarity'] * 100)
                print(f"   • '{original}' → '{suggested}' ({confidence}%)")
    
    # عرض الشركات غير المعروفة
    if analysis['invalid_companies']:
        print(f"\n❌ الشركات غير المعروفة:")
        for company in analysis['invalid_companies'][:5]:  # أول 5
            print(f"   • '{company['original']}'")
    
    # إنشاء تقرير
    print(f"\n📋 إنشاء تقرير مفصل...")
    report = enhancer.generate_enhancement_report()
    print("✅ تم إنشاء التقرير")
    
    # حفظ التقرير في ملف
    try:
        with open("shipping_enhancement_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print("💾 تم حفظ التقرير في: shipping_enhancement_report.txt")
    except Exception as e:
        print(f"❌ خطأ في حفظ التقرير: {e}")

def test_specific_corrections():
    """اختبار تصحيحات محددة"""
    print("\n\n🎯 اختبار تصحيحات محددة")
    print("=" * 40)
    
    validator = ShippingCompanyValidator()
    
    # حالات اختبار محددة
    test_cases = [
        ("MSK", "MSC"),  # خطأ شائع
        ("ماريسك", "Maersk"),  # عربي إلى إنجليزي
        ("فيدكس", "FedEx"),  # عربي إلى إنجليزي
        ("DHL Express", "DHL"),  # اسم مطول
        ("Mediterranean Shipping", "MSC"),  # اسم مختصر
        ("شركة البحر المتوسط", "MSC"),  # ترجمة عربية
    ]
    
    for original, expected in test_cases:
        print(f"\n🧪 اختبار: '{original}' → متوقع: '{expected}'")
        
        suggestions = validator.suggest_corrections(original, max_suggestions=3)
        
        if suggestions:
            best = suggestions[0]
            confidence = int(best["similarity"] * 100)
            
            if expected.lower() in best["suggested_name"].lower():
                print(f"   ✅ نجح: {best['suggested_name']} ({confidence}%)")
            else:
                print(f"   ⚠️  مختلف: {best['suggested_name']} ({confidence}%)")
                print(f"      المتوقع: {expected}")
        else:
            print(f"   ❌ لا توجد اقتراحات")

def main():
    """دالة رئيسية للاختبار"""
    print("🚢 اختبار نظام تحسين بيانات شركات الشحن")
    print("=" * 50)
    
    try:
        # اختبار المدقق
        test_validator()
        
        # اختبار المحسن
        test_enhancer()
        
        # اختبار تصحيحات محددة
        test_specific_corrections()
        
        print("\n\n🎉 اكتمل الاختبار بنجاح!")
        print("💡 يمكنك الآن استخدام النظام من خلال:")
        print("   1. الواجهة الذكية في نافذة الشحنة الجديدة")
        print("   2. نافذة تحسين البيانات من قائمة الأدوات")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
