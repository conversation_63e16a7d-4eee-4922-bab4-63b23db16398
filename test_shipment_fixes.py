#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاحات الشحنات - رقم الشحنة وتاريخ الشحنة
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier
from datetime import datetime

def test_shipment_date_field():
    """اختبار حقل تاريخ الشحنة"""
    print("🧪 اختبار حقل تاريخ الشحنة...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن شحنة موجودة
        shipment = session.query(Shipment).first()
        if shipment:
            print(f"📦 اختبار الشحنة: {shipment.shipment_number}")
            
            # اختبار الوصول لحقل تاريخ الشحنة
            try:
                shipment_date = shipment.shipment_date
                print(f"   ✅ تاريخ الشحنة: {shipment_date}")
                
                # إذا لم يكن هناك تاريخ، استخدم تاريخ الإنشاء
                if not shipment.shipment_date and shipment.created_at:
                    shipment.shipment_date = shipment.created_at
                    session.commit()
                    print("   ✅ تم تحديث تاريخ الشحنة من تاريخ الإنشاء!")
                    
            except AttributeError as e:
                print(f"   ❌ خطأ في الوصول لحقل تاريخ الشحنة: {str(e)}")
                return False
                
        else:
            print("   ⚠️ لا توجد شحنات في قاعدة البيانات")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()

def test_shipment_number_field():
    """اختبار حقل رقم الشحنة"""
    print("\n🧪 اختبار حقل رقم الشحنة...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # البحث عن شحنة موجودة
        shipment = session.query(Shipment).first()
        if shipment:
            print(f"📦 اختبار الشحنة: {shipment.id}")
            
            # اختبار الوصول لحقل رقم الشحنة
            try:
                shipment_number = shipment.shipment_number
                print(f"   ✅ رقم الشحنة: {shipment_number}")
                
            except AttributeError as e:
                print(f"   ❌ خطأ في الوصول لحقل رقم الشحنة: {str(e)}")
                return False
                
        else:
            print("   ⚠️ لا توجد شحنات في قاعدة البيانات")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()

def test_shipment_loading():
    """اختبار تحميل الشحنات مع الحقول الجديدة"""
    print("\n🧪 اختبار تحميل الشحنات...")
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        # تحميل جميع الشحنات مع المورد
        shipments = session.query(Shipment).join(Supplier).filter(
            Shipment.is_active == True
        ).order_by(Shipment.created_at.desc()).limit(5).all()
        
        print(f"📊 تم العثور على {len(shipments)} شحنة")
        
        for i, shipment in enumerate(shipments, 1):
            print(f"\n   📦 الشحنة {i}:")
            print(f"      - رقم الشحنة: {shipment.shipment_number}")
            print(f"      - تاريخ الشحنة: {shipment.shipment_date}")
            print(f"      - المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}")
            print(f"      - حالة الشحنة: {shipment.shipment_status}")
            print(f"      - تاريخ الإنشاء: {shipment.created_at}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل الشحنات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()

def test_database_schema():
    """اختبار مخطط قاعدة البيانات"""
    print("\n🧪 اختبار مخطط قاعدة البيانات...")
    
    db_manager = DatabaseManager()
    
    try:
        from sqlalchemy import text
        engine = db_manager.engine
        
        with engine.connect() as connection:
            # التحقق من أعمدة جدول الشحنات
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            columns = [row[1] for row in result.fetchall()]
            
            print(f"📋 إجمالي الأعمدة: {len(columns)}")
            
            # التحقق من وجود الحقول المطلوبة
            required_fields = ['shipment_number', 'shipment_date']
            for field in required_fields:
                if field in columns:
                    print(f"   ✅ {field}: موجود")
                else:
                    print(f"   ❌ {field}: غير موجود")
                    return False
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المخطط: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== اختبار إصلاحات الشحنات ===")
    
    # اختبار مخطط قاعدة البيانات
    if not test_database_schema():
        print("\n❌ فشل في اختبار مخطط قاعدة البيانات!")
        sys.exit(1)
    
    # اختبار حقل رقم الشحنة
    if not test_shipment_number_field():
        print("\n❌ فشل في اختبار حقل رقم الشحنة!")
        sys.exit(1)
    
    # اختبار حقل تاريخ الشحنة
    if not test_shipment_date_field():
        print("\n❌ فشل في اختبار حقل تاريخ الشحنة!")
        sys.exit(1)
    
    # اختبار تحميل الشحنات
    if not test_shipment_loading():
        print("\n❌ فشل في اختبار تحميل الشحنات!")
        sys.exit(1)
    
    print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
    print("✅ رقم الشحنة: يعمل بشكل صحيح")
    print("✅ تاريخ الشحنة: يعمل بشكل صحيح")
    print("✅ تحميل الشحنات: يعمل بشكل صحيح")
