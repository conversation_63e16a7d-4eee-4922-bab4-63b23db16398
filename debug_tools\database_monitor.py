#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة مراقبة قاعدة البيانات المتخصصة
Specialized Database Monitoring Tool
"""

import sys
import os
import time
import threading
from datetime import datetime
import sqlite3
from contextlib import contextmanager

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DatabaseMonitor:
    """مراقب قاعدة البيانات"""
    
    def __init__(self):
        self.monitoring = False
        self.connection_count = 0
        self.active_queries = []
        self.lock_info = []
        self.debug_log = []
        
    def log(self, message):
        """تسجيل رسالة"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        self.debug_log.append(log_entry)
        print(f"🗄️ {log_entry}")
        
    def start_monitoring(self):
        """بدء مراقبة قاعدة البيانات"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_database, daemon=True)
        self.monitor_thread.start()
        self.log("بدء مراقبة قاعدة البيانات")
        
    def stop_monitoring(self):
        """إيقاف مراقبة قاعدة البيانات"""
        self.monitoring = False
        self.log("إيقاف مراقبة قاعدة البيانات")
        
    def _monitor_database(self):
        """مراقبة قاعدة البيانات"""
        while self.monitoring:
            try:
                self._check_connections()
                self._check_locks()
                self._check_long_running_queries()
                time.sleep(2)
            except Exception as e:
                self.log(f"خطأ في مراقبة قاعدة البيانات: {e}")
                
    def _check_connections(self):
        """فحص الاتصالات"""
        try:
            from src.database.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # فحص مجموعة الاتصالات
            if hasattr(db_manager.engine.pool, 'size'):
                pool_size = db_manager.engine.pool.size()
                if hasattr(db_manager.engine.pool, 'checkedout'):
                    checked_out_obj = db_manager.engine.pool.checkedout()
                    checked_out = len(checked_out_obj) if hasattr(checked_out_obj, '__len__') else checked_out_obj
                else:
                    checked_out = 0

                if checked_out != self.connection_count:
                    self.connection_count = checked_out
                    self.log(f"📊 الاتصالات النشطة: {checked_out}/{pool_size}")
                    
        except Exception as e:
            self.log(f"خطأ في فحص الاتصالات: {e}")
            
    def _check_locks(self):
        """فحص الأقفال"""
        try:
            # فحص أقفال SQLite
            db_path = "shipment_management.db"
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path, timeout=1)
                try:
                    # محاولة قفل قاعدة البيانات
                    conn.execute("BEGIN IMMEDIATE")
                    conn.rollback()
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e):
                        self.log("🔒 قاعدة البيانات مقفلة!")
                finally:
                    conn.close()
                    
        except Exception as e:
            self.log(f"خطأ في فحص الأقفال: {e}")
            
    def _check_long_running_queries(self):
        """فحص الاستعلامات طويلة المدى"""
        # هذا مخصص لـ SQLite - يمكن توسيعه لقواعد بيانات أخرى
        pass
        
    @contextmanager
    def monitor_query(self, query_description):
        """مراقب الاستعلامات"""
        query_id = len(self.active_queries)
        start_time = time.time()
        
        self.active_queries.append({
            'id': query_id,
            'description': query_description,
            'start_time': start_time
        })
        
        self.log(f"🚀 بدء استعلام: {query_description}")
        
        try:
            yield
        except Exception as e:
            self.log(f"❌ خطأ في الاستعلام {query_description}: {e}")
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            # إزالة الاستعلام من القائمة النشطة
            self.active_queries = [q for q in self.active_queries if q['id'] != query_id]
            
            self.log(f"✅ انتهاء استعلام: {query_description} ({duration:.3f}s)")
            
    def test_database_operations(self):
        """اختبار عمليات قاعدة البيانات"""
        self.log("🔍 بدء اختبار عمليات قاعدة البيانات")
        
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Shipment
            
            db_manager = DatabaseManager()
            
            # اختبار إنشاء جلسة
            with self.monitor_query("إنشاء_جلسة"):
                session = db_manager.get_session()
                self.log("✅ تم إنشاء الجلسة")
                
            # اختبار استعلام بسيط
            with self.monitor_query("استعلام_بسيط"):
                count = session.query(Shipment).count()
                self.log(f"✅ عدد الشحنات: {count}")
                
            # اختبار استعلام معقد
            with self.monitor_query("استعلام_معقد"):
                shipments = session.query(Shipment).limit(5).all()
                self.log(f"✅ تم جلب {len(shipments)} شحنات")
                
            # اختبار تحديث
            if shipments:
                with self.monitor_query("تحديث_بيانات"):
                    shipment = shipments[0]
                    original_notes = shipment.notes
                    shipment.notes = f"تحديث اختبار - {datetime.now()}"
                    session.commit()
                    self.log("✅ تم التحديث")
                    
                    # إعادة البيانات الأصلية
                    shipment.notes = original_notes
                    session.commit()
                    self.log("✅ تم إعادة البيانات الأصلية")
                    
            # إغلاق الجلسة
            with self.monitor_query("إغلاق_جلسة"):
                session.close()
                self.log("✅ تم إغلاق الجلسة")
                
        except Exception as e:
            self.log(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
            import traceback
            traceback.print_exc()
            
    def analyze_database_performance(self):
        """تحليل أداء قاعدة البيانات"""
        self.log("📊 تحليل أداء قاعدة البيانات")
        
        try:
            db_path = "shipment_management.db"
            if not os.path.exists(db_path):
                self.log("❌ ملف قاعدة البيانات غير موجود")
                return
                
            # حجم قاعدة البيانات
            db_size = os.path.getsize(db_path) / 1024 / 1024  # MB
            self.log(f"📊 حجم قاعدة البيانات: {db_size:.2f} MB")
            
            # فحص الجداول
            conn = sqlite3.connect(db_path)
            try:
                cursor = conn.cursor()
                
                # قائمة الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                self.log(f"📊 عدد الجداول: {len(tables)}")
                
                # إحصائيات كل جدول
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    self.log(f"📊 {table_name}: {count} سجل")
                    
                # فحص الفهارس
                cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
                indexes = cursor.fetchall()
                self.log(f"📊 عدد الفهارس: {len(indexes)}")
                
            finally:
                conn.close()
                
        except Exception as e:
            self.log(f"❌ خطأ في تحليل الأداء: {e}")
            
    def save_monitor_log(self):
        """حفظ سجل المراقبة"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"debug_tools/database_monitor_{timestamp}.log"
        
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("سجل مراقبة قاعدة البيانات\n")
            f.write("=" * 60 + "\n")
            f.write(f"التاريخ: {datetime.now()}\n\n")
            
            for entry in self.debug_log:
                f.write(entry + "\n")
                
        self.log(f"💾 تم حفظ السجل: {log_file}")
        return log_file

# إنشاء مثيل عام
db_monitor = DatabaseMonitor()

def run_database_diagnostics():
    """تشغيل تشخيص قاعدة البيانات"""
    print("🗄️ بدء تشخيص قاعدة البيانات...")
    
    db_monitor.start_monitoring()
    
    try:
        # تحليل الأداء
        db_monitor.analyze_database_performance()
        
        # اختبار العمليات
        db_monitor.test_database_operations()
        
        # انتظار لمراقبة النشاط
        time.sleep(5)
        
    finally:
        db_monitor.stop_monitoring()
        log_file = db_monitor.save_monitor_log()
        
        print(f"\n✅ تم الانتهاء من تشخيص قاعدة البيانات")
        print(f"📋 سجل المراقبة: {log_file}")

if __name__ == "__main__":
    run_database_diagnostics()
