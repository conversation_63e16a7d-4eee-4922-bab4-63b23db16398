#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لنافذة الشحنة الجديدة مع أزرار التحكم
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_shipment_window():
    """اختبار نافذة الشحنة الجديدة"""
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = NewShipmentWindow()
    
    print("=== اختبار نافذة الشحنة الجديدة ===")
    print(f"عنوان النافذة: {window.windowTitle()}")
    print(f"حجم النافذة: {window.size().width()}x{window.size().height()}")
    
    # فحص شريط الأدوات
    if hasattr(window, 'toolbar'):
        print("✅ شريط الأدوات موجود")
        actions = window.toolbar.actions()
        print(f"عدد الأزرار في الشريط: {len(actions)}")
        
        for i, action in enumerate(actions):
            if action.isSeparator():
                print(f"   {i+1}. فاصل")
            else:
                print(f"   {i+1}. {action.text()} - {action.toolTip()}")
    else:
        print("❌ شريط الأدوات غير موجود")
    
    # فحص الأزرار القديمة
    old_buttons = ['new_button', 'save_button', 'edit_button', 'exit_button']
    for btn_name in old_buttons:
        if hasattr(window, btn_name):
            btn = getattr(window, btn_name)
            print(f"✅ {btn_name}: موجود - مرئي: {btn.isVisible()}")
        else:
            print(f"❌ {btn_name}: غير موجود")
    
    # عرض النافذة
    window.show()
    
    print("\n🔍 تحقق من النافذة:")
    print("   • يجب أن ترى شريط أدوات أزرق في الأعلى")
    print("   • يجب أن ترى أزرار: إضافة، حفظ، تعديل، خروج")
    print("   • يجب أن ترى أزرار إضافية في الأسفل")
    print("   • جرب الضغط على الأزرار لاختبار الوظائف")
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    test_shipment_window()
