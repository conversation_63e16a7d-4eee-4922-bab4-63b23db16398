#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشات العرض
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.shipments_window import ShipmentsWindow
from src.ui.shipments.shipment_tracking_window import ShipmentTrackingWindow
from src.database.database_manager import DatabaseManager
from src.database.models import Shipment

def test_display_screens():
    """اختبار شاشات العرض"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار شاشات العرض ===")
        
        # التحقق من وجود شحنات في قاعدة البيانات
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            shipments_count = session.query(Shipment).count()
            print(f"✅ عدد الشحنات في قاعدة البيانات: {shipments_count}")
            
            if shipments_count == 0:
                print("❌ لا توجد شحنات للاختبار")
                return False
                
            # اختبار شاشة إدارة الشحنات
            print("\n🔍 اختبار شاشة إدارة الشحنات...")
            shipments_window = ShipmentsWindow()
            shipments_window.load_shipments()
            
            table_rows = shipments_window.shipments_table.rowCount()
            print(f"✅ عدد الصفوف في جدول إدارة الشحنات: {table_rows}")
            
            if table_rows > 0:
                # عرض أول شحنة
                first_shipment_number = shipments_window.shipments_table.item(0, 1).text()
                first_supplier = shipments_window.shipments_table.item(0, 2).text()
                first_status = shipments_window.shipments_table.item(0, 3).text()
                print(f"   📋 أول شحنة: {first_shipment_number} - {first_supplier} - {first_status}")
            
            # اختبار شاشة تتبع الشحنات
            print("\n🔍 اختبار شاشة تتبع الشحنات...")
            tracking_window = ShipmentTrackingWindow()
            tracking_window.load_tracking_data()
            
            tracking_rows = tracking_window.tracking_table.rowCount()
            print(f"✅ عدد الصفوف في جدول تتبع الشحنات: {tracking_rows}")
            
            if tracking_rows > 0:
                # عرض أول شحنة في التتبع
                first_tracking_number = tracking_window.tracking_table.item(0, 1).text()
                first_tracking_supplier = tracking_window.tracking_table.item(0, 2).text()
                first_tracking_status = tracking_window.tracking_table.item(0, 3).text()
                print(f"   📋 أول شحنة في التتبع: {first_tracking_number} - {first_tracking_supplier} - {first_tracking_status}")
            
            # النتائج
            print("\n📊 نتائج الاختبار:")
            print(f"   • شحنات في قاعدة البيانات: {shipments_count}")
            print(f"   • شحنات في إدارة الشحنات: {table_rows}")
            print(f"   • شحنات في تتبع الشحنات: {tracking_rows}")
            
            if shipments_count == table_rows == tracking_rows:
                print("\n✅ جميع الشاشات تعرض البيانات بشكل صحيح!")
                return True
            else:
                print("\n⚠️ هناك تباين في عرض البيانات بين الشاشات")
                return False
                
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_display_screens()
