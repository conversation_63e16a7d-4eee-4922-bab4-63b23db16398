#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للتحديثات الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import():
    """اختبار استيراد الوحدات"""
    try:
        print("🔍 اختبار استيراد الوحدات...")
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")
        
        from src.database.database_manager import DatabaseManager
        print("✅ تم استيراد DatabaseManager بنجاح")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        return False

def test_window_creation():
    """اختبار إنشاء النافذة"""
    try:
        print("\n🔍 اختبار إنشاء النافذة...")

        from PySide6.QtWidgets import QApplication
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

        # إنشاء QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة في وضع الإدخال
        window = PurchaseOrdersWindow(maximize_on_start=False, mode="entry")
        print("✅ تم إنشاء النافذة بنجاح")
        
        # فحص وجود تبويب المستندات
        if hasattr(window, 'details_tabs'):
            print("✅ تبويبات التفاصيل موجودة")
            
            # البحث عن تبويب المستندات
            for i in range(window.details_tabs.count()):
                tab_text = window.details_tabs.tabText(i)
                if "المستندات" in tab_text:
                    print(f"✅ تبويب المستندات موجود: '{tab_text}'")
                    break
            else:
                print("❌ تبويب المستندات غير موجود")
        else:
            print("❌ تبويبات التفاصيل غير موجودة")
        
        # فحص الحقول الجديدة
        print("\n📋 فحص الحقول الجديدة...")
        
        fields_to_check = [
            ('contract_url_display', 'حقل عرض رابط العقد'),
            ('contract_add_link_btn', 'زر إضافة رابط العقد'),
            ('contract_add_attachment_btn', 'زر إضافة مرفق العقد'),
            ('initial_designs_url_display', 'حقل عرض رابط التصاميم الأولية'),
            ('initial_designs_add_link_btn', 'زر إضافة رابط التصاميم الأولية'),
            ('initial_designs_add_attachment_btn', 'زر إضافة مرفق التصاميم الأولية'),
        ]
        
        for field_name, field_desc in fields_to_check:
            if hasattr(window, field_name):
                field = getattr(window, field_name)
                print(f"✅ {field_desc}: موجود")
                
                # إذا كان زر، اطبع النص
                if hasattr(field, 'text'):
                    print(f"   📝 النص: '{field.text()}'")
            else:
                print(f"❌ {field_desc}: غير موجود")
        
        # فحص الدوال الجديدة
        print("\n🔧 فحص الدوال الجديدة...")
        
        functions_to_check = [
            ('add_link_dialog', 'دالة حوار إضافة رابط'),
            ('add_attachment_with_manager', 'دالة إضافة مرفق بالمدير'),
            ('get_document_url', 'دالة الحصول على رابط المستند'),
            ('update_document_display', 'دالة تحديث عرض المستند')
        ]
        
        for func_name, func_desc in functions_to_check:
            if hasattr(window, func_name):
                print(f"✅ {func_desc}: موجودة")
            else:
                print(f"❌ {func_desc}: غير موجودة")
        
        print("\n✅ انتهى الاختبار بنجاح!")

        # إغلاق التطبيق
        if app:
            app.quit()

        return window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء الاختبار المبسط...")
    
    # اختبار الاستيراد
    if not test_import():
        print("❌ فشل في اختبار الاستيراد")
        return
    
    # اختبار إنشاء النافذة
    window = test_window_creation()
    
    if window:
        print("\n📝 ملخص النتائج:")
        print("   ✅ تم إنشاء النافذة بنجاح")
        print("   ✅ تم العثور على الحقول والأزرار الجديدة")
        print("   ✅ تم العثور على الدوال الجديدة")
        print("\n🎉 التحديثات تعمل بشكل صحيح!")
    else:
        print("\n❌ فشل في إنشاء النافذة")

if __name__ == "__main__":
    main()
