#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تكامل نظام تحديث كميات طلبات الشراء مع واجهة المستخدم
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_purchase_order_integration():
    """اختبار تكامل النظام مع واجهة المستخدم"""
    try:
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        from src.database.models import PurchaseOrder, PurchaseOrderItem, Supplier, Item
        from src.database.database_manager import DatabaseManager
        from datetime import datetime
        
        print("🧪 اختبار تكامل نظام تحديث كميات طلبات الشراء")
        print("=" * 60)
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن طلب شراء موجود
        purchase_order = session.query(PurchaseOrder).first()
        if not purchase_order:
            print("❌ لا توجد طلبات شراء في قاعدة البيانات")
            return False
            
        print(f"✅ تم العثور على طلب الشراء: {purchase_order.order_number}")
        
        # البحث عن أصناف الطلب
        order_items = session.query(PurchaseOrderItem).filter(
            PurchaseOrderItem.purchase_order_id == purchase_order.id
        ).all()
        
        if not order_items:
            print("❌ لا توجد أصناف في طلب الشراء")
            return False
            
        print(f"✅ تم العثور على {len(order_items)} صنف في الطلب")
        
        # عرض حالة الكميات قبل الشحن
        print(f"\n📊 حالة الكميات قبل الشحن:")
        for item in order_items:
            print(f"   - الصنف: {item.item.name if item.item else 'غير محدد'}")
            print(f"     الكمية المطلوبة: {item.quantity}")
            print(f"     الكمية المسلمة: {item.delivered_quantity}")
            print(f"     الكمية المتبقية: {item.remaining_quantity}")
            print()
        
        # إنشاء نافذة الشحنة الجديدة
        shipment_window = NewShipmentWindow()
        
        # تحميل بيانات طلب الشراء
        print(f"🔄 تحميل بيانات طلب الشراء في نافذة الشحنة...")
        shipment_window.load_from_purchase_order(purchase_order.id)
        
        # التحقق من تحميل البيانات
        items_count = shipment_window.items_table.rowCount()
        print(f"✅ تم تحميل {items_count} صنف في جدول الشحنة")
        
        # التحقق من وجود معرفات طلب الشراء
        print(f"\n🔍 التحقق من ربط أصناف الشحنة بطلب الشراء:")
        for row in range(items_count):
            item_id = shipment_window.items_table.item(row, 0).data(Qt.UserRole)
            po_item_id = shipment_window.items_table.item(row, 0).data(Qt.UserRole + 1)
            item_name = shipment_window.items_table.item(row, 1).text()
            
            print(f"   - الصنف: {item_name}")
            print(f"     معرف الصنف: {item_id}")
            print(f"     معرف صنف طلب الشراء: {po_item_id}")
            
            if po_item_id:
                print("     ✅ مرتبط بطلب الشراء")
            else:
                print("     ❌ غير مرتبط بطلب الشراء")
            print()
        
        # اختبار دالة تحديث الكميات
        print(f"🧪 اختبار دالة تحديث الكميات...")
        
        # محاكاة تحديث كمية
        if order_items:
            test_item = order_items[0]
            original_delivered = test_item.delivered_quantity
            test_quantity = 5.0
            
            print(f"   - الصنف المختبر: {test_item.item.name if test_item.item else 'غير محدد'}")
            print(f"   - الكمية المسلمة الأصلية: {original_delivered}")
            print(f"   - كمية الاختبار: {test_quantity}")
            
            # استدعاء دالة التحديث
            shipment_window.update_purchase_order_quantities(
                session, test_item.id, test_quantity
            )
            
            # التحقق من التحديث
            session.refresh(test_item)
            new_delivered = test_item.delivered_quantity
            new_remaining = test_item.remaining_quantity
            
            print(f"   - الكمية المسلمة الجديدة: {new_delivered}")
            print(f"   - الكمية المتبقية الجديدة: {new_remaining}")
            
            if new_delivered == original_delivered + test_quantity:
                print("   ✅ تم تحديث الكمية المسلمة بشكل صحيح")
            else:
                print("   ❌ خطأ في تحديث الكمية المسلمة")
                
            expected_remaining = test_item.quantity - new_delivered
            if abs(new_remaining - expected_remaining) < 0.01:
                print("   ✅ تم حساب الكمية المتبقية بشكل صحيح")
            else:
                print("   ❌ خطأ في حساب الكمية المتبقية")
        
        session.close()
        
        # عرض النافذة للمراجعة اليدوية
        print(f"\n👀 عرض النافذة للمراجعة اليدوية...")
        print("   يمكنك الآن:")
        print("   1. مراجعة البيانات المحملة")
        print("   2. تعديل الكميات إذا لزم الأمر")
        print("   3. حفظ الشحنة لاختبار تحديث الكميات")
        print("   4. إغلاق النافذة عند الانتهاء")
        
        shipment_window.show()
        
        # رسالة للمستخدم
        QMessageBox.information(
            shipment_window,
            "اختبار تكامل النظام",
            "✅ تم تحميل بيانات طلب الشراء بنجاح!\n\n"
            "الميزات المتاحة:\n"
            "• تحديث كميات طلب الشراء تلقائياً عند الحفظ\n"
            "• عرض الكميات المسلمة والمتبقية\n"
            "• ربط أصناف الشحنة بطلب الشراء\n\n"
            "يمكنك الآن حفظ الشحنة لاختبار النظام."
        )
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        return False

def show_purchase_order_status():
    """عرض حالة طلبات الشراء"""
    try:
        from src.database.models import PurchaseOrder, PurchaseOrderItem
        from src.database.database_manager import DatabaseManager
        
        print("📊 حالة طلبات الشراء الحالية:")
        print("=" * 60)
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        orders = session.query(PurchaseOrder).all()
        
        if not orders:
            print("❌ لا توجد طلبات شراء في قاعدة البيانات")
            return
            
        for order in orders:
            print(f"\n🛒 طلب الشراء: {order.order_number}")
            print(f"   المورد: {order.supplier.name if order.supplier else 'غير محدد'}")
            print(f"   التاريخ: {order.order_date}")
            print(f"   الحالة: {order.order_status}")
            
            items = session.query(PurchaseOrderItem).filter(
                PurchaseOrderItem.purchase_order_id == order.id
            ).all()
            
            if items:
                print(f"   الأصناف ({len(items)}):")
                for item in items:
                    completion_rate = 0
                    if item.quantity > 0:
                        completion_rate = (item.delivered_quantity / item.quantity) * 100
                        
                    print(f"     • {item.item.name if item.item else 'غير محدد'}")
                    print(f"       المطلوب: {item.quantity} | المسلم: {item.delivered_quantity} | المتبقي: {item.remaining_quantity}")
                    print(f"       نسبة الإنجاز: {completion_rate:.1f}%")
            else:
                print("   لا توجد أصناف")
                
        session.close()
        
    except Exception as e:
        print(f"❌ خطأ في عرض حالة طلبات الشراء: {str(e)}")

if __name__ == "__main__":
    print("🚀 اختبار تكامل نظام تحديث كميات طلبات الشراء")
    print("=" * 60)
    
    # عرض حالة طلبات الشراء
    show_purchase_order_status()
    
    print("\n" + "=" * 60)
    
    # اختبار التكامل
    test_purchase_order_integration()
