"""
نظام ذكي لتعبئة الحقول الفارغة في بيانات الشحنات
يستخدم البيانات الموجودة للبحث والتطابق وتعبئة الحقول المفقودة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database_manager import DatabaseManager
from database.models import Shipment, Container
from sqlalchemy import and_, or_
from typing import Dict, List, Optional, Tuple
from difflib import SequenceMatcher
import re
import asyncio

class ShipmentDataFiller:
    """فئة لتعبئة البيانات المفقودة في الشحنات"""
    
    def __init__(self):
        """تهيئة النظام"""
        self.db_manager = DatabaseManager()
        self.similarity_threshold = 0.7  # حد التشابه للمطابقة
        self.fill_log = []  # سجل التعبئة
        self._active_sessions = set()  # تتبع الجلسات النشطة

        # خدمة البحث عبر الإنترنت
        try:
            from services.web_scraping_service import WebScrapingService
            self.web_scraper = WebScrapingService()
            self.web_scraping_enabled = True
        except ImportError:
            self.web_scraper = None
            self.web_scraping_enabled = False

    def get_safe_session(self):
        """إنشاء جلسة آمنة ومعزولة"""
        try:
            # إنشاء مدير قاعدة بيانات منفصل لتجنب التداخل
            isolated_db_manager = DatabaseManager()
            session = isolated_db_manager.get_session()
            session_id = id(session)
            self._active_sessions.add(session_id)
            return session, session_id
        except Exception as e:
            print(f"خطأ في إنشاء جلسة آمنة: {e}")
            return None, None

    def close_safe_session(self, session, session_id):
        """إغلاق جلسة آمنة"""
        if session and session_id:
            try:
                session.close()
                self._active_sessions.discard(session_id)
            except Exception as e:
                print(f"خطأ في إغلاق الجلسة الآمنة: {e}")

    def analyze_missing_data(self) -> Dict:
        """تحليل البيانات المفقودة في الشحنات"""
        session, session_id = self.get_safe_session()
        analysis = {
            "total_shipments": 0,
            "missing_fields": {},
            "fillable_fields": {},
            "patterns": {}
        }

        if not session:
            return analysis

        try:
            shipments = session.query(Shipment).all()
            analysis["total_shipments"] = len(shipments)
            
            # الحقول المهمة للتحليل
            important_fields = [
                'shipping_company', 'container_number', 'bill_of_lading',
                'tracking_number', 'vessel_name', 'voyage_number',
                'port_of_loading', 'port_of_discharge', 'port_of_arrival',
                'final_destination', 'shipping_method', 'dhl_number'
            ]
            
            # تحليل الحقول المفقودة
            for field in important_fields:
                missing_count = 0
                for shipment in shipments:
                    value = getattr(shipment, field, None)
                    if not value or value.strip() == "":
                        missing_count += 1
                
                analysis["missing_fields"][field] = {
                    "count": missing_count,
                    "percentage": (missing_count / len(shipments)) * 100 if shipments else 0
                }
            
            # تحليل الأنماط للتعبئة
            analysis["patterns"] = self._analyze_patterns(shipments)
            
            return analysis
            
        except Exception as e:
            print(f"خطأ في تحليل البيانات: {e}")
            return analysis
        finally:
            self.close_safe_session(session, session_id)
    
    def _analyze_patterns(self, shipments: List[Shipment]) -> Dict:
        """تحليل الأنماط في البيانات"""
        patterns = {
            "shipping_company_containers": {},  # شركة الشحن -> أرقام الحاويات
            "container_vessel": {},  # رقم الحاوية -> اسم السفينة
            "vessel_voyage": {},  # اسم السفينة -> رقم الرحلة
            "company_ports": {},  # شركة الشحن -> الموانئ
            "supplier_shipping": {}  # المورد -> شركة الشحن المعتادة
        }
        
        for shipment in shipments:
            # ربط شركة الشحن بأرقام الحاويات
            if shipment.shipping_company and shipment.container_number:
                company = shipment.shipping_company.strip()
                container = shipment.container_number.strip()
                
                if company not in patterns["shipping_company_containers"]:
                    patterns["shipping_company_containers"][company] = set()
                patterns["shipping_company_containers"][company].add(container)
            
            # ربط رقم الحاوية باسم السفينة
            if shipment.container_number and shipment.vessel_name:
                container = shipment.container_number.strip()
                vessel = shipment.vessel_name.strip()
                patterns["container_vessel"][container] = vessel
            
            # ربط اسم السفينة برقم الرحلة
            if shipment.vessel_name and shipment.voyage_number:
                vessel = shipment.vessel_name.strip()
                voyage = shipment.voyage_number.strip()
                patterns["vessel_voyage"][vessel] = voyage
            
            # ربط شركة الشحن بالموانئ
            if shipment.shipping_company:
                company = shipment.shipping_company.strip()
                if company not in patterns["company_ports"]:
                    patterns["company_ports"][company] = {
                        "loading": set(),
                        "discharge": set(),
                        "arrival": set()
                    }
                
                if shipment.port_of_loading:
                    patterns["company_ports"][company]["loading"].add(shipment.port_of_loading.strip())
                if shipment.port_of_discharge:
                    patterns["company_ports"][company]["discharge"].add(shipment.port_of_discharge.strip())
                if shipment.port_of_arrival:
                    patterns["company_ports"][company]["arrival"].add(shipment.port_of_arrival.strip())
            
            # ربط المورد بشركة الشحن المعتادة
            if shipment.supplier_id and shipment.shipping_company:
                supplier_id = shipment.supplier_id
                company = shipment.shipping_company.strip()
                
                if supplier_id not in patterns["supplier_shipping"]:
                    patterns["supplier_shipping"][supplier_id] = {}
                
                if company not in patterns["supplier_shipping"][supplier_id]:
                    patterns["supplier_shipping"][supplier_id][company] = 0
                patterns["supplier_shipping"][supplier_id][company] += 1
        
        # تحويل المجموعات إلى قوائم للتسلسل
        for company in patterns["shipping_company_containers"]:
            patterns["shipping_company_containers"][company] = list(patterns["shipping_company_containers"][company])
        
        for company in patterns["company_ports"]:
            for port_type in patterns["company_ports"][company]:
                patterns["company_ports"][company][port_type] = list(patterns["company_ports"][company][port_type])
        
        return patterns
    
    def find_similar_shipments(self, search_criteria: Dict) -> List[Tuple[Shipment, float]]:
        """البحث عن شحنات مشابهة بناءً على معايير البحث"""
        session = self.db_manager.get_session()
        similar_shipments = []
        
        try:
            # بناء استعلام البحث
            query = session.query(Shipment)
            conditions = []
            
            # البحث بشركة الشحن
            if search_criteria.get('shipping_company'):
                conditions.append(Shipment.shipping_company.ilike(f"%{search_criteria['shipping_company']}%"))
            
            # البحث برقم الحاوية
            if search_criteria.get('container_number'):
                conditions.append(Shipment.container_number.ilike(f"%{search_criteria['container_number']}%"))
            
            # البحث ببوليصة الشحن
            if search_criteria.get('bill_of_lading'):
                conditions.append(Shipment.bill_of_lading.ilike(f"%{search_criteria['bill_of_lading']}%"))
            
            # البحث برقم التتبع
            if search_criteria.get('tracking_number'):
                conditions.append(Shipment.tracking_number.ilike(f"%{search_criteria['tracking_number']}%"))
            
            # البحث باسم السفينة
            if search_criteria.get('vessel_name'):
                conditions.append(Shipment.vessel_name.ilike(f"%{search_criteria['vessel_name']}%"))
            
            if conditions:
                query = query.filter(or_(*conditions))
                shipments = query.all()
                
                # حساب درجة التشابه
                for shipment in shipments:
                    similarity = self._calculate_similarity(shipment, search_criteria)
                    if similarity >= self.similarity_threshold:
                        similar_shipments.append((shipment, similarity))
                
                # ترتيب حسب درجة التشابه
                similar_shipments.sort(key=lambda x: x[1], reverse=True)
            
            return similar_shipments
            
        except Exception as e:
            print(f"خطأ في البحث عن الشحنات المشابهة: {e}")
            return []
        finally:
            session.close()
    
    def _calculate_similarity(self, shipment: Shipment, criteria: Dict) -> float:
        """حساب درجة التشابه بين الشحنة ومعايير البحث"""
        similarities = []
        
        # مقارنة شركة الشحن
        if criteria.get('shipping_company') and shipment.shipping_company:
            sim = SequenceMatcher(None, 
                                criteria['shipping_company'].lower(),
                                shipment.shipping_company.lower()).ratio()
            similarities.append(sim)
        
        # مقارنة رقم الحاوية
        if criteria.get('container_number') and shipment.container_number:
            sim = SequenceMatcher(None,
                                criteria['container_number'].lower(),
                                shipment.container_number.lower()).ratio()
            similarities.append(sim)
        
        # مقارنة بوليصة الشحن
        if criteria.get('bill_of_lading') and shipment.bill_of_lading:
            sim = SequenceMatcher(None,
                                criteria['bill_of_lading'].lower(),
                                shipment.bill_of_lading.lower()).ratio()
            similarities.append(sim)
        
        # مقارنة رقم التتبع
        if criteria.get('tracking_number') and shipment.tracking_number:
            sim = SequenceMatcher(None,
                                criteria['tracking_number'].lower(),
                                shipment.tracking_number.lower()).ratio()
            similarities.append(sim)
        
        # مقارنة اسم السفينة
        if criteria.get('vessel_name') and shipment.vessel_name:
            sim = SequenceMatcher(None,
                                criteria['vessel_name'].lower(),
                                shipment.vessel_name.lower()).ratio()
            similarities.append(sim)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
    
    def suggest_field_values(self, shipment_id: int, field_name: str) -> List[Dict]:
        """اقتراح قيم للحقل المحدد بناءً على البيانات المشابهة"""
        session = self.db_manager.get_session()
        suggestions = []
        
        try:
            target_shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
            if not target_shipment:
                return suggestions
            
            # بناء معايير البحث من البيانات الموجودة
            search_criteria = {}
            if target_shipment.shipping_company:
                search_criteria['shipping_company'] = target_shipment.shipping_company
            if target_shipment.container_number:
                search_criteria['container_number'] = target_shipment.container_number
            if target_shipment.bill_of_lading:
                search_criteria['bill_of_lading'] = target_shipment.bill_of_lading
            if target_shipment.vessel_name:
                search_criteria['vessel_name'] = target_shipment.vessel_name
            
            # البحث عن شحنات مشابهة
            similar_shipments = self.find_similar_shipments(search_criteria)
            
            # جمع القيم المقترحة
            value_counts = {}
            for similar_shipment, similarity in similar_shipments:
                value = getattr(similar_shipment, field_name, None)
                if value and value.strip():
                    value = value.strip()
                    if value not in value_counts:
                        value_counts[value] = {
                            'count': 0,
                            'total_similarity': 0,
                            'shipment_ids': []
                        }
                    value_counts[value]['count'] += 1
                    value_counts[value]['total_similarity'] += similarity
                    value_counts[value]['shipment_ids'].append(similar_shipment.id)
            
            # ترتيب الاقتراحات
            for value, data in value_counts.items():
                avg_similarity = data['total_similarity'] / data['count']
                confidence = (data['count'] * avg_similarity) / len(similar_shipments) if similar_shipments else 0
                
                suggestions.append({
                    'value': value,
                    'confidence': confidence,
                    'count': data['count'],
                    'avg_similarity': avg_similarity,
                    'source_shipments': data['shipment_ids'][:3]  # أول 3 شحنات مصدر
                })
            
            # ترتيب حسب الثقة
            suggestions.sort(key=lambda x: x['confidence'], reverse=True)
            
            return suggestions[:5]  # أفضل 5 اقتراحات
            
        except Exception as e:
            print(f"خطأ في اقتراح القيم: {e}")
            return []
        finally:
            session.close()
    
    def auto_fill_missing_fields(self, shipment_id: int, confidence_threshold: float = 0.8) -> Dict:
        """تعبئة تلقائية للحقول المفقودة"""
        session = self.db_manager.get_session()
        fill_result = {
            'shipment_id': shipment_id,
            'filled_fields': {},
            'skipped_fields': {},
            'errors': []
        }
        
        try:
            shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
            if not shipment:
                fill_result['errors'].append('الشحنة غير موجودة')
                return fill_result
            
            # الحقول المراد تعبئتها
            fields_to_fill = [
                'shipping_company', 'container_number', 'bill_of_lading',
                'tracking_number', 'vessel_name', 'voyage_number',
                'port_of_loading', 'port_of_discharge', 'port_of_arrival',
                'final_destination', 'shipping_method', 'dhl_number'
            ]
            
            for field in fields_to_fill:
                current_value = getattr(shipment, field, None)
                
                # تخطي الحقول التي تحتوي على قيم
                if current_value and current_value.strip():
                    continue
                
                # الحصول على اقتراحات
                suggestions = self.suggest_field_values(shipment_id, field)
                
                if suggestions and suggestions[0]['confidence'] >= confidence_threshold:
                    best_suggestion = suggestions[0]
                    
                    # تطبيق القيمة المقترحة
                    setattr(shipment, field, best_suggestion['value'])
                    
                    fill_result['filled_fields'][field] = {
                        'value': best_suggestion['value'],
                        'confidence': best_suggestion['confidence'],
                        'source_count': best_suggestion['count']
                    }
                    
                    # إضافة إلى سجل التعبئة
                    self.fill_log.append({
                        'shipment_id': shipment_id,
                        'field': field,
                        'value': best_suggestion['value'],
                        'confidence': best_suggestion['confidence'],
                        'timestamp': self.db_manager.get_current_timestamp()
                    })
                else:
                    fill_result['skipped_fields'][field] = {
                        'reason': 'ثقة منخفضة' if suggestions else 'لا توجد اقتراحات',
                        'best_confidence': suggestions[0]['confidence'] if suggestions else 0
                    }
            
            # حفظ التغييرات
            if fill_result['filled_fields']:
                session.commit()
            
            return fill_result
            
        except Exception as e:
            session.rollback()
            fill_result['errors'].append(str(e))
            return fill_result
        finally:
            session.close()
    
    def get_fill_log(self) -> List[Dict]:
        """الحصول على سجل التعبئة"""
        return self.fill_log.copy()
    
    def clear_fill_log(self):
        """مسح سجل التعبئة"""
        self.fill_log.clear()

    def generate_fill_report(self) -> str:
        """إنشاء تقرير مفصل عن عملية التعبئة"""
        if not self.fill_log:
            return "لا توجد عمليات تعبئة مسجلة"

        report = "📋 تقرير تعبئة البيانات المفقودة\n"
        report += "=" * 50 + "\n\n"

        # إحصائيات عامة
        total_fills = len(self.fill_log)
        fields_filled = set(entry['field'] for entry in self.fill_log)
        shipments_affected = set(entry['shipment_id'] for entry in self.fill_log)

        report += f"📊 الإحصائيات العامة:\n"
        report += f"   • إجمالي التعبئات: {total_fills}\n"
        report += f"   • الحقول المتأثرة: {len(fields_filled)}\n"
        report += f"   • الشحنات المتأثرة: {len(shipments_affected)}\n\n"

        # تفاصيل التعبئة
        report += f"📝 تفاصيل التعبئة:\n"
        for entry in self.fill_log:
            confidence_percent = int(entry['confidence'] * 100)
            report += f"   • الشحنة {entry['shipment_id']}: {entry['field']} = '{entry['value']}' (ثقة: {confidence_percent}%)\n"

        return report

    async def search_web_data(self, container_number: str = None, bill_of_lading: str = None,
                             carrier_name: str = None) -> Dict:
        """البحث عبر الإنترنت عن بيانات الشحنة"""
        if not self.web_scraping_enabled:
            return {
                'success': False,
                'error': 'خدمة البحث عبر الإنترنت غير متوفرة',
                'results': []
            }

        try:
            # تشغيل البحث المحسن
            search_result = await self.web_scraper.enhanced_search({
                'container_number': container_number,
                'bill_of_lading': bill_of_lading,
                'carrier_name': carrier_name
            })

            return {
                'success': True,
                'results': search_result['results'],
                'analysis': search_result['analysis'],
                'best_data': search_result['analysis']['best_data'],
                'confidence_scores': search_result['analysis']['confidence_scores'],
                'recommendations': search_result['analysis']['recommendations']
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في البحث عبر الإنترنت: {str(e)}',
                'results': []
            }

    def fill_from_web_data(self, shipment_id: int, web_data: Dict) -> Dict:
        """تعبئة بيانات الشحنة من البيانات المستخرجة من الويب"""
        session = self.db_manager.get_session()

        try:
            # الحصول على الشحنة
            shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
            if not shipment:
                return {
                    'success': False,
                    'error': 'الشحنة غير موجودة'
                }

            # تحويل بيانات الشحنة إلى قاموس
            shipment_dict = {
                'shipping_company': shipment.shipping_company,
                'vessel_name': shipment.vessel_name,
                'voyage_number': shipment.voyage_number,
                'origin_port': shipment.origin_port,
                'destination_port': shipment.destination_port,
                'departure_date': shipment.departure_date,
                'arrival_date': shipment.arrival_date,
                'status': shipment.status,
                'weight': shipment.weight,
                'volume': shipment.volume,
                'commodity': shipment.commodity,
                'consignee': shipment.consignee,
                'shipper': shipment.shipper
            }

            # دمج البيانات
            if self.web_scraper and web_data.get('best_data'):
                # إنشاء كائن ShipmentData مؤقت
                from services.web_scraping_service import ShipmentData
                web_shipment_data = ShipmentData()

                best_data = web_data['best_data']
                for key, value in best_data.items():
                    if hasattr(web_shipment_data, key):
                        setattr(web_shipment_data, key, value)

                # دمج البيانات
                merged_data = self.web_scraper.merge_shipment_data(shipment_dict, web_shipment_data)

                # تطبيق البيانات المدموجة
                fields_updated = []
                for field, value in merged_data.items():
                    if hasattr(shipment, field) and value:
                        old_value = getattr(shipment, field)
                        if not old_value or old_value == '':
                            setattr(shipment, field, value)
                            fields_updated.append(field)

                # حفظ التغييرات
                if fields_updated:
                    session.commit()

                    # تسجيل التعبئة
                    log_entry = {
                        'shipment_id': shipment_id,
                        'source': 'web_scraping',
                        'fields_filled': fields_updated,
                        'confidence': web_data.get('confidence_scores', []),
                        'timestamp': self._get_current_timestamp()
                    }
                    self.fill_log.append(log_entry)

                    return {
                        'success': True,
                        'fields_updated': fields_updated,
                        'total_fields': len(fields_updated),
                        'confidence_scores': web_data.get('confidence_scores', [])
                    }
                else:
                    return {
                        'success': True,
                        'fields_updated': [],
                        'total_fields': 0,
                        'message': 'لا توجد حقول فارغة للتعبئة'
                    }
            else:
                return {
                    'success': False,
                    'error': 'لا توجد بيانات ويب للدمج'
                }

        except Exception as e:
            session.rollback()
            return {
                'success': False,
                'error': f'خطأ في تعبئة البيانات: {str(e)}'
            }
        finally:
            session.close()

    def search_similar_shipments(self, search_criteria):
        """البحث عن الشحنات المشابهة بناءً على معايير البحث"""
        try:
            session = self.db_manager.get_session()
            query = session.query(Shipment).filter(Shipment.is_active == True)

            # تطبيق معايير البحث
            if 'container_number' in search_criteria and search_criteria['container_number']:
                query = query.filter(Shipment.container_number.ilike(f"%{search_criteria['container_number']}%"))

            if 'shipping_company' in search_criteria and search_criteria['shipping_company']:
                query = query.filter(Shipment.shipping_company.ilike(f"%{search_criteria['shipping_company']}%"))

            if 'bill_of_lading' in search_criteria and search_criteria['bill_of_lading']:
                query = query.filter(Shipment.bill_of_lading.ilike(f"%{search_criteria['bill_of_lading']}%"))

            # ترتيب النتائج حسب تاريخ الإنشاء
            query = query.order_by(Shipment.created_at.desc())

            # الحصول على النتائج
            shipments = query.limit(50).all()  # تحديد عدد النتائج لتجنب الحمل الزائد

            session.close()
            return shipments

        except Exception as e:
            print(f"خطأ في البحث عن الشحنات المشابهة: {str(e)}")
            return []

def main():
    """دالة رئيسية للاختبار"""
    filler = ShipmentDataFiller()
    
    print("🔍 تحليل البيانات المفقودة...")
    analysis = filler.analyze_missing_data()
    
    print(f"📊 إجمالي الشحنات: {analysis['total_shipments']}")
    print("\n📋 الحقول المفقودة:")
    
    for field, data in analysis['missing_fields'].items():
        if data['count'] > 0:
            print(f"   • {field}: {data['count']} ({data['percentage']:.1f}%)")
    
    print("\n🔍 اختبار البحث...")
    similar = filler.find_similar_shipments({
        'shipping_company': 'MSC'
    })
    
    print(f"   وجد {len(similar)} شحنة مشابهة لـ MSC")

    # اختبار البحث عبر الإنترنت
    if filler.web_scraping_enabled:
        print("\n🌐 اختبار البحث عبر الإنترنت...")

        async def test_web_search():
            result = await filler.search_web_data(
                container_number='MSKU1234567',
                carrier_name='Maersk'
            )

            if result['success']:
                print(f"   ✅ تم العثور على {len(result['results'])} نتيجة")
                if result['best_data']:
                    print("   📊 أفضل البيانات:")
                    for key, value in result['best_data'].items():
                        if value:
                            print(f"      {key}: {value}")
            else:
                print(f"   ❌ فشل البحث: {result['error']}")

        try:
            asyncio.run(test_web_search())
        except Exception as e:
            print(f"   ❌ خطأ في اختبار البحث عبر الإنترنت: {e}")
    else:
        print("\n⚠️ البحث عبر الإنترنت غير متوفر")

if __name__ == "__main__":
    main()
