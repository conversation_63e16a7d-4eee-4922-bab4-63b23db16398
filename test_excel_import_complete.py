#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لميزة استيراد الإكسيل المتقدمة
"""

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_excel_file():
    """إنشاء ملف إكسيل للاختبار"""
    try:
        import pandas as pd
        from datetime import datetime, timedelta
        
        # بيانات الاختبار
        test_data = {
            'التاريخ': [
                '2024-01-15',
                '2024-01-20', 
                '2024-01-25',
                '2024-02-01',
                '2024-02-10',
                '2024-02-15'
            ],
            'المورد': [
                'شركة الإمارات للتجارة',
                'مؤسسة الخليج التجارية',
                'شركة الشرق الأوسط',
                'مجموعة النور التجارية',
                'شركة الأطلس للاستيراد',
                'مؤسسة البحر الأحمر'
            ],
            'بوليصة الشحن': [
                'BL-2024-001',
                'BL-2024-002',
                'BL-2024-003',
                'BL-2024-004',
                'BL-2024-005',
                'BL-2024-006'
            ],
            'ملاحظات': [
                'شحنة عاجلة - أولوية عالية',
                'بضائع قابلة للكسر - احتياط خاص',
                'شحنة عادية',
                'تحتاج فحص جمركي',
                'بضائع مبردة',
                'شحنة كبيرة الحجم'
            ],
            'حالة الشحنة': [
                'في الطريق',
                'وصلت',
                'قيد التخليص',
                'مكتملة',
                'في الطريق',
                'وصلت'
            ],
            'حالة الإفراج': [
                'قيد المراجعة',
                'مفرج عنها',
                'تحتاج مستندات',
                'مكتملة',
                'قيد المراجعة',
                'مفرج عنها'
            ],
            'شركة الشحن': [
                'DHL Express',
                'FedEx International',
                'UPS Worldwide',
                'Aramex',
                'TNT Express',
                'Emirates SkyCargo'
            ],
            'رقم DHL': [
                'DHL123456789',
                'FDX987654321',
                'UPS456789123',
                'ARX789123456',
                'TNT321654987',
                'EK147258369'
            ],
            'ميناء الوصول': [
                'ميناء جبل علي - دبي',
                'ميناء الملك عبدالعزيز - الدمام',
                'ميناء جدة الإسلامي',
                'ميناء الشويخ - الكويت',
                'ميناء الدوحة',
                'ميناء صلالة - عمان'
            ],
            'تاريخ الوصول المتوقع': [
                '2024-02-01',
                '2024-02-05',
                '2024-02-10',
                '2024-02-15',
                '2024-02-20',
                '2024-02-25'
            ],
            'رقم الحاوية': [
                'CONT001, CONT002',
                'CONT003; CONT004; CONT005',
                'CONT006|CONT007',
                'CONT008/CONT009/CONT010',
                'CONT011-CONT012',
                'CONT013\nCONT014\nCONT015'
            ]
        }
        
        # إنشاء DataFrame
        df = pd.DataFrame(test_data)
        
        # حفظ الملف
        filename = 'test_shipments_complete.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف الاختبار: {filename}")
        print(f"📊 عدد الشحنات: {len(df)}")
        print(f"📋 عدد الأعمدة: {len(df.columns)}")
        
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الاختبار: {e}")
        return None

def test_container_parsing():
    """اختبار تحليل الحاويات المتعددة"""
    try:
        print("\n🔍 اختبار تحليل الحاويات المتعددة...")
        
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        # إنشاء نافذة للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        window = NewShipmentWindow()
        
        # اختبار حالات مختلفة
        test_cases = [
            ('CONT001, CONT002', ['CONT001', 'CONT002']),
            ('CONT003; CONT004; CONT005', ['CONT003', 'CONT004', 'CONT005']),
            ('CONT006|CONT007', ['CONT006', 'CONT007']),
            ('CONT008/CONT009/CONT010', ['CONT008', 'CONT009', 'CONT010']),
            ('CONT011-CONT012', ['CONT011', 'CONT012']),
            ('CONT013\nCONT014', ['CONT013', 'CONT014']),
            ('SINGLE_CONTAINER', ['SINGLE_CONTAINER'])
        ]
        
        all_passed = True
        for input_text, expected in test_cases:
            result = window.parse_multiple_containers(input_text)
            if result == expected:
                print(f"✅ '{input_text}' -> {result}")
            else:
                print(f"❌ '{input_text}' -> {result} (متوقع: {expected})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل الحاويات: {e}")
        return False

def show_feature_summary():
    """عرض ملخص الميزات"""
    print("\n📊 ملخص ميزة استيراد الإكسيل المتقدمة")
    print("=" * 60)
    
    features = [
        "🎯 اختيار نوع الاستيراد (فردي أو متعدد)",
        "📦 دعم الحاويات المتعددة بفواصل مختلفة",
        "🏗️ إنشاء شحنات متعددة في قاعدة البيانات",
        "🔄 توليد أرقام شحنات تلقائي",
        "🏢 إنشاء موردين جدد تلقائياً",
        "📅 معالجة تنسيقات التواريخ المختلفة",
        "⚡ معالجة أخطاء متقدمة",
        "📊 شريط تقدم للعمليات الطويلة",
        "💾 حفظ آمن مع إمكانية التراجع",
        "🔍 تقارير مفصلة عن النتائج"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n📋 الحقول المدعومة:")
    fields = [
        "التاريخ", "المورد", "بوليصة الشحن", "ملاحظات",
        "حالة الشحنة", "حالة الإفراج", "شركة الشحن", "رقم DHL",
        "ميناء الوصول", "تاريخ الوصول المتوقع", "رقم الحاوية"
    ]
    
    for field in fields:
        print(f"   • {field}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لميزة استيراد الإكسيل المتقدمة")
    print("=" * 60)
    
    # عرض ملخص الميزات
    show_feature_summary()
    
    # إنشاء ملف الاختبار
    print("\n📁 إنشاء ملف الاختبار...")
    test_file = create_test_excel_file()
    if not test_file:
        print("❌ فشل في إنشاء ملف الاختبار")
        return
    
    # اختبار تحليل الحاويات
    if not test_container_parsing():
        print("❌ فشل في اختبار تحليل الحاويات")
        return
    
    print("\n✅ جميع الاختبارات نجحت!")
    print("\n🎯 خطوات الاختبار اليدوي:")
    print("   1. شغل التطبيق الرئيسي")
    print("   2. افتح شاشة الشحنة الجديدة")
    print("   3. انقر على زر 'استيراد إكسيل'")
    print(f"   4. اختر الملف: {test_file}")
    print("   5. اختر نوع الاستيراد:")
    print("      • فردي: لاستيراد الصف الأول فقط")
    print("      • متعدد: لإنشاء 6 شحنات جديدة")
    print("   6. راقب النتائج والرسائل")
    
    print("\n⚠️ تحذيرات:")
    print("   • الاستيراد المتعدد ينشئ شحنات دائمة")
    print("   • تأكد من وجود نسخة احتياطية")
    print("   • يتم إنشاء موردين جدد تلقائياً")

if __name__ == "__main__":
    main()
