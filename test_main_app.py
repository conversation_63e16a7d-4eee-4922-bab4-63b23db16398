#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق الرئيسي مع الإصلاحات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.ui.main_window import MainWindow
from src.database.database_manager import DatabaseManager
from src.utils.arabic_support import setup_arabic_support

def main():
    """اختبار التطبيق الرئيسي مع الإصلاحات"""
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # إعداد دعم اللغة العربية
    setup_arabic_support(app)
    
    # إعداد قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    print("🚀 تم تشغيل التطبيق الرئيسي مع الإصلاحات")
    print("✅ الإصلاحات المطبقة:")
    print("   - إصلاح مشكلة الوصول لنظام الموردين بعد الإغلاق")
    print("   - إضافة معالجة أخطاء النوافذ المحذوفة")
    print("   - تحسين إدارة حالة النوافذ")
    print("   - إضافة أعمدة التواريخ في شاشة الشحنة الجديدة")
    print()
    print("🔧 اختبر الآن:")
    print("   1. افتح نظام الموردين")
    print("   2. أغلق نظام الموردين")
    print("   3. حاول فتح نظام الموردين مرة أخرى")
    print("   4. افتح شاشة شحنة جديدة وتحقق من أعمدة التواريخ")
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    main_window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
