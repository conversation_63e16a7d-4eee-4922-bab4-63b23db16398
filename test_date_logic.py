#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار منطق تحميل تاريخ الشحنة
"""

from datetime import datetime, date

def test_date_loading_logic():
    """اختبار منطق تحميل التاريخ"""
    
    print("🗓️ اختبار منطق تحميل تاريخ الشحنة")
    print("=" * 60)
    
    # محاكاة بيانات الشحنة
    class MockShipment:
        def __init__(self, shipment_date=None, created_at=None):
            self.shipment_date = shipment_date
            self.created_at = created_at
    
    # اختبار 1: شحنة لها تاريخ محفوظ
    print("\n📋 اختبار 1: شحنة لها تاريخ محفوظ")
    saved_date = date(2024, 1, 15)
    shipment1 = MockShipment(shipment_date=saved_date, created_at=datetime(2024, 1, 10))
    
    # تطبيق المنطق الجديد
    date_to_use = None
    if shipment1.shipment_date:
        date_to_use = shipment1.shipment_date
        print(f"✅ استخدام التاريخ المحفوظ: {date_to_use}")
    elif shipment1.created_at:
        date_to_use = shipment1.created_at
        print(f"⚠️ استخدام تاريخ الإنشاء: {date_to_use}")
    else:
        date_to_use = date.today()
        print(f"⚠️ استخدام التاريخ الحالي: {date_to_use}")
    
    assert date_to_use == saved_date, "يجب استخدام التاريخ المحفوظ"
    print("✅ النتيجة صحيحة")
    
    # اختبار 2: شحنة بدون تاريخ محفوظ لكن لها تاريخ إنشاء
    print("\n📋 اختبار 2: شحنة بدون تاريخ محفوظ")
    created_date = datetime(2024, 1, 20)
    shipment2 = MockShipment(shipment_date=None, created_at=created_date)
    
    date_to_use = None
    if shipment2.shipment_date:
        date_to_use = shipment2.shipment_date
        print(f"✅ استخدام التاريخ المحفوظ: {date_to_use}")
    elif shipment2.created_at:
        date_to_use = shipment2.created_at
        print(f"⚠️ استخدام تاريخ الإنشاء: {date_to_use}")
    else:
        date_to_use = date.today()
        print(f"⚠️ استخدام التاريخ الحالي: {date_to_use}")
    
    assert date_to_use == created_date, "يجب استخدام تاريخ الإنشاء"
    print("✅ النتيجة صحيحة")
    
    # اختبار 3: شحنة بدون أي تاريخ
    print("\n📋 اختبار 3: شحنة بدون أي تاريخ")
    shipment3 = MockShipment(shipment_date=None, created_at=None)
    
    date_to_use = None
    if shipment3.shipment_date:
        date_to_use = shipment3.shipment_date
        print(f"✅ استخدام التاريخ المحفوظ: {date_to_use}")
    elif shipment3.created_at:
        date_to_use = shipment3.created_at
        print(f"⚠️ استخدام تاريخ الإنشاء: {date_to_use}")
    else:
        date_to_use = date.today()
        print(f"⚠️ استخدام التاريخ الحالي: {date_to_use}")
    
    assert date_to_use == date.today(), "يجب استخدام التاريخ الحالي"
    print("✅ النتيجة صحيحة")
    
    print("\n" + "=" * 60)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ المنطق الجديد يحافظ على تاريخ الشحنة الأصلي")
    print("✅ التاريخ لن يتغير عند التعديل إذا كان محفوظاً في قاعدة البيانات")
    
    return True

if __name__ == "__main__":
    test_date_loading_logic()
