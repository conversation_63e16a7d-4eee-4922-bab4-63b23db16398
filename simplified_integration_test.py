#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التكامل المبسط للنظام
Simplified System Integration Test - Focused system workflow testing
"""

import sys
import os
import time
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Tuple

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import (
        Shipment, ShipmentItem, Container, ShipmentDocument,
        Supplier, Item, Base
    )
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد النماذج: {e}")

class SimplifiedIntegrationTester:
    """مختبر التكامل المبسط للنظام"""
    
    def __init__(self, db_manager=None):
        """تهيئة مختبر التكامل المبسط"""
        self.db_manager = db_manager or DatabaseManager()
        self.test_log = []
        self.passed_tests = 0
        self.failed_tests = 0
        
    def log_test(self, test_name: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الاختبار"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {test_name}: {message}"
        self.test_log.append(log_entry)
        print(log_entry)
        
        if status == "PASS":
            self.passed_tests += 1
        elif status == "FAIL":
            self.failed_tests += 1
    
    def test_database_connectivity(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        self.log_test("اتصال قاعدة البيانات", "بدء اختبار الاتصال بقاعدة البيانات")
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار استعلام بسيط
            suppliers_count = session.query(Supplier).count()
            items_count = session.query(Item).count()
            shipments_count = session.query(Shipment).count()
            
            session.close()
            
            self.log_test("اتصال قاعدة البيانات", 
                         f"✅ الاتصال ناجح - موردين: {suppliers_count}, أصناف: {items_count}, شحنات: {shipments_count}", 
                         "PASS")
            return True
            
        except Exception as e:
            self.log_test("اتصال قاعدة البيانات", f"❌ فشل الاتصال: {str(e)}", "FAIL")
            return False
    
    def test_data_models_integrity(self) -> bool:
        """اختبار سلامة نماذج البيانات"""
        self.log_test("سلامة النماذج", "بدء اختبار سلامة نماذج البيانات")
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار العلاقات بين الجداول
            shipments_with_suppliers = session.query(Shipment).join(Supplier).count()
            shipments_with_items = session.query(Shipment).join(ShipmentItem).count()
            shipments_with_containers = session.query(Shipment).join(Container).count()
            
            session.close()
            
            self.log_test("سلامة النماذج", 
                         f"✅ العلاقات سليمة - شحنات مع موردين: {shipments_with_suppliers}, مع أصناف: {shipments_with_items}, مع حاويات: {shipments_with_containers}", 
                         "PASS")
            return True
            
        except Exception as e:
            self.log_test("سلامة النماذج", f"❌ مشكلة في العلاقات: {str(e)}", "FAIL")
            return False
    
    def test_crud_operations(self) -> bool:
        """اختبار عمليات CRUD الأساسية"""
        self.log_test("عمليات CRUD", "بدء اختبار عمليات CRUD الأساسية")
        
        try:
            session = self.db_manager.get_session()
            
            # البحث عن مورد موجود أو إنشاء واحد جديد
            supplier = session.query(Supplier).first()
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار مبسط",
                    contact_person="شخص الاتصال",
                    phone="123456789"
                )
                session.add(supplier)
                session.commit()
                created_supplier = True
            else:
                created_supplier = False
            
            # البحث عن صنف موجود أو إنشاء واحد جديد
            item = session.query(Item).first()
            if not item:
                item = Item(
                    code="SIMPLE-TEST-001",
                    name="صنف اختبار مبسط",
                    unit="قطعة"
                )
                session.add(item)
                session.commit()
                created_item = True
            else:
                created_item = False
            
            # إنشاء شحنة تجريبية (CREATE)
            test_shipment = Shipment(
                shipment_number="SIMPLE-TEST-SHIP-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                notes="شحنة اختبار مبسط"
            )
            session.add(test_shipment)
            session.commit()
            
            # قراءة الشحنة (READ)
            read_shipment = session.query(Shipment).filter(
                Shipment.shipment_number == "SIMPLE-TEST-SHIP-001"
            ).first()
            
            if not read_shipment:
                raise Exception("فشل في قراءة الشحنة المُنشأة")
            
            # تحديث الشحنة (UPDATE)
            setattr(read_shipment, 'notes', 'تم تحديث الشحنة في الاختبار المبسط')
            session.commit()
            
            # التحقق من التحديث
            updated_shipment = session.query(Shipment).filter(
                Shipment.id == read_shipment.id
            ).first()
            
            if getattr(updated_shipment, 'notes', '') != 'تم تحديث الشحنة في الاختبار المبسط':
                raise Exception("فشل في تحديث الشحنة")
            
            # حذف الشحنة (DELETE)
            session.delete(updated_shipment)
            session.commit()
            
            # التحقق من الحذف
            deleted_shipment = session.query(Shipment).filter(
                Shipment.shipment_number == "SIMPLE-TEST-SHIP-001"
            ).first()
            
            if deleted_shipment:
                raise Exception("فشل في حذف الشحنة")
            
            # تنظيف البيانات التجريبية
            if created_item:
                session.delete(item)
            if created_supplier:
                session.delete(supplier)
            session.commit()
            session.close()
            
            self.log_test("عمليات CRUD", "✅ جميع عمليات CRUD تعمل بشكل صحيح", "PASS")
            return True
            
        except Exception as e:
            self.log_test("عمليات CRUD", f"❌ فشل في عمليات CRUD: {str(e)}", "FAIL")
            return False
    
    def test_data_validation(self) -> bool:
        """اختبار التحقق من صحة البيانات"""
        self.log_test("التحقق من البيانات", "بدء اختبار التحقق من صحة البيانات")
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار منع الأرقام المكررة
            try:
                # إنشاء شحنة بنفس الرقم مرتين
                supplier = session.query(Supplier).first()
                if not supplier:
                    supplier = Supplier(name="مورد مؤقت", contact_person="test", phone="123")
                    session.add(supplier)
                    session.commit()
                
                shipment1 = Shipment(
                    shipment_number="DUPLICATE-TEST-001",
                    shipment_date=date.today(),
                    supplier_id=supplier.id,
                    shipment_status="تحت الطلب"
                )
                session.add(shipment1)
                session.commit()
                
                # محاولة إنشاء شحنة بنفس الرقم
                shipment2 = Shipment(
                    shipment_number="DUPLICATE-TEST-001",
                    shipment_date=date.today(),
                    supplier_id=supplier.id,
                    shipment_status="تحت الطلب"
                )
                session.add(shipment2)
                session.commit()
                
                # إذا وصلنا هنا، فالتحقق من التفرد لا يعمل
                self.log_test("التحقق من البيانات", "❌ فشل في منع الأرقام المكررة", "FAIL")
                
                # تنظيف
                session.delete(shipment1)
                session.delete(shipment2)
                session.commit()
                return False
                
            except Exception:
                # هذا متوقع - يجب أن يفشل الإدراج
                session.rollback()
                
                # تنظيف الشحنة الأولى
                shipment1 = session.query(Shipment).filter(
                    Shipment.shipment_number == "DUPLICATE-TEST-001"
                ).first()
                if shipment1:
                    session.delete(shipment1)
                    session.commit()
                
                self.log_test("التحقق من البيانات", "✅ منع الأرقام المكررة يعمل بشكل صحيح", "PASS")
            
            session.close()
            return True
            
        except Exception as e:
            self.log_test("التحقق من البيانات", f"❌ خطأ في اختبار التحقق: {str(e)}", "FAIL")
            return False
    
    def test_performance(self) -> bool:
        """اختبار الأداء الأساسي"""
        self.log_test("الأداء", "بدء اختبار الأداء الأساسي")
        
        try:
            session = self.db_manager.get_session()
            
            # اختبار سرعة الاستعلامات
            start_time = time.time()
            
            # استعلامات متعددة
            suppliers = session.query(Supplier).all()
            items = session.query(Item).all()
            shipments = session.query(Shipment).limit(10).all()
            
            query_time = time.time() - start_time
            
            session.close()
            
            if query_time < 2.0:
                self.log_test("الأداء", f"✅ الأداء ممتاز: {query_time:.3f} ثانية", "PASS")
            elif query_time < 5.0:
                self.log_test("الأداء", f"⚠️ الأداء مقبول: {query_time:.3f} ثانية", "PASS")
            else:
                self.log_test("الأداء", f"❌ الأداء بطيء: {query_time:.3f} ثانية", "FAIL")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("الأداء", f"❌ خطأ في اختبار الأداء: {str(e)}", "FAIL")
            return False
    
    def test_file_system_access(self) -> bool:
        """اختبار الوصول لنظام الملفات"""
        self.log_test("نظام الملفات", "بدء اختبار الوصول لنظام الملفات")
        
        try:
            # فحص الملفات الأساسية للنظام
            essential_files = [
                "src/database/database_manager.py",
                "src/database/models.py",
                "src/ui/shipments/shipments_window.py",
                "src/ui/shipments/new_shipment_window.py"
            ]
            
            missing_files = []
            for file_path in essential_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
            
            if missing_files:
                self.log_test("نظام الملفات", f"❌ ملفات مفقودة: {', '.join(missing_files)}", "FAIL")
                return False
            else:
                self.log_test("نظام الملفات", "✅ جميع الملفات الأساسية موجودة", "PASS")
                return True
            
        except Exception as e:
            self.log_test("نظام الملفات", f"❌ خطأ في فحص الملفات: {str(e)}", "FAIL")
            return False
    
    def run_simplified_integration_test(self) -> Dict:
        """تشغيل اختبار التكامل المبسط"""
        self.log_test("اختبار التكامل المبسط", "بدء اختبار التكامل المبسط للنظام")
        
        # تشغيل جميع الاختبارات
        tests = [
            ("اتصال قاعدة البيانات", self.test_database_connectivity),
            ("سلامة النماذج", self.test_data_models_integrity),
            ("عمليات CRUD", self.test_crud_operations),
            ("التحقق من البيانات", self.test_data_validation),
            ("الأداء", self.test_performance),
            ("نظام الملفات", self.test_file_system_access)
        ]
        
        for test_name, test_function in tests:
            try:
                success = test_function()
                if not success:
                    self.log_test("اختبار التكامل المبسط", f"فشل في اختبار: {test_name}", "FAIL")
            except Exception as e:
                self.log_test("اختبار التكامل المبسط", f"خطأ في اختبار {test_name}: {str(e)}", "FAIL")
        
        # حساب النتائج
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # تحديد التقييم
        if success_rate >= 95:
            grade = "ممتاز"
        elif success_rate >= 85:
            grade = "جيد جداً"
        elif success_rate >= 75:
            grade = "جيد"
        elif success_rate >= 60:
            grade = "مقبول"
        else:
            grade = "يحتاج تحسين"
        
        self.log_test("اختبار التكامل المبسط", f"النتيجة النهائية: {self.passed_tests}/{total_tests} ({success_rate:.1f}%) - {grade}")
        
        return {
            'total_tests': total_tests,
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'success_rate': success_rate,
            'grade': grade,
            'test_log': self.test_log
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("🔧 اختبار التكامل المبسط للنظام")
        print("=" * 50)
        
        # إنشاء مختبر التكامل المبسط
        tester = SimplifiedIntegrationTester()
        
        # تشغيل الاختبار المبسط
        results = tester.run_simplified_integration_test()
        
        print("\n" + "=" * 50)
        print("📊 تقرير اختبار التكامل المبسط:")
        print(f"• إجمالي الاختبارات: {results['total_tests']}")
        print(f"• الاختبارات الناجحة: {results['passed_tests']}")
        print(f"• الاختبارات الفاشلة: {results['failed_tests']}")
        print(f"• معدل النجاح: {results['success_rate']:.1f}%")
        print(f"• التقييم: {results['grade']}")
        
        print("\n" + "=" * 50)
        if results['success_rate'] >= 85:
            print("✅ النظام يعمل بتكامل ممتاز!")
        elif results['success_rate'] >= 75:
            print("⚠️ النظام يعمل بتكامل جيد مع بعض التحسينات المطلوبة")
        else:
            print("❌ النظام يحتاج إلى إصلاحات مهمة")
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل المبسط: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
