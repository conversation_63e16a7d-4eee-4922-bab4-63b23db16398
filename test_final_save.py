#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لوظيفة الحفظ
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.ui.shipments.shipments_window import ShipmentsWindow
from src.ui.shipments.shipment_tracking_window import ShipmentTrackingWindow
from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier

def test_final_save():
    """اختبار نهائي لوظيفة الحفظ"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار نهائي لوظيفة الحفظ ===")
        
        # إنشاء نافذة الشحنة الجديدة
        window = NewShipmentWindow()
        
        # الحصول على مورد
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            supplier = session.query(Supplier).filter(Supplier.is_active == True).first()
            if not supplier:
                print("❌ لا توجد موردين")
                return False
                
            print(f"✅ المورد: {supplier.name}")
            
            # عد الشحنات قبل الحفظ
            initial_count = session.query(Shipment).count()
            print(f"✅ عدد الشحنات قبل الحفظ: {initial_count}")
            
        finally:
            session.close()
        
        # ملء البيانات الأساسية فقط
        print("\n🔄 ملء البيانات الأساسية...")
        window.shipment_number_edit.setText("FINAL-TEST-001")
        window.supplier_edit.setText(supplier.name)
        window.supplier_edit.setProperty("supplier_id", supplier.id)
        window.supplier_invoice_edit.setText("FINAL-INV-001")
        window.shipment_status_combo.setCurrentText("جديدة")
        window.clearance_status_combo.setCurrentText("لم يتم الإفراج")
        window.tracking_number_edit.setText("FINAL-TRK-001")
        window.bill_of_lading_edit.setText("FINAL-BL-001")
        window.notes_edit.setPlainText("اختبار نهائي لوظيفة الحفظ")
        
        # لا نضيف أصناف لتجنب مشاكل الجدول
        print("✅ تم ملء البيانات الأساسية")
        
        # محاولة الحفظ
        print("\n💾 محاولة الحفظ...")
        
        # التحقق من صحة البيانات أولاً
        if not window.validate_data():
            print("❌ فشل في التحقق من صحة البيانات")
            return False
        
        print("✅ تم التحقق من صحة البيانات")
        
        # محاولة الحفظ
        success = window.save_shipment()
        
        if success:
            print("✅ تم حفظ الشحنة بنجاح!")
            shipment_id = window.current_shipment_id
            print(f"📋 معرف الشحنة: {shipment_id}")
            
            # التحقق من قاعدة البيانات
            session = db_manager.get_session()
            try:
                final_count = session.query(Shipment).count()
                print(f"✅ عدد الشحنات بعد الحفظ: {final_count}")
                
                if final_count > initial_count:
                    print("✅ تم إضافة الشحنة إلى قاعدة البيانات")
                    
                    # البحث عن الشحنة المحفوظة
                    saved_shipment = session.query(Shipment).filter(
                        Shipment.shipment_number == "FINAL-TEST-001"
                    ).first()
                    
                    if saved_shipment:
                        print(f"✅ الشحنة موجودة: {saved_shipment.shipment_number}")
                        print(f"   📋 المورد: {saved_shipment.supplier.name}")
                        print(f"   📋 الحالة: {saved_shipment.shipment_status}")
                        
                        # اختبار الشاشات الأخرى
                        print("\n🔍 اختبار ظهور الشحنة في الشاشات الأخرى...")
                        
                        # شاشة إدارة الشحنات
                        shipments_window = ShipmentsWindow()
                        shipments_window.load_shipments()
                        
                        found_in_management = False
                        for row in range(shipments_window.shipments_table.rowCount()):
                            if shipments_window.shipments_table.item(row, 0).text() == "FINAL-TEST-001":
                                found_in_management = True
                                print("✅ الشحنة ظاهرة في شاشة إدارة الشحنات")
                                break
                        
                        if not found_in_management:
                            print("❌ الشحنة غير ظاهرة في شاشة إدارة الشحنات")
                            # طباعة جميع الشحنات للتشخيص
                            print("   📋 الشحنات الموجودة في الجدول:")
                            for row in range(shipments_window.shipments_table.rowCount()):
                                shipment_num = shipments_window.shipments_table.item(row, 0).text()
                                print(f"      - {shipment_num}")
                        
                        # شاشة تتبع الشحنات
                        tracking_window = ShipmentTrackingWindow()
                        tracking_window.load_tracking_data()
                        
                        found_in_tracking = False
                        for row in range(tracking_window.tracking_table.rowCount()):
                            if tracking_window.tracking_table.item(row, 0).text() == "FINAL-TEST-001":
                                found_in_tracking = True
                                print("✅ الشحنة ظاهرة في شاشة تتبع الشحنات")
                                break
                        
                        if not found_in_tracking:
                            print("❌ الشحنة غير ظاهرة في شاشة تتبع الشحنات")
                            # طباعة جميع الشحنات للتشخيص
                            print("   📋 الشحنات الموجودة في جدول التتبع:")
                            for row in range(tracking_window.tracking_table.rowCount()):
                                shipment_num = tracking_window.tracking_table.item(row, 0).text()
                                print(f"      - {shipment_num}")
                        
                        # النتائج النهائية
                        print("\n🎯 النتائج النهائية:")
                        print(f"   ✅ حفظ الشحنة: نجح")
                        print(f"   {'✅' if found_in_management else '❌'} ظهور في إدارة الشحنات: {'نجح' if found_in_management else 'فشل'}")
                        print(f"   {'✅' if found_in_tracking else '❌'} ظهور في تتبع الشحنات: {'نجح' if found_in_tracking else 'فشل'}")
                        
                        if found_in_management and found_in_tracking:
                            print("\n🎉 تم إصلاح المشكلة بالكامل!")
                            return True
                        else:
                            print("\n⚠️ المشكلة لم تحل بالكامل - الشحنة محفوظة لكن لا تظهر في الشاشات")
                            return False
                    else:
                        print("❌ لم يتم العثور على الشحنة المحفوظة")
                        return False
                else:
                    print("❌ لم تتم إضافة الشحنة إلى قاعدة البيانات")
                    return False
                    
            finally:
                session.close()
        else:
            print("❌ فشل في حفظ الشحنة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_final_save()
