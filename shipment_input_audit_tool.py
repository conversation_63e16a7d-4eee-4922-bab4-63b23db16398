#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص ومراجعة إجراءات إدخال الشحنات
Shipment Input Audit Tool - Comprehensive review of shipment input procedures
"""

import sys
import os
from datetime import datetime
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, ShipmentItem, Supplier, Item

class ShipmentInputAuditTool:
    """أداة فحص إجراءات إدخال الشحنات"""
    
    def __init__(self):
        """تهيئة الأداة"""
        self.db_manager = DatabaseManager()
        self.audit_log = []
        self.issues_found = []
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الفحص"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status in ["ERROR", "WARNING"]:
            self.issues_found.append({
                'category': category,
                'message': message,
                'status': status,
                'timestamp': timestamp
            })
    
    def check_database_structure(self):
        """فحص بنية قاعدة البيانات للشحنات"""
        self.log_audit("بنية قاعدة البيانات", "بدء فحص بنية الجداول")
        
        session = self.db_manager.get_session()
        try:
            # فحص جدول الشحنات
            from sqlalchemy import text
            
            # الحصول على بنية جدول الشحنات
            result = session.execute(text("PRAGMA table_info(shipments)"))
            shipment_columns = [row[1] for row in result.fetchall()]
            
            required_columns = [
                'id', 'shipment_number', 'shipment_date', 'supplier_id',
                'shipment_status', 'shipping_company', 'container_number',
                'bill_of_lading', 'tracking_number', 'vessel_name',
                'port_of_loading', 'port_of_discharge', 'port_of_arrival'
            ]
            
            missing_columns = [col for col in required_columns if col not in shipment_columns]
            
            if missing_columns:
                self.log_audit("بنية قاعدة البيانات", f"أعمدة مفقودة في جدول الشحنات: {missing_columns}", "ERROR")
            else:
                self.log_audit("بنية قاعدة البيانات", "جدول الشحنات يحتوي على جميع الأعمدة المطلوبة", "SUCCESS")
            
            # فحص جدول أصناف الشحنات
            result = session.execute(text("PRAGMA table_info(shipment_items)"))
            item_columns = [row[1] for row in result.fetchall()]
            
            required_item_columns = ['id', 'shipment_id', 'item_id', 'quantity', 'unit_price']
            missing_item_columns = [col for col in required_item_columns if col not in item_columns]
            
            if missing_item_columns:
                self.log_audit("بنية قاعدة البيانات", f"أعمدة مفقودة في جدول أصناف الشحنات: {missing_item_columns}", "ERROR")
            else:
                self.log_audit("بنية قاعدة البيانات", "جدول أصناف الشحنات يحتوي على جميع الأعمدة المطلوبة", "SUCCESS")
            
        except Exception as e:
            self.log_audit("بنية قاعدة البيانات", f"خطأ في فحص البنية: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def check_foreign_key_constraints(self):
        """فحص قيود المفاتيح الخارجية"""
        self.log_audit("قيود المفاتيح الخارجية", "بدء فحص القيود")
        
        session = self.db_manager.get_session()
        try:
            from sqlalchemy import text
            
            # فحص قيود جدول الشحنات
            result = session.execute(text("PRAGMA foreign_key_list(shipments)"))
            fk_constraints = result.fetchall()
            
            expected_fks = ['suppliers']  # الجداول المرتبطة
            found_fks = [row[2] for row in fk_constraints]  # الجدول المرجعي
            
            for expected_fk in expected_fks:
                if expected_fk in found_fks:
                    self.log_audit("قيود المفاتيح الخارجية", f"قيد {expected_fk} موجود", "SUCCESS")
                else:
                    self.log_audit("قيود المفاتيح الخارجية", f"قيد {expected_fk} مفقود", "WARNING")
            
            # فحص قيود جدول أصناف الشحنات
            result = session.execute(text("PRAGMA foreign_key_list(shipment_items)"))
            item_fk_constraints = result.fetchall()
            
            expected_item_fks = ['shipments', 'items']
            found_item_fks = [row[2] for row in item_fk_constraints]
            
            for expected_fk in expected_item_fks:
                if expected_fk in found_item_fks:
                    self.log_audit("قيود المفاتيح الخارجية", f"قيد أصناف الشحنات {expected_fk} موجود", "SUCCESS")
                else:
                    self.log_audit("قيود المفاتيح الخارجية", f"قيد أصناف الشحنات {expected_fk} مفقود", "WARNING")
            
        except Exception as e:
            self.log_audit("قيود المفاتيح الخارجية", f"خطأ في فحص القيود: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def test_basic_shipment_creation(self):
        """اختبار إنشاء شحنة أساسية"""
        self.log_audit("إنشاء الشحنات", "بدء اختبار إنشاء شحنة أساسية")
        
        session = self.db_manager.get_session()
        try:
            # التحقق من وجود مورد للاختبار
            supplier = session.query(Supplier).first()
            if not supplier:
                self.log_audit("إنشاء الشحنات", "لا يوجد موردين في قاعدة البيانات للاختبار", "WARNING")
                return
            
            # إنشاء شحنة تجريبية
            test_shipment = Shipment(
                shipment_number="TEST-001",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة اختبار",
                container_number="TEST123456",
                bill_of_lading="BL-TEST-001"
            )
            
            session.add(test_shipment)
            session.commit()
            
            # التحقق من الحفظ
            saved_shipment = session.query(Shipment).filter_by(shipment_number="TEST-001").first()
            if saved_shipment:
                self.log_audit("إنشاء الشحنات", "تم إنشاء شحنة تجريبية بنجاح", "SUCCESS")
                
                # حذف الشحنة التجريبية
                session.delete(saved_shipment)
                session.commit()
                self.log_audit("إنشاء الشحنات", "تم حذف الشحنة التجريبية", "INFO")
            else:
                self.log_audit("إنشاء الشحنات", "فشل في إنشاء الشحنة التجريبية", "ERROR")
            
        except Exception as e:
            session.rollback()
            self.log_audit("إنشاء الشحنات", f"خطأ في اختبار إنشاء الشحنة: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def test_shipment_items_creation(self):
        """اختبار إنشاء أصناف الشحنات"""
        self.log_audit("أصناف الشحنات", "بدء اختبار إنشاء أصناف الشحنات")
        
        session = self.db_manager.get_session()
        try:
            # التحقق من وجود مورد وصنف للاختبار
            supplier = session.query(Supplier).first()
            item = session.query(Item).first()
            
            if not supplier:
                self.log_audit("أصناف الشحنات", "لا يوجد موردين للاختبار", "WARNING")
                return
            
            if not item:
                self.log_audit("أصناف الشحنات", "لا يوجد أصناف للاختبار", "WARNING")
                return
            
            # إنشاء شحنة تجريبية
            test_shipment = Shipment(
                shipment_number="TEST-ITEMS-001",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب"
            )
            session.add(test_shipment)
            session.flush()  # للحصول على ID
            
            # إنشاء صنف تجريبي
            test_item = ShipmentItem(
                shipment_id=test_shipment.id,
                item_id=item.id,
                quantity=10.0,
                unit_price=100.0
            )
            session.add(test_item)
            session.commit()
            
            # التحقق من الحفظ
            saved_item = session.query(ShipmentItem).filter_by(shipment_id=test_shipment.id).first()
            if saved_item:
                self.log_audit("أصناف الشحنات", "تم إنشاء صنف شحنة تجريبي بنجاح", "SUCCESS")
                
                # حذف البيانات التجريبية
                session.delete(saved_item)
                session.delete(test_shipment)
                session.commit()
                self.log_audit("أصناف الشحنات", "تم حذف البيانات التجريبية", "INFO")
            else:
                self.log_audit("أصناف الشحنات", "فشل في إنشاء صنف الشحنة التجريبي", "ERROR")
            
        except Exception as e:
            session.rollback()
            self.log_audit("أصناف الشحنات", f"خطأ في اختبار أصناف الشحنات: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def check_data_validation(self):
        """فحص آليات التحقق من صحة البيانات"""
        self.log_audit("التحقق من البيانات", "بدء فحص آليات التحقق")
        
        session = self.db_manager.get_session()
        try:
            # اختبار القيود الفريدة
            supplier = session.query(Supplier).first()
            if supplier:
                # محاولة إنشاء شحنتين بنفس الرقم
                shipment1 = Shipment(
                    shipment_number="DUPLICATE-TEST",
                    supplier_id=supplier.id,
                    shipment_status="تحت الطلب"
                )
                session.add(shipment1)
                session.commit()
                
                try:
                    shipment2 = Shipment(
                        shipment_number="DUPLICATE-TEST",
                        supplier_id=supplier.id,
                        shipment_status="تحت الطلب"
                    )
                    session.add(shipment2)
                    session.commit()
                    
                    # إذا وصلنا هنا، فالقيد الفريد لا يعمل
                    self.log_audit("التحقق من البيانات", "القيد الفريد لرقم الشحنة لا يعمل", "ERROR")
                    
                    # حذف الشحنات المكررة
                    session.delete(shipment1)
                    session.delete(shipment2)
                    session.commit()
                    
                except Exception:
                    # هذا متوقع - القيد الفريد يعمل
                    session.rollback()
                    self.log_audit("التحقق من البيانات", "القيد الفريد لرقم الشحنة يعمل بشكل صحيح", "SUCCESS")
                    
                    # حذف الشحنة الأولى
                    session.delete(shipment1)
                    session.commit()
            
        except Exception as e:
            session.rollback()
            self.log_audit("التحقق من البيانات", f"خطأ في فحص التحقق من البيانات: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def run_complete_audit(self):
        """تشغيل الفحص الشامل"""
        self.log_audit("فحص شامل", "بدء الفحص الشامل لإجراءات إدخال الشحنات")
        
        # 1. فحص بنية قاعدة البيانات
        self.check_database_structure()
        
        # 2. فحص قيود المفاتيح الخارجية
        self.check_foreign_key_constraints()
        
        # 3. اختبار إنشاء الشحنات
        self.test_basic_shipment_creation()
        
        # 4. اختبار أصناف الشحنات
        self.test_shipment_items_creation()
        
        # 5. فحص التحقق من البيانات
        self.check_data_validation()
        
        # تقرير نهائي
        self.log_audit("تقرير نهائي", "=" * 50)
        
        if not self.issues_found:
            self.log_audit("تقرير نهائي", "✅ لم يتم العثور على مشاكل في إجراءات إدخال الشحنات", "SUCCESS")
        else:
            self.log_audit("تقرير نهائي", f"⚠️ تم العثور على {len(self.issues_found)} مشكلة", "WARNING")
            for issue in self.issues_found:
                self.log_audit("مشكلة مكتشفة", f"{issue['category']}: {issue['message']}", issue['status'])
        
        return {
            'success': len(self.issues_found) == 0,
            'issues_count': len(self.issues_found),
            'issues': self.issues_found,
            'audit_log': self.audit_log
        }

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة فحص إجراءات إدخال الشحنات")
    print("=" * 50)
    
    audit_tool = ShipmentInputAuditTool()
    result = audit_tool.run_complete_audit()
    
    print("\n" + "=" * 50)
    if result['success']:
        print("✅ تم اجتياز جميع فحوصات إدخال الشحنات!")
    else:
        print(f"⚠️ تم العثور على {result['issues_count']} مشكلة في إجراءات إدخال الشحنات")
    
    return result

if __name__ == "__main__":
    main()
