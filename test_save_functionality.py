#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظيفة الحفظ الجديدة
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.database.database_manager import DatabaseManager
from src.database.models import Shipment

def test_save_functionality():
    """اختبار وظيفة الحفظ"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار وظيفة الحفظ الجديدة ===")
        
        # إنشاء النافذة
        window = NewShipmentWindow()
        
        # التحقق من وجود DatabaseManager
        if hasattr(window, 'db_manager'):
            print("✅ DatabaseManager متوفر")
        else:
            print("❌ DatabaseManager غير متوفر")
            return
            
        # التحقق من وجود وظيفة الحفظ الجديدة
        if hasattr(window, 'save_shipment'):
            print("✅ وظيفة save_shipment موجودة")
        else:
            print("❌ وظيفة save_shipment غير موجودة")
            return
            
        # التحقق من وجود وظائف التحقق
        if hasattr(window, 'validate_data'):
            print("✅ وظيفة validate_data موجودة")
        else:
            print("❌ وظيفة validate_data غير موجودة")
            
        if hasattr(window, 'get_shipment_items'):
            print("✅ وظيفة get_shipment_items موجودة")
        else:
            print("❌ وظيفة get_shipment_items غير موجودة")
            
        # التحقق من قاعدة البيانات
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        try:
            # عد الشحنات الحالية
            current_count = session.query(Shipment).count()
            print(f"✅ عدد الشحنات الحالية في قاعدة البيانات: {current_count}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        finally:
            session.close()
            
        print("\n🎯 وظيفة الحفظ جاهزة!")
        print("📋 لاختبار الحفظ:")
        print("   1. افتح نافذة شحنة جديدة")
        print("   2. اضغط على 'بيانات تجريبية' لملء النموذج")
        print("   3. اضغط على 'حفظ'")
        print("   4. تحقق من ظهور الشحنة في:")
        print("      • شاشة إدارة الشحنات")
        print("      • شاشة تتبع الشحنات")
        print("      • النافذة الرئيسية")
        
        # عرض النافذة للاختبار
        window.show()
        print("\n🔍 النافذة مفتوحة - اختبر الحفظ يدوياً")
        
        app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_save_functionality()
