#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لوظيفة الحفظ
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier, Item

def test_simple_save():
    """اختبار حفظ شحنة بسيط"""
    try:
        print("=== اختبار حفظ شحنة بسيط ===")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            # الحصول على مورد
            supplier = session.query(Supplier).filter(Supplier.is_active == True).first()
            if not supplier:
                print("❌ لا توجد موردين")
                return False
                
            print(f"✅ المورد: {supplier.name}")
            
            # إنشاء شحنة بسيطة
            new_shipment = Shipment(
                shipment_number="TEST-SIMPLE-001",
                supplier_id=supplier.id,
                supplier_invoice_number="INV-SIMPLE-001",
                shipment_status="جديدة",
                clearance_status="لم يتم الإفراج",
                tracking_number="TRK-SIMPLE-001",
                bill_of_lading="BL-SIMPLE-001",
                notes="شحنة تجريبية بسيطة",
                total_amount=1000.0
            )
            
            session.add(new_shipment)
            session.commit()
            
            print(f"✅ تم حفظ الشحنة بنجاح - ID: {new_shipment.id}")
            
            # التحقق من الحفظ
            saved_shipment = session.query(Shipment).filter(Shipment.id == new_shipment.id).first()
            if saved_shipment:
                print(f"✅ الشحنة محفوظة: {saved_shipment.shipment_number}")
                
                # عد جميع الشحنات
                total_shipments = session.query(Shipment).count()
                print(f"✅ إجمالي الشحنات في قاعدة البيانات: {total_shipments}")
                
                return True
            else:
                print("❌ فشل في العثور على الشحنة المحفوظة")
                return False
                
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في الحفظ: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    test_simple_save()
