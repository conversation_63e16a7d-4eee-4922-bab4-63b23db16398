#!/usr/bin/env python3
"""
اختبار شريط الأدوات في وضع الإدخال
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🔍 اختبار شريط الأدوات في وضع الإدخال...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # 1. اختبار وضع القائمة
        print("\n📋 اختبار وضع القائمة...")
        list_window = PurchaseOrdersWindow(mode="list")
        
        # فحص شريط الأدوات
        from PySide6.QtWidgets import QToolBar
        toolbars = list_window.findChildren(QToolBar)
        if toolbars:
            toolbar = toolbars[0]
            actions = toolbar.actions()
            print(f"✅ شريط أدوات وضع القائمة: {len(actions)} أزرار")
            for action in actions:
                if action.text():  # تجاهل الفواصل
                    print(f"   - {action.text()}")
        else:
            print("❌ لا يوجد شريط أدوات في وضع القائمة")
        
        # 2. اختبار وضع الإدخال
        print("\n📝 اختبار وضع الإدخال...")
        entry_window = PurchaseOrdersWindow(mode="entry")
        
        # فحص شريط الأدوات
        from PySide6.QtWidgets import QToolBar
        toolbars = entry_window.findChildren(QToolBar)
        if toolbars:
            toolbar = toolbars[0]
            actions = toolbar.actions()
            print(f"✅ شريط أدوات وضع الإدخال: {len(actions)} أزرار")
            
            expected_buttons = ["حفظ", "إضافة صنف", "تعديل صنف", "حذف صنف", "خروج"]
            found_buttons = []
            
            for action in actions:
                if action.text():  # تجاهل الفواصل
                    found_buttons.append(action.text())
                    print(f"   - {action.text()}")
            
            # التحقق من الأزرار المطلوبة
            print("\n🔍 فحص الأزرار المطلوبة:")
            for button in expected_buttons:
                if button in found_buttons:
                    print(f"✅ {button}")
                else:
                    print(f"❌ {button} - غير موجود")
            
            # اختبار الدوال المرتبطة
            print("\n🔧 فحص الدوال المرتبطة:")
            required_methods = ['save_order', 'add_item', 'edit_item', 'delete_item']
            for method in required_methods:
                if hasattr(entry_window, method):
                    print(f"✅ {method}")
                else:
                    print(f"❌ {method} - غير موجود")
                    
        else:
            print("❌ لا يوجد شريط أدوات في وضع الإدخال")
            return False
        
        # 3. اختبار فتح نافذة جديدة من القائمة
        print("\n🆕 اختبار فتح نافذة جديدة...")
        try:
            list_window.new_order()
            print("✅ تم فتح نافذة طلب جديد بنجاح")
            print("✅ يجب أن تحتوي على شريط الأدوات الكامل")
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة جديدة: {e}")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ شريط الأدوات يعمل في كلا الوضعين")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار العام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 شريط الأدوات جاهز للاستخدام!")
    else:
        print("\n❌ شريط الأدوات يحتاج مراجعة إضافية")
