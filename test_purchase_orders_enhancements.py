#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات طلبات الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QDate
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
from src.database.database_manager import DatabaseManager

def test_purchase_orders_enhancements():
    """اختبار تحسينات طلبات الشراء"""
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    print("🧪 اختبار تحسينات طلبات الشراء")
    print("=" * 50)
    
    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # إنشاء نافذة طلبات الشراء
        window = PurchaseOrdersWindow(db_manager)
        
        print("✅ تم إنشاء نافذة طلبات الشراء بنجاح")
        
        # اختبار حقل التاريخ المرن
        print("\n📅 اختبار حقل التاريخ المرن:")
        
        # التحقق من نوع حقل التاريخ
        date_field = window.order_date_edit
        print(f"   نوع حقل التاريخ: {type(date_field).__name__}")
        
        # اختبار تعيين تاريخ
        test_date = QDate(2024, 3, 15)
        date_field.setDate(test_date)
        retrieved_date = date_field.date()
        
        if retrieved_date == test_date:
            print("   ✅ تعيين واسترجاع التاريخ يعمل بشكل صحيح")
        else:
            print("   ❌ مشكلة في تعيين أو استرجاع التاريخ")
        
        # اختبار نظام الألوان
        print("\n🎨 اختبار نظام الألوان:")
        
        # التحقق من وجود دالة حالة الاستخدام
        if hasattr(window, 'get_purchase_order_usage_status'):
            print("   ✅ دالة حالة الاستخدام موجودة")
            
            # اختبار الحالات المختلفة
            test_statuses = ["unused", "partially_used", "fully_used", "empty", "unknown"]
            for status in test_statuses:
                color = window.get_status_color(status)
                print(f"   📌 حالة '{status}': لون {color}")
        else:
            print("   ❌ دالة حالة الاستخدام غير موجودة")
        
        # التحقق من وجود دالة تطبيق الألوان
        if hasattr(window, 'apply_row_colors'):
            print("   ✅ دالة تطبيق الألوان موجودة")
        else:
            print("   ❌ دالة تطبيق الألوان غير موجودة")
        
        # اختبار تحميل البيانات
        print("\n📊 اختبار تحميل البيانات:")
        try:
            window.load_data()
            print("   ✅ تم تحميل البيانات بنجاح")
            
            # التحقق من عدد الصفوف في الجدول
            row_count = window.orders_table.rowCount()
            print(f"   📋 عدد طلبات الشراء: {row_count}")
            
        except Exception as e:
            print(f"   ❌ خطأ في تحميل البيانات: {str(e)}")
        
        # عرض النافذة للاختبار البصري
        print("\n👁️ عرض النافذة للاختبار البصري...")
        window.show()
        
        print("\n🎉 انتهى الاختبار!")
        print("💡 تحقق من:")
        print("   • حقل تاريخ الطلب يدعم الإدخال المرن")
        print("   • الجدول يظهر الطلبات بألوان مختلفة")
        print("   • مفتاح الألوان يظهر أسفل الجدول")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(test_purchase_orders_enhancements())
