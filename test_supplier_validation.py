#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مشكلة التحقق من المورد في شاشة الشحنة الجديدة
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_supplier_validation():
    """اختبار التحقق من المورد"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار التحقق من المورد ===")
        
        window = NewShipmentWindow()
        
        # اختبار 1: التحقق من الحالة الأولية
        print(f"✅ حالة المورد الأولية: {window.supplier_edit.text()}")
        print(f"✅ معرف المورد الأولي: {window.supplier_edit.property('supplier_id')}")
        
        # اختبار 2: ملء البيانات التجريبية
        print("\n--- ملء البيانات التجريبية ---")
        window.fill_test_data()
        
        print(f"✅ نص المورد بعد التعبئة: {window.supplier_edit.text()}")
        print(f"✅ معرف المورد بعد التعبئة: {window.supplier_edit.property('supplier_id')}")
        
        # اختبار 3: التحقق من صحة البيانات
        print("\n--- اختبار التحقق من الصحة ---")
        validation_result = window.validate_data()
        print(f"✅ نتيجة التحقق من الصحة: {'نجح' if validation_result else 'فشل'}")
        
        # اختبار 4: محاولة الحفظ
        print("\n--- اختبار الحفظ ---")
        if validation_result:
            print("✅ التحقق من الصحة نجح - يمكن المتابعة للحفظ")
            # لا نقوم بالحفظ الفعلي لتجنب إضافة بيانات تجريبية
        else:
            print("❌ التحقق من الصحة فشل - لن يتم الحفظ")
        
        print("\n=== انتهى الاختبار ===")
        return validation_result
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_supplier_validation()
    if success:
        print("🎉 تم حل مشكلة التحقق من المورد!")
    else:
        print("⚠️ لا تزال هناك مشكلة في التحقق من المورد")
