#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة حفظ الشحنة - 'status' is an invalid keyword argument
Test Save Fix - 'status' is an invalid keyword argument
"""

import sys
import os
import time
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_shipment_data_preparation():
    """اختبار تحضير بيانات الشحنة"""
    print("🔍 اختبار تحضير بيانات الشحنة...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        from src.database.database_manager import DatabaseManager
        from src.database.models import Shipment
        
        # إنشاء QApplication إذا لم يكن موجوداً
        if not QApplication.instance():
            app = QApplication(sys.argv)
            print("✅ تم إنشاء QApplication")
        
        # إنشاء نافذة جديدة
        window = NewShipmentWindow()
        print("✅ تم إنشاء NewShipmentWindow")
        
        # محاولة تحضير البيانات
        shipment_data = window.prepare_shipment_data()
        print("✅ تم تحضير بيانات الشحنة بنجاح")
        
        # التحقق من وجود الحقول المطلوبة
        required_fields = ['shipment_status', 'clearance_status']
        for field in required_fields:
            if field in shipment_data:
                print(f"✅ الحقل '{field}' موجود: {shipment_data[field]}")
            else:
                print(f"⚠️ الحقل '{field}' مفقود")
        
        # التحقق من عدم وجود الحقل الخاطئ
        if 'status' in shipment_data:
            print("❌ الحقل الخاطئ 'status' لا يزال موجوداً!")
            return False
        else:
            print("✅ الحقل الخاطئ 'status' غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shipment_model_fields():
    """اختبار حقول نموذج الشحنة"""
    print("\n🔍 اختبار حقول نموذج الشحنة...")
    
    try:
        from src.database.models import Shipment
        
        # إنشاء كائن شحنة وهمي للاختبار
        test_data = {
            'shipment_number': 'TEST-001',
            'supplier_id': 1,
            'shipment_status': 'تحت الطلب',
            'clearance_status': 'بدون الافراج'
        }
        
        # محاولة إنشاء كائن الشحنة
        shipment = Shipment(**test_data)
        print("✅ تم إنشاء كائن الشحنة بنجاح")
        
        # التحقق من الحقول
        print(f"✅ shipment_status: {shipment.shipment_status}")
        print(f"✅ clearance_status: {shipment.clearance_status}")
        
        # محاولة استخدام الحقل الخاطئ
        try:
            wrong_data = {
                'shipment_number': 'TEST-002',
                'supplier_id': 1,
                'status': 'تحت الطلب'  # هذا خطأ
            }
            wrong_shipment = Shipment(**wrong_data)
            print("❌ تم قبول الحقل الخاطئ 'status' - هذا خطأ!")
            return False
        except TypeError as e:
            if "'status' is an invalid keyword argument" in str(e):
                print("✅ تم رفض الحقل الخاطئ 'status' بشكل صحيح")
            else:
                print(f"⚠️ خطأ مختلف: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_save_simulation():
    """محاكاة عملية حفظ قاعدة البيانات"""
    print("\n🔍 محاكاة عملية حفظ قاعدة البيانات...")
    
    try:
        from src.database.database_manager import DatabaseManager
        from src.database.models import Shipment
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # بيانات اختبار صحيحة
        correct_data = {
            'shipment_number': f'TEST-SAVE-{int(time.time())}',
            'supplier_id': 1,
            'shipment_status': 'تحت الطلب',
            'clearance_status': 'بدون الافراج',
            'notes': 'اختبار إصلاح مشكلة الحفظ'
        }
        
        # محاولة إنشاء وحفظ الشحنة
        shipment = Shipment(**correct_data)
        session.add(shipment)
        session.commit()
        
        print(f"✅ تم حفظ الشحنة بنجاح - ID: {shipment.id}")
        
        # التحقق من البيانات المحفوظة
        saved_shipment = session.get(Shipment, shipment.id)
        if saved_shipment:
            print(f"✅ تم استرجاع الشحنة: {saved_shipment.shipment_number}")
            print(f"✅ حالة الشحنة: {saved_shipment.shipment_status}")
            print(f"✅ حالة الإفراج: {saved_shipment.clearance_status}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة الحفظ: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح مشكلة حفظ الشحنة")
    print("="*60)
    
    tests = [
        ("اختبار تحضير بيانات الشحنة", test_shipment_data_preparation),
        ("اختبار حقول نموذج الشحنة", test_shipment_model_fields),
        ("محاكاة عملية حفظ قاعدة البيانات", test_database_save_simulation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print("📊 نتائج الاختبار النهائية")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! تم إصلاح المشكلة بنجاح")
        print("💡 يمكنك الآن استخدام نافذة الشحنة الجديدة بدون مشاكل")
    else:
        print("⚠️ بعض الاختبارات فشلت - قد تحتاج مراجعة إضافية")
    
    print("="*60)

if __name__ == "__main__":
    main()
