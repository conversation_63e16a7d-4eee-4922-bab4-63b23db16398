# -*- coding: utf-8 -*-
"""
سكريبت تحديث مخطط جدول الموردين
Update Suppliers Table Schema
"""

import sqlite3
import os
from pathlib import Path

def update_suppliers_schema():
    """تحديث مخطط جدول الموردين لإضافة الحقول الجديدة"""
    
    # مسار قاعدة البيانات
    db_path = Path("data/proshipment.db")
    
    if not db_path.exists():
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("🔄 بدء تحديث مخطط جدول الموردين...")
        
        # قائمة الحقول الجديدة المطلوب إضافتها
        new_columns = [
            ("supplier_type", "VARCHAR(50)", "نوع المورد"),
            ("commercial_register", "VARCHAR(50)", "السجل التجاري"),
            ("mobile", "VARCHAR(50)", "الجوال"),
            ("website", "VARCHAR(200)", "الموقع الإلكتروني"),
            ("country", "VARCHAR(100)", "الدولة"),
            ("city", "VARCHAR(100)", "المدينة"),
            ("postal_code", "VARCHAR(20)", "الرمز البريدي")
        ]
        
        # التحقق من الحقول الموجودة
        cursor.execute("PRAGMA table_info(suppliers)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة الحقول الجديدة
        for column_name, column_type, description in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE suppliers ADD COLUMN {column_name} {column_type}"
                    cursor.execute(sql)
                    print(f"✅ تم إضافة حقل: {column_name} ({description})")
                except sqlite3.Error as e:
                    print(f"❌ خطأ في إضافة حقل {column_name}: {e}")
            else:
                print(f"⚠️  حقل {column_name} موجود بالفعل")
        
        # تحديث نوع حقل payment_terms إلى INTEGER
        try:
            # إنشاء جدول مؤقت بالمخطط الجديد
            cursor.execute("""
                CREATE TABLE suppliers_temp (
                    id INTEGER PRIMARY KEY,
                    code VARCHAR(50) UNIQUE,
                    name VARCHAR(200) NOT NULL,
                    name_en VARCHAR(200),
                    supplier_type VARCHAR(50),
                    contact_person VARCHAR(100),
                    tax_number VARCHAR(50),
                    commercial_register VARCHAR(50),
                    phone VARCHAR(50),
                    mobile VARCHAR(50),
                    email VARCHAR(100),
                    website VARCHAR(200),
                    country VARCHAR(100),
                    city VARCHAR(100),
                    address TEXT,
                    postal_code VARCHAR(20),
                    credit_limit REAL DEFAULT 0.0,
                    payment_terms INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # نسخ البيانات من الجدول الأصلي
            cursor.execute("""
                INSERT INTO suppliers_temp (
                    id, code, name, name_en, contact_person, tax_number,
                    phone, email, address, credit_limit, payment_terms,
                    is_active, created_at, updated_at
                )
                SELECT 
                    id, code, name, name_en, contact_person, tax_number,
                    phone, email, address, credit_limit, 
                    CASE 
                        WHEN payment_terms IS NULL THEN 0
                        WHEN payment_terms = '' THEN 0
                        ELSE CAST(payment_terms AS INTEGER)
                    END as payment_terms,
                    is_active, created_at, updated_at
                FROM suppliers
            """)
            
            # حذف الجدول الأصلي
            cursor.execute("DROP TABLE suppliers")
            
            # إعادة تسمية الجدول المؤقت
            cursor.execute("ALTER TABLE suppliers_temp RENAME TO suppliers")
            
            print("✅ تم تحديث مخطط الجدول بنجاح")
            
        except sqlite3.Error as e:
            print(f"❌ خطأ في تحديث مخطط الجدول: {e}")
            conn.rollback()
            return False
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة النهائية
        cursor.execute("PRAGMA table_info(suppliers)")
        final_columns = cursor.fetchall()
        
        print("\n📋 مخطط الجدول النهائي:")
        for column in final_columns:
            print(f"   - {column[1]} ({column[2]})")
        
        print(f"\n✅ تم تحديث جدول الموردين بنجاح! العدد الإجمالي للحقول: {len(final_columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في تحديث قاعدة البيانات: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 بدء تحديث مخطط جدول الموردين...")
    success = update_suppliers_schema()
    
    if success:
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("يمكنك الآن استخدام جميع حقول الموردين الجديدة.")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
