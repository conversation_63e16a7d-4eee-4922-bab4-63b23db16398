#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار العلامة المائية في الواجهة الرئيسية
Test Watermark in Main Interface
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.main_window import MainWindow

def test_watermark():
    """اختبار العلامة المائية"""
    print("🎨 اختبار العلامة المائية في الواجهة الرئيسية")
    print("=" * 60)
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        print("🎯 العلامة المائية يجب أن تظهر في خلفية المحتوى الرئيسي")
        print("📋 التفاصيل:")
        print("   • الشفافية: 8% (شفافة جداً)")
        print("   • الموقع: وسط الشاشة")
        print("   • الحجم: ثلث حجم الشاشة")
        print("   • النوع: SVG أو PNG بديل")
        
        # عرض النافذة
        main_window.show()
        
        print("\n🎉 تم تطبيق العلامة المائية بنجاح!")
        print("💡 يمكنك الآن رؤية العلامة المائية في خلفية الواجهة")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العلامة المائية: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_watermark())
