#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار دورة الحفظ الكاملة - حفظ شحنة والتحقق من ظهورها في الشاشات الأخرى
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication, QTableWidgetItem
from PySide6.QtCore import Qt, QDate
from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.ui.shipments.shipments_window import ShipmentsWindow
from src.ui.shipments.shipment_tracking_window import ShipmentTrackingWindow
from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier, Item

def create_test_shipment():
    """إنشاء شحنة تجريبية"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار دورة الحفظ الكاملة ===")
        
        # إنشاء نافذة الشحنة الجديدة
        window = NewShipmentWindow()
        
        # الحصول على مورد من قاعدة البيانات
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            # الحصول على أول مورد
            supplier = session.query(Supplier).filter(Supplier.is_active == True).first()
            if not supplier:
                print("❌ لا توجد موردين في قاعدة البيانات")
                return False
                
            # الحصول على أول صنف
            item = session.query(Item).filter(Item.is_active == True).first()
            if not item:
                print("❌ لا توجد أصناف في قاعدة البيانات")
                return False
                
            print(f"✅ سيتم استخدام المورد: {supplier.name}")
            print(f"✅ سيتم استخدام الصنف: {item.name}")
            
        finally:
            session.close()
        
        # ملء البيانات الأساسية
        window.shipment_number_edit.setText("TEST-001")
        window.supplier_edit.setText(supplier.name)
        window.supplier_edit.setProperty("supplier_id", supplier.id)
        window.supplier_invoice_edit.setText("INV-001")
        window.shipment_status_combo.setCurrentText("جديدة")
        window.clearance_status_combo.setCurrentText("لم يتم الإفراج")
        window.tracking_number_edit.setText("TRK-001")
        window.bill_of_lading_edit.setText("BL-001")
        window.notes_edit.setPlainText("شحنة تجريبية للاختبار")
        
        # إضافة صنف إلى الجدول
        window.items_table.setRowCount(1)
        window.items_table.setItem(0, 0, QTableWidgetItem(item.code))
        window.items_table.item(0, 0).setData(Qt.UserRole, item.id)
        window.items_table.setItem(0, 1, QTableWidgetItem(item.name))
        window.items_table.setItem(0, 2, QTableWidgetItem("10"))  # الكمية
        window.items_table.setItem(0, 3, QTableWidgetItem("100.00"))  # سعر الوحدة
        window.items_table.setItem(0, 4, QTableWidgetItem("1000.00"))  # الإجمالي
        
        print("✅ تم ملء البيانات التجريبية")
        
        # حفظ الشحنة
        print("🔄 جاري حفظ الشحنة...")
        success = window.save_shipment()
        
        if success:
            print("✅ تم حفظ الشحنة بنجاح!")
            shipment_id = window.current_shipment_id
            print(f"📋 معرف الشحنة: {shipment_id}")
            
            # التحقق من وجود الشحنة في قاعدة البيانات
            session = db_manager.get_session()
            try:
                saved_shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
                if saved_shipment:
                    print(f"✅ الشحنة موجودة في قاعدة البيانات: {saved_shipment.shipment_number}")
                    
                    # اختبار شاشة إدارة الشحنات
                    print("\n🔍 اختبار شاشة إدارة الشحنات...")
                    shipments_window = ShipmentsWindow()
                    shipments_window.load_shipments()
                    
                    # البحث عن الشحنة في الجدول
                    found_in_shipments = False
                    for row in range(shipments_window.shipments_table.rowCount()):
                        if shipments_window.shipments_table.item(row, 1).text() == "TEST-001":
                            found_in_shipments = True
                            break
                    
                    if found_in_shipments:
                        print("✅ الشحنة ظاهرة في شاشة إدارة الشحنات")
                    else:
                        print("❌ الشحنة غير ظاهرة في شاشة إدارة الشحنات")
                    
                    # اختبار شاشة تتبع الشحنات
                    print("\n🔍 اختبار شاشة تتبع الشحنات...")
                    tracking_window = ShipmentTrackingWindow()
                    tracking_window.load_tracking_data()
                    
                    # البحث عن الشحنة في جدول التتبع
                    found_in_tracking = False
                    for row in range(tracking_window.tracking_table.rowCount()):
                        if tracking_window.tracking_table.item(row, 1).text() == "TEST-001":
                            found_in_tracking = True
                            break
                    
                    if found_in_tracking:
                        print("✅ الشحنة ظاهرة في شاشة تتبع الشحنات")
                    else:
                        print("❌ الشحنة غير ظاهرة في شاشة تتبع الشحنات")
                    
                    # عرض النتائج النهائية
                    print("\n📊 نتائج الاختبار:")
                    print(f"   • حفظ الشحنة: {'✅ نجح' if success else '❌ فشل'}")
                    print(f"   • ظهور في إدارة الشحنات: {'✅ نجح' if found_in_shipments else '❌ فشل'}")
                    print(f"   • ظهور في تتبع الشحنات: {'✅ نجح' if found_in_tracking else '❌ فشل'}")
                    
                    if success and found_in_shipments and found_in_tracking:
                        print("\n🎉 تم إصلاح المشكلة بنجاح!")
                        print("   الشحنات المحفوظة تظهر الآن في جميع الشاشات")
                    else:
                        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
                        
                else:
                    print("❌ الشحنة غير موجودة في قاعدة البيانات")
                    
            finally:
                session.close()
                
        else:
            print("❌ فشل في حفظ الشحنة")
            
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_test_shipment()
