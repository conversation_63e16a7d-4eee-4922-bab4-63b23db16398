# -*- coding: utf-8 -*-
"""
تبويب إدارة حوالات الموردين المتقدم
Advanced Supplier Remittances Management Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QDateEdit,
                               QTextEdit, QAbstractItemView, QDoubleSpinBox, QFrame,
                               QSplitter, QScrollArea, QGridLayout, QSpacerItem, QSizePolicy,
                               QCheckBox, QSpinBox, QProgressBar)
from PySide6.QtCore import Qt, QDate, QTimer, Signal
from PySide6.QtGui import QFont, QIcon

from ...database.database_manager import DatabaseManager


class AdvancedRemittancesTab(QWidget):
    """تبويب إدارة حوالات الموردين المتقدم"""
    
    # إشارات
    remittance_added = Signal(dict)
    remittance_updated = Signal(dict)
    remittance_deleted = Signal(int)
    remittance_selected = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        
        # متغيرات النظام
        self.current_remittance_id = None
        self.is_editing = False
        self.selected_suppliers = []  # قائمة الموردين المحددين
        
        # إعداد الواجهة المتقدمة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        print("🚀 تم إنشاء تبويب حوالات الموردين المتقدم")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم وفقاً للتخطيط المطلوب"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel("إدارة حوالات الموردين الشاملة")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 12px;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # القسم العلوي - معلومات الحوالة الأساسية
        basic_info_section = self.create_basic_info_section()
        main_layout.addWidget(basic_info_section)

        # القسم الأوسط - تقسيم أفقي (معلومات البنوك + جدول الموردين)
        middle_splitter = QSplitter(Qt.Horizontal)
        middle_splitter.setChildrenCollapsible(False)

        # الجانب الأيسر - معلومات البنوك والرسوم
        left_section = self.create_left_section()
        middle_splitter.addWidget(left_section)

        # الجانب الأيمن - جدول الموردين
        right_section = self.create_suppliers_section()
        middle_splitter.addWidget(right_section)

        # تحديد نسب التقسيم (50:50)
        middle_splitter.setSizes([500, 500])
        main_layout.addWidget(middle_splitter)

        # القسم السفلي - أزرار التحكم
        buttons_section = self.create_control_buttons_section()
        main_layout.addLayout(buttons_section)
        
    def create_advanced_form_section(self):
        """إنشاء قسم النموذج المتقدم"""
        # إطار النموذج الرئيسي
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                margin: 5px;
            }
        """)
        
        # تخطيط النموذج مع scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # الويدجت الداخلي للنموذج
        form_content = QWidget()
        form_content.setMinimumWidth(900)
        
        form_layout = QVBoxLayout(form_content)
        form_layout.setSpacing(20)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # قسم المعلومات الأساسية
        basic_section = self.create_basic_info_section()
        form_layout.addWidget(basic_section)
        
        # قسم الموردين المتعددين
        suppliers_section = self.create_multiple_suppliers_section()
        form_layout.addWidget(suppliers_section)
        
        # قسم معلومات البنوك
        banks_section = self.create_banks_info_section()
        form_layout.addWidget(banks_section)
        
        # قسم الرسوم والمعلومات الإضافية
        fees_section = self.create_fees_info_section()
        form_layout.addWidget(fees_section)
        
        # قسم حالة الحوالة والموافقات
        status_section = self.create_status_section()
        form_layout.addWidget(status_section)
        
        # مساحة مرنة في النهاية
        form_layout.addStretch()
        
        # إضافة المحتوى إلى scroll area
        scroll_area.setWidget(form_content)
        
        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(form_frame)
        frame_layout.setContentsMargins(5, 5, 5, 5)
        frame_layout.addWidget(scroll_area)
        
        return form_frame
        
    def create_basic_info_section(self):
        """إنشاء قسم المعلومات الأساسية - القسم العلوي"""
        group = QGroupBox("معلومات الحوالة الأساسية")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #2c3e50;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 25, 20, 20)

        # الصف الأول: رقم الحوالة وتاريخ الحوالة
        layout.addWidget(QLabel("رقم الحوالة:"), 0, 0)
        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.remittance_number_edit.setReadOnly(True)
        self.remittance_number_edit.setMinimumHeight(35)
        self.remittance_number_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.remittance_number_edit, 0, 1)

        layout.addWidget(QLabel("تاريخ الحوالة:"), 0, 2)
        self.remittance_date_edit = QDateEdit()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.remittance_date_edit.setCalendarPopup(True)
        self.remittance_date_edit.setMinimumHeight(35)
        self.remittance_date_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.remittance_date_edit, 0, 3)

        # الصف الثاني: العملة وسعر الصرف
        layout.addWidget(QLabel("العملة:"), 1, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(35)
        self.currency_combo.setStyleSheet(self.get_clean_combo_style())
        layout.addWidget(self.currency_combo, 1, 1)

        layout.addWidget(QLabel("سعر الصرف:"), 1, 2)
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.001, 999999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0)
        self.exchange_rate_spin.setMinimumHeight(35)
        self.exchange_rate_spin.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.exchange_rate_spin, 1, 3)

        # الصف الثالث: المبلغ الإجمالي وعدد الموردين
        layout.addWidget(QLabel("المبلغ الإجمالي:"), 2, 0)
        self.total_amount_edit = QLineEdit()
        self.total_amount_edit.setReadOnly(True)
        self.total_amount_edit.setMinimumHeight(35)
        self.total_amount_edit.setStyleSheet(self.get_clean_readonly_style())
        layout.addWidget(self.total_amount_edit, 2, 1)

        layout.addWidget(QLabel("عدد الموردين:"), 2, 2)
        self.suppliers_count_edit = QLineEdit()
        self.suppliers_count_edit.setReadOnly(True)
        self.suppliers_count_edit.setMinimumHeight(35)
        self.suppliers_count_edit.setStyleSheet(self.get_clean_readonly_style())
        layout.addWidget(self.suppliers_count_edit, 2, 3)

        # الصف الرابع: الحالة والغرض
        layout.addWidget(QLabel("حالة الحوالة:"), 3, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "مسودة", "في انتظار الموافقة", "موافق عليها",
            "مرسلة للبنك", "مؤكدة من البنك", "مرحلة للحسابات"
        ])
        self.status_combo.setMinimumHeight(35)
        self.status_combo.setStyleSheet(self.get_clean_combo_style())
        layout.addWidget(self.status_combo, 3, 1)

        layout.addWidget(QLabel("الغرض من التحويل:"), 3, 2)
        self.purpose_edit = QLineEdit()
        self.purpose_edit.setPlaceholderText("الغرض من التحويل...")
        self.purpose_edit.setMinimumHeight(35)
        self.purpose_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.purpose_edit, 3, 3)

        return group

    def create_left_section(self):
        """إنشاء القسم الأيسر - معلومات البنوك والرسوم"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # قسم معلومات البنوك
        banks_section = self.create_banks_section()
        left_layout.addWidget(banks_section)

        # قسم الرسوم والمعلومات الإضافية
        fees_section = self.create_fees_section()
        left_layout.addWidget(fees_section)

        return left_widget

    def create_banks_section(self):
        """إنشاء قسم معلومات البنوك"""
        group = QGroupBox("معلومات البنوك")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #2c3e50;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 25, 20, 20)

        # البنك المرسل
        layout.addWidget(QLabel("البنك المرسل:"), 0, 0)
        self.sender_bank_edit = QLineEdit()
        self.sender_bank_edit.setPlaceholderText("اسم البنك المرسل")
        self.sender_bank_edit.setMinimumHeight(35)
        self.sender_bank_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.sender_bank_edit, 0, 1)

        # رقم الحساب المرسل
        layout.addWidget(QLabel("رقم الحساب المرسل:"), 1, 0)
        self.sender_account_edit = QLineEdit()
        self.sender_account_edit.setPlaceholderText("رقم الحساب المرسل")
        self.sender_account_edit.setMinimumHeight(35)
        self.sender_account_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.sender_account_edit, 1, 1)

        # البنك المستقبل
        layout.addWidget(QLabel("البنك المستقبل:"), 2, 0)
        self.receiver_bank_edit = QLineEdit()
        self.receiver_bank_edit.setPlaceholderText("اسم البنك المستقبل")
        self.receiver_bank_edit.setMinimumHeight(35)
        self.receiver_bank_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.receiver_bank_edit, 2, 1)

        # دولة البنك
        layout.addWidget(QLabel("دولة البنك:"), 3, 0)
        self.receiver_country_edit = QLineEdit()
        self.receiver_country_edit.setPlaceholderText("دولة البنك المستقبل")
        self.receiver_country_edit.setMinimumHeight(35)
        self.receiver_country_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.receiver_country_edit, 3, 1)

        # كود SWIFT
        layout.addWidget(QLabel("كود SWIFT:"), 4, 0)
        self.swift_code_edit = QLineEdit()
        self.swift_code_edit.setPlaceholderText("كود SWIFT للتحويلات الدولية")
        self.swift_code_edit.setMinimumHeight(35)
        self.swift_code_edit.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.swift_code_edit, 4, 1)

        return group

    def create_fees_section(self):
        """إنشاء قسم الرسوم والمعلومات الإضافية"""
        group = QGroupBox("الرسوم والمعلومات الإضافية")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #2c3e50;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 25, 20, 20)

        # رسوم التحويل
        layout.addWidget(QLabel("رسوم التحويل:"), 0, 0)
        self.transfer_fees_spin = QDoubleSpinBox()
        self.transfer_fees_spin.setRange(0, 999999)
        self.transfer_fees_spin.setDecimals(2)
        self.transfer_fees_spin.setSuffix(" ريال")
        self.transfer_fees_spin.setMinimumHeight(35)
        self.transfer_fees_spin.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.transfer_fees_spin, 0, 1)

        # رسوم البنك
        layout.addWidget(QLabel("رسوم البنك:"), 1, 0)
        self.bank_charges_spin = QDoubleSpinBox()
        self.bank_charges_spin.setRange(0, 999999)
        self.bank_charges_spin.setDecimals(2)
        self.bank_charges_spin.setSuffix(" ريال")
        self.bank_charges_spin.setMinimumHeight(35)
        self.bank_charges_spin.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.bank_charges_spin, 1, 1)

        # رسوم أخرى
        layout.addWidget(QLabel("رسوم أخرى:"), 2, 0)
        self.other_charges_spin = QDoubleSpinBox()
        self.other_charges_spin.setRange(0, 999999)
        self.other_charges_spin.setDecimals(2)
        self.other_charges_spin.setSuffix(" ريال")
        self.other_charges_spin.setMinimumHeight(35)
        self.other_charges_spin.setStyleSheet(self.get_clean_input_style())
        layout.addWidget(self.other_charges_spin, 2, 1)

        # إجمالي الرسوم
        layout.addWidget(QLabel("إجمالي الرسوم:"), 3, 0)
        self.total_charges_edit = QLineEdit()
        self.total_charges_edit.setReadOnly(True)
        self.total_charges_edit.setMinimumHeight(35)
        self.total_charges_edit.setStyleSheet(self.get_clean_readonly_style())
        layout.addWidget(self.total_charges_edit, 3, 1)

        # الملاحظات
        layout.addWidget(QLabel("الملاحظات:"), 4, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setMinimumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        self.notes_edit.setStyleSheet(self.get_clean_text_style())
        layout.addWidget(self.notes_edit, 4, 1)

        return group

    def create_suppliers_section(self):
        """إنشاء قسم الموردين - الجانب الأيمن"""
        suppliers_widget = QWidget()
        suppliers_layout = QVBoxLayout(suppliers_widget)
        suppliers_layout.setSpacing(10)
        suppliers_layout.setContentsMargins(0, 0, 0, 0)

        # عنوان قسم الموردين
        suppliers_title = QLabel("عدد الموردين")
        suppliers_title.setFont(QFont("Arial", 12, QFont.Bold))
        suppliers_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 8px 15px;
                border-radius: 6px;
                border: 1px solid #bdc3c7;
            }
        """)
        suppliers_layout.addWidget(suppliers_title)

        # شريط أدوات الموردين
        toolbar_layout = QHBoxLayout()

        # زر إضافة مورد
        add_supplier_btn = QPushButton("إضافة مورد")
        add_supplier_btn.setMinimumHeight(35)
        add_supplier_btn.setStyleSheet(self.get_clean_button_style("#27ae60"))
        add_supplier_btn.clicked.connect(self.add_supplier_to_remittance)
        toolbar_layout.addWidget(add_supplier_btn)

        # زر تعديل مورد
        edit_supplier_btn = QPushButton("تعديل مورد")
        edit_supplier_btn.setMinimumHeight(35)
        edit_supplier_btn.setStyleSheet(self.get_clean_button_style("#f39c12"))
        edit_supplier_btn.clicked.connect(self.edit_supplier_amount)
        toolbar_layout.addWidget(edit_supplier_btn)

        # زر حذف مورد
        remove_supplier_btn = QPushButton("حذف مورد")
        remove_supplier_btn.setMinimumHeight(35)
        remove_supplier_btn.setStyleSheet(self.get_clean_button_style("#e74c3c"))
        remove_supplier_btn.clicked.connect(self.remove_supplier_from_remittance)
        toolbar_layout.addWidget(remove_supplier_btn)

        suppliers_layout.addLayout(toolbar_layout)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "المورد", "المبلغ", "العملة", "الحالة", "ملاحظات"
        ])

        # تحسين مظهر الجدول
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        # إعدادات الجدول
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # ضبط عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # ملاحظات

        suppliers_layout.addWidget(self.suppliers_table)

        # إضافة بيانات تجريبية
        self.load_sample_suppliers()

        return suppliers_widget

    def load_sample_suppliers(self):
        """تحميل بيانات موردين تجريبية"""
        sample_suppliers = [
            ("شركة مؤسسة كارفور", "15000.00", "SAR", "في الانتظار", "دفع مستحقات"),
            ("شركة سعد الحكير", "8500.00", "SAR", "في الانتظار", "فاتورة شهرية")
        ]

        self.suppliers_table.setRowCount(len(sample_suppliers))

        for row, (supplier, amount, currency, status, notes) in enumerate(sample_suppliers):
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier))
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(amount))
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(currency))
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(status))
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(notes))

        # تحديث العدادات
        self.update_counters()

        # تحميل العملات
        self.load_currencies()

    def load_currencies(self):
        """تحميل قائمة العملات"""
        currencies = [
            "SAR - ريال سعودي",
            "USD - دولار أمريكي",
            "EUR - يورو",
            "GBP - جنيه إسترليني",
            "AED - درهم إماراتي",
            "KWD - دينار كويتي",
            "BHD - دينار بحريني",
            "QAR - ريال قطري",
            "OMR - ريال عماني",
            "JOD - دينار أردني",
            "EGP - جنيه مصري"
        ]

        self.currency_combo.addItems(currencies)
        self.currency_combo.setCurrentText("SAR - ريال سعودي")

        # ربط الأحداث
        self.connect_events()

    def connect_events(self):
        """ربط الأحداث"""
        # ربط تحديث الرسوم عند تغيير القيم
        self.transfer_fees_spin.valueChanged.connect(self.update_counters)
        self.bank_charges_spin.valueChanged.connect(self.update_counters)
        self.other_charges_spin.valueChanged.connect(self.update_counters)

    def create_control_buttons_section(self):
        """إنشاء قسم أزرار التحكم - القسم السفلي"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setMinimumHeight(40)
        save_btn.setStyleSheet(self.get_clean_button_style("#3498db"))
        save_btn.clicked.connect(self.save_current_remittance)
        buttons_layout.addWidget(save_btn)

        # زر موافقة
        approve_btn = QPushButton("موافقة")
        approve_btn.setMinimumHeight(40)
        approve_btn.setStyleSheet(self.get_clean_button_style("#27ae60"))
        approve_btn.clicked.connect(self.approve_remittance)
        buttons_layout.addWidget(approve_btn)

        # زر إرسال للبنك
        send_bank_btn = QPushButton("إرسال للبنك")
        send_bank_btn.setMinimumHeight(40)
        send_bank_btn.setStyleSheet(self.get_clean_button_style("#f39c12"))
        send_bank_btn.clicked.connect(self.send_to_bank)
        buttons_layout.addWidget(send_bank_btn)

        # زر تأكيد بنكي
        confirm_btn = QPushButton("تأكيد بنكي")
        confirm_btn.setMinimumHeight(40)
        confirm_btn.setStyleSheet(self.get_clean_button_style("#9b59b6"))
        confirm_btn.clicked.connect(self.bank_confirmation)
        buttons_layout.addWidget(confirm_btn)

        # زر ترحيل للحسابات
        post_btn = QPushButton("ترحيل للحسابات")
        post_btn.setMinimumHeight(40)
        post_btn.setStyleSheet(self.get_clean_button_style("#e67e22"))
        post_btn.clicked.connect(self.post_to_accounts)
        buttons_layout.addWidget(post_btn)

        # مساحة مرنة
        buttons_layout.addStretch()

        # زر تحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.setMinimumHeight(40)
        refresh_btn.setStyleSheet(self.get_clean_button_style("#34495e"))
        refresh_btn.clicked.connect(self.refresh_data)
        buttons_layout.addWidget(refresh_btn)

        return buttons_layout

    def get_clean_input_style(self):
        """الحصول على نمط الحقول النظيف"""
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit, QSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                color: #2c3e50;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus, QSpinBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_clean_readonly_style(self):
        """الحصول على نمط الحقول للقراءة فقط النظيف"""
        return """
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: #ecf0f1;
                color: #7f8c8d;
            }
        """

    def get_clean_combo_style(self):
        """الحصول على نمط القوائم المنسدلة النظيف"""
        return """
            QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                color: #2c3e50;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
            }
        """

    def get_clean_text_style(self):
        """الحصول على نمط النصوص المتعددة الأسطر النظيف"""
        return """
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_clean_button_style(self, color):
        """الحصول على نمط الأزرار النظيف"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # إزالة # من بداية اللون
        color = color.lstrip('#')

        # تحويل إلى RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        # تطبيق التغميق
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)

        # التأكد من أن القيم في النطاق الصحيح
        r = max(0, min(255, r))
        g = max(0, min(255, g))
        b = max(0, min(255, b))

        return f"#{r:02x}{g:02x}{b:02x}"

    def update_counters(self):
        """تحديث العدادات والمبالغ"""
        row_count = self.suppliers_table.rowCount()
        self.suppliers_count_edit.setText(str(row_count))

        # حساب المبلغ الإجمالي
        total_amount = 0
        for row in range(row_count):
            amount_item = self.suppliers_table.item(row, 1)
            if amount_item:
                try:
                    amount = float(amount_item.text())
                    total_amount += amount
                except ValueError:
                    pass

        self.total_amount_edit.setText(f"{total_amount:,.2f} ريال")

        # تحديث إجمالي الرسوم
        total_charges = (self.transfer_fees_spin.value() +
                        self.bank_charges_spin.value() +
                        self.other_charges_spin.value())
        self.total_charges_edit.setText(f"{total_charges:,.2f} ريال")

    def add_supplier_to_remittance(self):
        """إضافة مورد جديد للحوالة"""
        from src.ui.suppliers.supplier_selection_dialog import SupplierSelectionDialog

        dialog = SupplierSelectionDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            selected_supplier = dialog.get_selected_supplier()
            if selected_supplier:
                # إضافة المورد للجدول
                row_count = self.suppliers_table.rowCount()
                self.suppliers_table.insertRow(row_count)

                self.suppliers_table.setItem(row_count, 0, QTableWidgetItem(selected_supplier['name']))
                self.suppliers_table.setItem(row_count, 1, QTableWidgetItem("0.00"))
                self.suppliers_table.setItem(row_count, 2, QTableWidgetItem("SAR"))
                self.suppliers_table.setItem(row_count, 3, QTableWidgetItem("في الانتظار"))
                self.suppliers_table.setItem(row_count, 4, QTableWidgetItem(""))

                self.update_counters()

    def remove_supplier_from_remittance(self):
        """إزالة مورد من الحوالة"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا المورد من الحوالة؟",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.suppliers_table.removeRow(current_row)
                self.update_counters()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد لحذفه")

    def edit_supplier_amount(self):
        """تعديل مبلغ المورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            current_amount = self.suppliers_table.item(current_row, 1).text()

            amount, ok = QInputDialog.getDouble(
                self, "تعديل المبلغ",
                "أدخل المبلغ الجديد:",
                float(current_amount) if current_amount else 0.0,
                0.0, 999999999.99, 2
            )

            if ok:
                self.suppliers_table.setItem(current_row, 1, QTableWidgetItem(f"{amount:.2f}"))
                self.update_counters()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد لتعديل مبلغه")

    def save_current_remittance(self):
        """حفظ الحوالة الحالية"""
        QMessageBox.information(self, "حفظ", "تم حفظ الحوالة بنجاح")

    def approve_remittance(self):
        """الموافقة على الحوالة"""
        reply = QMessageBox.question(
            self, "تأكيد الموافقة",
            "هل أنت متأكد من الموافقة على هذه الحوالة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.status_combo.setCurrentText("موافق عليها")
            QMessageBox.information(self, "موافقة", "تم الموافقة على الحوالة")

    def send_to_bank(self):
        """إرسال الحوالة للبنك"""
        reply = QMessageBox.question(
            self, "تأكيد الإرسال",
            "هل أنت متأكد من إرسال هذه الحوالة للبنك؟",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.status_combo.setCurrentText("مرسلة للبنك")
            QMessageBox.information(self, "إرسال", "تم إرسال الحوالة للبنك")

    def bank_confirmation(self):
        """تأكيد بنكي للحوالة"""
        reply = QMessageBox.question(
            self, "تأكيد بنكي",
            "هل تم تأكيد هذه الحوالة من البنك؟",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.status_combo.setCurrentText("مؤكدة من البنك")
            QMessageBox.information(self, "تأكيد بنكي", "تم تأكيد الحوالة من البنك")

    def post_to_accounts(self):
        """ترحيل الحوالة للحسابات"""
        reply = QMessageBox.question(
            self, "تأكيد الترحيل",
            "هل أنت متأكد من ترحيل هذه الحوالة للحسابات؟",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.status_combo.setCurrentText("مرحلة للحسابات")
            QMessageBox.information(self, "ترحيل", "تم ترحيل الحوالة للحسابات")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_sample_suppliers()
        QMessageBox.information(self, "تحديث", "تم تحديث البيانات")







    def create_status_section(self):
        """إنشاء قسم حالة الحوالة والموافقات"""
        group = QGroupBox("📊 حالة الحوالة والموافقات")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #9b59b6;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)

        # الصف الأول: حالة الحوالة وحالة الموافقة
        layout.addWidget(QLabel("حالة الحوالة:"), 0, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "مسودة", "في انتظار الموافقة", "موافق عليها",
            "مرسلة للبنك", "قيد المعالجة في البنك", "مؤكدة من البنك",
            "وصلت للموردين", "مرحلة للحسابات", "ملغية", "فشلت"
        ])
        self.status_combo.setMinimumHeight(45)
        self.status_combo.setStyleSheet(self.get_combo_style())
        layout.addWidget(self.status_combo, 0, 1)

        layout.addWidget(QLabel("حالة الموافقة:"), 0, 2)
        self.approval_status_checkbox = QCheckBox("موافق عليها")
        self.approval_status_checkbox.setMinimumHeight(45)
        self.approval_status_checkbox.setStyleSheet("font-size: 14px; padding: 10px;")
        layout.addWidget(self.approval_status_checkbox, 0, 3)

        # الصف الثاني: تاريخ الموافقة والموافق عليها من
        layout.addWidget(QLabel("تاريخ الموافقة:"), 1, 0)
        self.approval_date_edit = QDateEdit()
        self.approval_date_edit.setCalendarPopup(True)
        self.approval_date_edit.setMinimumHeight(45)
        self.approval_date_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.approval_date_edit, 1, 1)

        layout.addWidget(QLabel("موافق عليها من:"), 1, 2)
        self.approved_by_edit = QLineEdit()
        self.approved_by_edit.setPlaceholderText("اسم الموافق")
        self.approved_by_edit.setMinimumHeight(45)
        self.approved_by_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.approved_by_edit, 1, 3)

        # الصف الثالث: رقم مرجع البنك وتاريخ التأكيد
        layout.addWidget(QLabel("رقم مرجع البنك:"), 2, 0)
        self.bank_reference_edit = QLineEdit()
        self.bank_reference_edit.setPlaceholderText("رقم المرجع من البنك")
        self.bank_reference_edit.setMinimumHeight(45)
        self.bank_reference_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.bank_reference_edit, 2, 1)

        layout.addWidget(QLabel("تاريخ التأكيد البنكي:"), 2, 2)
        self.bank_confirmation_date_edit = QDateEdit()
        self.bank_confirmation_date_edit.setCalendarPopup(True)
        self.bank_confirmation_date_edit.setMinimumHeight(45)
        self.bank_confirmation_date_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.bank_confirmation_date_edit, 2, 3)

        return group

    def create_advanced_table_section(self):
        """إنشاء قسم الجدول المتقدم"""
        table_frame = QFrame()
        table_frame.setFrameStyle(QFrame.StyledPanel)
        table_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                margin: 5px;
            }
        """)

        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)
        table_layout.setSpacing(10)

        # عنوان الجدول مع إحصائيات
        table_header = QHBoxLayout()

        table_title = QLabel("📊 قائمة الحوالات المتقدمة")
        table_title.setFont(QFont("Arial", 14, QFont.Bold))
        table_title.setStyleSheet("color: #2c3e50; padding: 5px;")
        table_header.addWidget(table_title)

        table_header.addStretch()

        # عداد الحوالات
        self.remittances_count_label = QLabel("العدد: 0")
        self.remittances_count_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        table_header.addWidget(self.remittances_count_label)

        table_layout.addLayout(table_header)

        # شريط البحث والفلترة
        search_layout = QHBoxLayout()

        search_label = QLabel("🔍 بحث:")
        search_layout.addWidget(search_label)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الحوالات...")
        self.search_edit.setMinimumHeight(35)
        self.search_edit.setStyleSheet(self.get_input_style())
        search_layout.addWidget(self.search_edit)

        filter_label = QLabel("📋 فلترة:")
        search_layout.addWidget(filter_label)

        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["جميع الحوالات", "مسودة", "في الانتظار", "مؤكدة", "مرحلة"])
        self.filter_combo.setMinimumHeight(35)
        self.filter_combo.setStyleSheet(self.get_combo_style())
        search_layout.addWidget(self.filter_combo)

        table_layout.addLayout(search_layout)

        # الجدول المتقدم
        self.remittances_table = QTableWidget()
        self.remittances_table.setColumnCount(10)
        self.remittances_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "التاريخ", "المبلغ", "العملة",
            "عدد الموردين", "الحالة", "البنك المستقبل",
            "حالة الموافقة", "تاريخ التأكيد", "الملاحظات"
        ])

        # تحسين مظهر الجدول
        self.remittances_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # إعدادات الجدول
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.remittances_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # ضبط عرض الأعمدة
        header = self.remittances_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحوالة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # عدد الموردين
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.Stretch)           # البنك المستقبل
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # حالة الموافقة
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # تاريخ التأكيد
        header.setSectionResizeMode(9, QHeaderView.Stretch)           # الملاحظات

        table_layout.addWidget(self.remittances_table)

        return table_frame

    def create_advanced_buttons_section(self):
        """إنشاء قسم الأزرار المتقدم"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر حوالة جديدة
        self.new_btn = QPushButton("🆕 حوالة جديدة")
        self.new_btn.setMinimumHeight(45)
        self.new_btn.setStyleSheet(self.get_button_style("#27ae60"))
        buttons_layout.addWidget(self.new_btn)

        # زر حفظ
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.setMinimumHeight(45)
        self.save_btn.setStyleSheet(self.get_button_style("#3498db"))
        buttons_layout.addWidget(self.save_btn)

        # زر موافقة
        self.approve_btn = QPushButton("✅ موافقة")
        self.approve_btn.setMinimumHeight(45)
        self.approve_btn.setStyleSheet(self.get_button_style("#2ecc71"))
        buttons_layout.addWidget(self.approve_btn)

        # زر إرسال للبنك
        self.send_to_bank_btn = QPushButton("🏦 إرسال للبنك")
        self.send_to_bank_btn.setMinimumHeight(45)
        self.send_to_bank_btn.setStyleSheet(self.get_button_style("#f39c12"))
        buttons_layout.addWidget(self.send_to_bank_btn)

        # زر تأكيد بنكي
        self.bank_confirm_btn = QPushButton("📋 تأكيد بنكي")
        self.bank_confirm_btn.setMinimumHeight(45)
        self.bank_confirm_btn.setStyleSheet(self.get_button_style("#e67e22"))
        buttons_layout.addWidget(self.bank_confirm_btn)

        # زر ترحيل للحسابات
        self.post_to_accounts_btn = QPushButton("📊 ترحيل للحسابات")
        self.post_to_accounts_btn.setMinimumHeight(45)
        self.post_to_accounts_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        buttons_layout.addWidget(self.post_to_accounts_btn)

        # مساحة مرنة
        buttons_layout.addStretch()

        # زر تحديث
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setMinimumHeight(45)
        self.refresh_btn.setStyleSheet(self.get_button_style("#34495e"))
        buttons_layout.addWidget(self.refresh_btn)

        # زر حذف
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setMinimumHeight(45)
        self.delete_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        buttons_layout.addWidget(self.delete_btn)

        return buttons_layout

    def get_input_style(self):
        """الحصول على نمط الحقول"""
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit, QSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus, QSpinBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_readonly_style(self):
        """الحصول على نمط الحقول للقراءة فقط"""
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: #ecf0f1;
                color: #7f8c8d;
            }
        """

    def get_combo_style(self):
        """الحصول على نمط القوائم المنسدلة"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
            }
        """

    def get_text_style(self):
        """الحصول على نمط النصوص المتعددة الأسطر"""
        return """
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_button_style(self, color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
                transform: translateY(0px);
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#e74c3c": "#c0392b",
            "#9b59b6": "#8e44ad",
            "#34495e": "#2c3e50",
            "#2ecc71": "#27ae60",
            "#e67e22": "#d35400"
        }
        return color_map.get(color, color)

    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        # اتصالات الأزرار
        self.new_btn.clicked.connect(self.add_new_remittance)
        self.save_btn.clicked.connect(self.save_current_remittance)
        self.approve_btn.clicked.connect(self.approve_remittance)
        self.send_to_bank_btn.clicked.connect(self.send_to_bank)
        self.bank_confirm_btn.clicked.connect(self.bank_confirmation)
        self.post_to_accounts_btn.clicked.connect(self.post_to_accounts)
        self.delete_btn.clicked.connect(self.delete_selected_remittance)
        self.refresh_btn.clicked.connect(self.refresh_data)

        # اتصالات الجدول
        self.remittances_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.remittances_table.itemDoubleClicked.connect(self.edit_selected_remittance)

        # اتصالات البحث والفلترة
        self.search_edit.textChanged.connect(self.filter_remittances)
        self.filter_combo.currentTextChanged.connect(self.filter_remittances)

        # اتصالات حساب المجاميع
        self.transfer_fees_spin.valueChanged.connect(self.calculate_totals)
        self.bank_charges_spin.valueChanged.connect(self.calculate_totals)
        self.other_charges_spin.valueChanged.connect(self.calculate_totals)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل العملات
            self.load_currencies()

            # تحميل الحوالات
            self.refresh_data()

            # تعيين رقم حوالة جديد
            self.generate_new_remittance_number()

            # تحديث العدادات
            self.update_counters()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def load_currencies(self):
        """تحميل العملات"""
        try:
            self.currency_combo.clear()
            self.currency_combo.addItems([
                "ريال سعودي (SAR)",
                "دولار أمريكي (USD)",
                "يورو (EUR)",
                "جنيه إسترليني (GBP)",
                "درهم إماراتي (AED)",
                "دينار كويتي (KWD)",
                "ين ياباني (JPY)",
                "فرنك سويسري (CHF)"
            ])
            self.currency_combo.setCurrentIndex(0)  # ريال سعودي افتراضياً
        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")

    def generate_new_remittance_number(self):
        """إنشاء رقم حوالة جديد"""
        try:
            # تنسيق: REM-YYYY-NNNN
            from datetime import datetime
            year = datetime.now().year

            # إنشاء رقم حوالة مؤقت
            new_remittance_number = f"REM-{year}-0001"
            self.remittance_number_edit.setText(new_remittance_number)

        except Exception as e:
            print(f"خطأ في إنشاء رقم الحوالة: {e}")
            self.remittance_number_edit.setText("REM-2024-0001")

    def add_new_remittance(self):
        """إضافة حوالة جديدة"""
        try:
            self.clear_form()
            self.generate_new_remittance_number()
            self.current_remittance_id = None
            self.is_editing = False
            self.selected_suppliers = []

            # تفعيل النموذج
            self.set_form_enabled(True)

            # تحديث جدول الموردين
            self.update_suppliers_table()

            QMessageBox.information(self, "حوالة جديدة", "تم إعداد نموذج حوالة جديدة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء حوالة جديدة: {str(e)}")

    def save_current_remittance(self):
        """حفظ الحوالة الحالية"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            # جمع البيانات من النموذج
            remittance_data = self.get_form_data()

            # حفظ مؤقت - سيتم ربطه بقاعدة البيانات لاحقاً
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ الحوالة بنجاح")
            self.remittance_added.emit(remittance_data)

            # تحديث الجدول
            self.refresh_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ الحوالة: {str(e)}")

    def approve_remittance(self):
        """موافقة على الحوالة"""
        try:
            current_row = self.remittances_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد حوالة للموافقة عليها")
                return

            # تحديث حالة الموافقة
            self.approval_status_checkbox.setChecked(True)
            self.approval_date_edit.setDate(QDate.currentDate())
            self.approved_by_edit.setText("مدير النظام")
            self.status_combo.setCurrentText("موافق عليها")

            QMessageBox.information(self, "موافقة", "تم الموافقة على الحوالة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الموافقة: {str(e)}")

    def send_to_bank(self):
        """إرسال الحوالة للبنك"""
        try:
            if not self.approval_status_checkbox.isChecked():
                QMessageBox.warning(self, "تحذير", "يجب الموافقة على الحوالة أولاً")
                return

            self.status_combo.setCurrentText("مرسلة للبنك")
            QMessageBox.information(self, "إرسال للبنك", "تم إرسال الحوالة للبنك بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الإرسال للبنك: {str(e)}")

    def bank_confirmation(self):
        """تأكيد من البنك"""
        try:
            if self.status_combo.currentText() != "مرسلة للبنك":
                QMessageBox.warning(self, "تحذير", "يجب إرسال الحوالة للبنك أولاً")
                return

            # فتح نافذة التأكيد البنكي
            bank_ref = f"BNK-{QDate.currentDate().toString('yyyyMMdd')}-001"
            self.bank_reference_edit.setText(bank_ref)
            self.bank_confirmation_date_edit.setDate(QDate.currentDate())
            self.status_combo.setCurrentText("مؤكدة من البنك")

            QMessageBox.information(self, "تأكيد بنكي", "تم تأكيد الحوالة من البنك بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التأكيد البنكي: {str(e)}")

    def post_to_accounts(self):
        """ترحيل الحوالة للحسابات"""
        try:
            if self.status_combo.currentText() != "مؤكدة من البنك":
                QMessageBox.warning(self, "تحذير", "يجب تأكيد الحوالة من البنك أولاً")
                return

            # تحديث حالة الموردين إلى "مرحل للحسابات"
            for row in range(self.suppliers_table.rowCount()):
                status_item = QTableWidgetItem("مرحل للحسابات")
                status_item.setBackground(Qt.green)
                self.suppliers_table.setItem(row, 5, status_item)

            self.status_combo.setCurrentText("مرحلة للحسابات")

            QMessageBox.information(self, "ترحيل للحسابات",
                                  "تم ترحيل الحوالة لحسابات الموردين بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الترحيل للحسابات: {str(e)}")

    def add_supplier_to_remittance(self):
        """إضافة مورد للحوالة"""
        try:
            # نافذة بسيطة لإضافة مورد (مؤقت)
            from PySide6.QtWidgets import QInputDialog

            supplier_name, ok = QInputDialog.getText(self, 'إضافة مورد', 'اسم المورد:')
            if ok and supplier_name:
                amount, ok = QInputDialog.getDouble(self, 'مبلغ المورد', 'المبلغ:', 0, 0, 999999, 2)
                if ok:
                    # إضافة المورد للجدول
                    row = self.suppliers_table.rowCount()
                    self.suppliers_table.setRowCount(row + 1)

                    self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier_name))
                    self.suppliers_table.setItem(row, 1, QTableWidgetItem(f"SUP-{row+1:03d}"))
                    self.suppliers_table.setItem(row, 2, QTableWidgetItem(f"{amount:.2f}"))
                    self.suppliers_table.setItem(row, 3, QTableWidgetItem("ريال سعودي"))
                    self.suppliers_table.setItem(row, 4, QTableWidgetItem("سيتم تحديده"))
                    self.suppliers_table.setItem(row, 5, QTableWidgetItem("في الانتظار"))

                    # تحديث المجاميع
                    self.calculate_totals()
                    self.update_counters()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المورد: {str(e)}")

    def remove_supplier_from_remittance(self):
        """إزالة مورد من الحوالة"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مورد للإزالة")
                return

            reply = QMessageBox.question(self, 'تأكيد الإزالة',
                                       'هل أنت متأكد من إزالة هذا المورد؟',
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)

            if reply == QMessageBox.Yes:
                self.suppliers_table.removeRow(current_row)
                self.calculate_totals()
                self.update_counters()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إزالة المورد: {str(e)}")

    def edit_supplier_amount(self):
        """تعديل مبلغ المورد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مورد لتعديل مبلغه")
                return

            from PySide6.QtWidgets import QInputDialog

            current_amount = float(self.suppliers_table.item(current_row, 2).text())
            new_amount, ok = QInputDialog.getDouble(self, 'تعديل المبلغ', 'المبلغ الجديد:',
                                                  current_amount, 0, 999999, 2)
            if ok:
                self.suppliers_table.setItem(current_row, 2, QTableWidgetItem(f"{new_amount:.2f}"))
                self.calculate_totals()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المبلغ: {str(e)}")

    def calculate_totals(self):
        """حساب المجاميع"""
        try:
            # حساب إجمالي مبالغ الموردين
            total_suppliers_amount = 0
            for row in range(self.suppliers_table.rowCount()):
                amount_item = self.suppliers_table.item(row, 2)
                if amount_item:
                    total_suppliers_amount += float(amount_item.text())

            # حساب إجمالي الرسوم
            total_charges = (self.transfer_fees_spin.value() +
                           self.bank_charges_spin.value() +
                           self.other_charges_spin.value())

            # تحديث الحقول
            self.total_amount_edit.setText(f"{total_suppliers_amount:.2f}")
            self.total_charges_edit.setText(f"{total_charges:.2f}")

        except Exception as e:
            print(f"خطأ في حساب المجاميع: {e}")

    def update_counters(self):
        """تحديث العدادات"""
        try:
            # تحديث عدد الموردين
            suppliers_count = self.suppliers_table.rowCount()
            self.suppliers_count_edit.setText(str(suppliers_count))

            # تحديث عداد الحوالات
            remittances_count = self.remittances_table.rowCount()
            self.remittances_count_label.setText(f"العدد: {remittances_count}")

        except Exception as e:
            print(f"خطأ في تحديث العدادات: {e}")

    def update_suppliers_table(self):
        """تحديث جدول الموردين"""
        self.suppliers_table.setRowCount(0)

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # إضافة بيانات تجريبية للاختبار
            self.remittances_table.setRowCount(5)

            # حوالة تجريبية 1
            self.remittances_table.setItem(0, 0, QTableWidgetItem("REM-2024-0001"))
            self.remittances_table.setItem(0, 1, QTableWidgetItem("2024-01-15"))
            self.remittances_table.setItem(0, 2, QTableWidgetItem("15000.00"))
            self.remittances_table.setItem(0, 3, QTableWidgetItem("ريال سعودي"))
            self.remittances_table.setItem(0, 4, QTableWidgetItem("3"))
            self.remittances_table.setItem(0, 5, QTableWidgetItem("مؤكدة من البنك"))
            self.remittances_table.setItem(0, 6, QTableWidgetItem("HSBC Bank"))
            self.remittances_table.setItem(0, 7, QTableWidgetItem("موافق عليها"))
            self.remittances_table.setItem(0, 8, QTableWidgetItem("2024-01-16"))
            self.remittances_table.setItem(0, 9, QTableWidgetItem("دفع مستحقات الموردين"))

            # حوالة تجريبية 2
            self.remittances_table.setItem(1, 0, QTableWidgetItem("REM-2024-0002"))
            self.remittances_table.setItem(1, 1, QTableWidgetItem("2024-01-16"))
            self.remittances_table.setItem(1, 2, QTableWidgetItem("8500.00"))
            self.remittances_table.setItem(1, 3, QTableWidgetItem("دولار أمريكي"))
            self.remittances_table.setItem(1, 4, QTableWidgetItem("2"))
            self.remittances_table.setItem(1, 5, QTableWidgetItem("مرسلة للبنك"))
            self.remittances_table.setItem(1, 6, QTableWidgetItem("ANB Bank"))
            self.remittances_table.setItem(1, 7, QTableWidgetItem("موافق عليها"))
            self.remittances_table.setItem(1, 8, QTableWidgetItem("-"))
            self.remittances_table.setItem(1, 9, QTableWidgetItem("تحويل عاجل"))

            # حوالة تجريبية 3
            self.remittances_table.setItem(2, 0, QTableWidgetItem("REM-2024-0003"))
            self.remittances_table.setItem(2, 1, QTableWidgetItem("2024-01-17"))
            self.remittances_table.setItem(2, 2, QTableWidgetItem("22000.00"))
            self.remittances_table.setItem(2, 3, QTableWidgetItem("يورو"))
            self.remittances_table.setItem(2, 4, QTableWidgetItem("4"))
            self.remittances_table.setItem(2, 5, QTableWidgetItem("مرحلة للحسابات"))
            self.remittances_table.setItem(2, 6, QTableWidgetItem("Deutsche Bank"))
            self.remittances_table.setItem(2, 7, QTableWidgetItem("موافق عليها"))
            self.remittances_table.setItem(2, 8, QTableWidgetItem("2024-01-18"))
            self.remittances_table.setItem(2, 9, QTableWidgetItem("دفعة شهرية"))

            # حوالة تجريبية 4
            self.remittances_table.setItem(3, 0, QTableWidgetItem("REM-2024-0004"))
            self.remittances_table.setItem(3, 1, QTableWidgetItem("2024-01-18"))
            self.remittances_table.setItem(3, 2, QTableWidgetItem("5200.00"))
            self.remittances_table.setItem(3, 3, QTableWidgetItem("ريال سعودي"))
            self.remittances_table.setItem(3, 4, QTableWidgetItem("1"))
            self.remittances_table.setItem(3, 5, QTableWidgetItem("في انتظار الموافقة"))
            self.remittances_table.setItem(3, 6, QTableWidgetItem("NCB Bank"))
            self.remittances_table.setItem(3, 7, QTableWidgetItem("في الانتظار"))
            self.remittances_table.setItem(3, 8, QTableWidgetItem("-"))
            self.remittances_table.setItem(3, 9, QTableWidgetItem("دفع فاتورة"))

            # حوالة تجريبية 5
            self.remittances_table.setItem(4, 0, QTableWidgetItem("REM-2024-0005"))
            self.remittances_table.setItem(4, 1, QTableWidgetItem("2024-01-19"))
            self.remittances_table.setItem(4, 2, QTableWidgetItem("12800.00"))
            self.remittances_table.setItem(4, 3, QTableWidgetItem("جنيه إسترليني"))
            self.remittances_table.setItem(4, 4, QTableWidgetItem("2"))
            self.remittances_table.setItem(4, 5, QTableWidgetItem("مسودة"))
            self.remittances_table.setItem(4, 6, QTableWidgetItem("Barclays Bank"))
            self.remittances_table.setItem(4, 7, QTableWidgetItem("غير موافق"))
            self.remittances_table.setItem(4, 8, QTableWidgetItem("-"))
            self.remittances_table.setItem(4, 9, QTableWidgetItem("قيد الإعداد"))

            # تحديث العدادات
            self.update_counters()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def filter_remittances(self):
        """فلترة الحوالات"""
        try:
            search_text = self.search_edit.text().lower()
            filter_status = self.filter_combo.currentText()

            for row in range(self.remittances_table.rowCount()):
                show_row = True

                # فلترة بالبحث
                if search_text:
                    row_text = ""
                    for col in range(self.remittances_table.columnCount()):
                        item = self.remittances_table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "

                    if search_text not in row_text:
                        show_row = False

                # فلترة بالحالة
                if filter_status != "جميع الحوالات":
                    status_item = self.remittances_table.item(row, 5)
                    if status_item:
                        status_text = status_item.text()
                        if filter_status == "مسودة" and "مسودة" not in status_text:
                            show_row = False
                        elif filter_status == "في الانتظار" and "انتظار" not in status_text:
                            show_row = False
                        elif filter_status == "مؤكدة" and "مؤكدة" not in status_text:
                            show_row = False
                        elif filter_status == "مرحلة" and "مرحلة" not in status_text:
                            show_row = False

                self.remittances_table.setRowHidden(row, not show_row)

        except Exception as e:
            print(f"خطأ في الفلترة: {e}")

    def validate_form(self):
        """التحقق من صحة بيانات النموذج"""
        if not self.remittance_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "رقم الحوالة مطلوب")
            return False

        if self.suppliers_table.rowCount() == 0:
            QMessageBox.warning(self, "خطأ في البيانات", "يجب إضافة مورد واحد على الأقل")
            return False

        if not self.sender_bank_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "البنك المرسل مطلوب")
            return False

        return True

    def get_form_data(self):
        """الحصول على بيانات النموذج"""
        return {
            'remittance_number': self.remittance_number_edit.text().strip(),
            'remittance_date': self.remittance_date_edit.date().toPython(),
            'currency': self.currency_combo.currentText(),
            'exchange_rate': self.exchange_rate_spin.value(),
            'total_amount': float(self.total_amount_edit.text() or "0"),
            'suppliers_count': int(self.suppliers_count_edit.text() or "0"),
            'sender_bank': self.sender_bank_edit.text().strip(),
            'sender_account': self.sender_account_edit.text().strip(),
            'receiver_bank': self.receiver_bank_edit.text().strip(),
            'receiver_country': self.receiver_country_edit.text().strip(),
            'swift_code': self.swift_code_edit.text().strip(),
            'transfer_fees': self.transfer_fees_spin.value(),
            'bank_charges': self.bank_charges_spin.value(),
            'other_charges': self.other_charges_spin.value(),
            'purpose': self.purpose_edit.toPlainText().strip(),
            'notes': self.notes_edit.toPlainText().strip(),
            'status': self.status_combo.currentText(),
            'is_approved': self.approval_status_checkbox.isChecked(),
            'approved_by': self.approved_by_edit.text().strip(),
            'bank_reference': self.bank_reference_edit.text().strip()
        }

    def clear_form(self):
        """مسح النموذج"""
        self.remittance_number_edit.clear()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.currency_combo.setCurrentIndex(0)
        self.exchange_rate_spin.setValue(1.0)
        self.total_amount_edit.clear()
        self.suppliers_count_edit.clear()
        self.sender_bank_edit.clear()
        self.sender_account_edit.clear()
        self.receiver_bank_edit.clear()
        self.receiver_country_edit.clear()
        self.swift_code_edit.clear()
        self.transfer_fees_spin.setValue(0.0)
        self.bank_charges_spin.setValue(0.0)
        self.other_charges_spin.setValue(0.0)
        self.total_charges_edit.clear()
        self.purpose_edit.clear()
        self.notes_edit.clear()
        self.status_combo.setCurrentIndex(0)
        self.approval_status_checkbox.setChecked(False)
        self.approved_by_edit.clear()
        self.bank_reference_edit.clear()

        self.current_remittance_id = None
        self.is_editing = False
        self.selected_suppliers = []

        # مسح جدول الموردين
        self.suppliers_table.setRowCount(0)

    def set_form_enabled(self, enabled):
        """تفعيل/تعطيل النموذج"""
        # المعلومات الأساسية (عدا رقم الحوالة)
        self.remittance_date_edit.setEnabled(enabled)
        self.currency_combo.setEnabled(enabled)
        self.exchange_rate_spin.setEnabled(enabled)

        # معلومات البنوك
        self.sender_bank_edit.setEnabled(enabled)
        self.sender_account_edit.setEnabled(enabled)
        self.receiver_bank_edit.setEnabled(enabled)
        self.receiver_country_edit.setEnabled(enabled)
        self.swift_code_edit.setEnabled(enabled)

        # الرسوم والمعلومات الإضافية
        self.transfer_fees_spin.setEnabled(enabled)
        self.bank_charges_spin.setEnabled(enabled)
        self.other_charges_spin.setEnabled(enabled)
        self.purpose_edit.setEnabled(enabled)
        self.notes_edit.setEnabled(enabled)

        # الأزرار
        self.save_btn.setEnabled(enabled)

    def on_selection_changed(self):
        """التعامل مع تغيير التحديد في الجدول"""
        current_row = self.remittances_table.currentRow()
        has_selection = current_row >= 0

        # تفعيل/تعطيل الأزرار
        self.approve_btn.setEnabled(has_selection)
        self.send_to_bank_btn.setEnabled(has_selection)
        self.bank_confirm_btn.setEnabled(has_selection)
        self.post_to_accounts_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

        # إرسال إشارة التحديد
        if has_selection:
            remittance_data = self.get_selected_remittance_data()
            self.remittance_selected.emit(remittance_data)

    def get_selected_remittance_data(self):
        """الحصول على بيانات الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row >= 0:
            number_item = self.remittances_table.item(current_row, 0)
            return {'number': number_item.text() if number_item else ''}
        return {}

    def edit_selected_remittance(self):
        """تعديل الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row >= 0:
            number_item = self.remittances_table.item(current_row, 0)
            if number_item:
                QMessageBox.information(self, "تعديل", f"تم تحديد الحوالة: {number_item.text()}")

    def delete_selected_remittance(self):
        """حذف الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حوالة للحذف")
            return

        reply = QMessageBox.question(self, 'تأكيد الحذف',
                                   'هل أنت متأكد من حذف هذه الحوالة؟\nلا يمكن التراجع عن هذا الإجراء.',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.remittances_table.removeRow(current_row)
            self.update_counters()
            QMessageBox.information(self, "نجح الحذف", "تم حذف الحوالة بنجاح")
