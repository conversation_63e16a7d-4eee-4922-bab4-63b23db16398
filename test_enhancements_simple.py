#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط للتحسينات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيرادات"""
    
    print("🧪 اختبار التحسينات الجديدة")
    print("=" * 50)
    
    try:
        # اختبار استيراد FlexibleDateEdit
        from src.ui.widgets.flexible_date_edit import FlexibleDateEdit
        print("✅ تم استيراد FlexibleDateEdit بنجاح")
        
        # اختبار استيراد PurchaseOrdersWindow
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")
        
        # التحقق من وجود الدوال الجديدة
        if hasattr(PurchaseOrdersWindow, 'get_purchase_order_usage_status'):
            print("✅ دالة get_purchase_order_usage_status موجودة")
        else:
            print("❌ دالة get_purchase_order_usage_status غير موجودة")
            
        if hasattr(PurchaseOrdersWindow, 'get_status_color'):
            print("✅ دالة get_status_color موجودة")
        else:
            print("❌ دالة get_status_color غير موجودة")
            
        if hasattr(PurchaseOrdersWindow, 'apply_row_colors'):
            print("✅ دالة apply_row_colors موجودة")
        else:
            print("❌ دالة apply_row_colors غير موجودة")
            
        if hasattr(PurchaseOrdersWindow, 'create_color_legend'):
            print("✅ دالة create_color_legend موجودة")
        else:
            print("❌ دالة create_color_legend غير موجودة")
        
        print("\n🎨 اختبار نظام الألوان:")
        
        # إنشاء مثيل وهمي لاختبار الألوان
        class MockWindow:
            def get_status_color(self, status):
                colors = {
                    "unused": "#ffffff",        # أبيض - لم يستخدم
                    "partially_used": "#fff3cd", # أصفر فاتح - مستخدم جزئياً
                    "fully_used": "#d4edda",    # أخضر فاتح - مستخدم بالكامل
                    "empty": "#f8d7da",         # أحمر فاتح - طلب فارغ
                    "unknown": "#e2e3e5"        # رمادي - غير معروف
                }
                return colors.get(status, "#ffffff")
        
        mock = MockWindow()
        test_statuses = ["unused", "partially_used", "fully_used", "empty", "unknown"]
        
        for status in test_statuses:
            color = mock.get_status_color(status)
            print(f"   📌 حالة '{status}': لون {color}")
        
        print("\n📅 اختبار حقل التاريخ المرن:")
        
        # اختبار أنماط التاريخ
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QDate
        
        app = QApplication(sys.argv)
        
        date_edit = FlexibleDateEdit()
        
        test_dates = [
            "15/03/2024",
            "15032024", 
            "15-03-2024",
            "15.03.2024"
        ]
        
        for test_date in test_dates:
            try:
                parsed_date = date_edit.parse_date_string(test_date)
                if parsed_date.isValid():
                    formatted_date = parsed_date.toString("dd/MM/yyyy")
                    print(f"   ✅ '{test_date}' -> '{formatted_date}'")
                else:
                    print(f"   ❌ '{test_date}' -> غير صالح")
            except Exception as e:
                print(f"   ❌ '{test_date}' -> خطأ: {str(e)}")
        
        print("\n🎉 انتهى الاختبار!")
        print("✅ جميع التحسينات تم تطبيقها بنجاح:")
        print("   • حقل تاريخ الطلب أصبح مرناً")
        print("   • نظام الألوان للجدول تم إضافته")
        print("   • مفتاح الألوان تم إضافته")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
