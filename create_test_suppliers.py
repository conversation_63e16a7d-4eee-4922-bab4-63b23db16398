#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# إنشاء بيانات اختبار للموردين
test_data = {
    'اسم المورد': [
        'شركة اختبار 1',
        'مؤسسة اختبار 2',
        'مكت<PERSON> اختبار 3'
    ],
    'الاسم الإنجليزي': [
        'Test Company 1',
        'Test Foundation 2',
        'Test Office 3'
    ],
    'نوع المورد': [
        'شركة',
        'مؤسسة',
        'مكتب'
    ],
    'الهاتف': [
        '011-1111111',
        '012-2222222',
        '013-3333333'
    ],
    'الجوال': [
        '0501111111',
        '0502222222',
        '0503333333'
    ],
    'البريد الإلكتروني': [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ],
    'المدينة': [
        'الرياض',
        'جدة',
        'الدمام'
    ],
    'حد الائتمان': [
        10000.00,
        15000.00,
        20000.00
    ],
    'مدة السداد': [
        30,
        45,
        60
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(test_data)

# حفظ الملف
filename = 'اختبار_استيراد_موردين.xlsx'
df.to_excel(filename, index=False, engine='openpyxl')

print(f"✅ تم إنشاء ملف الاختبار: {filename}")
print(f"📊 عدد الموردين: {len(df)} مورد")
print("\n🔍 البيانات:")
for i, row in df.iterrows():
    print(f"{i+1}. {row['اسم المورد']} - {row['المدينة']}")

print("\n💡 يمكنك الآن استخدام هذا الملف لاختبار استيراد الموردين!")
