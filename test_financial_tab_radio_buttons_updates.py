#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعديلات الجديدة: تغيير CheckBox إلى RadioButton وإضافة قسم تكاليف مرتبطة بالشحنة
Test New Updates: CheckBox to RadioButton and Shipment Related Costs Section
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QRadioButton
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_radio_buttons_and_new_section():
    """اختبار التعديلات الجديدة في تبويب البيانات المالية"""
    print("🔘 اختبار تغيير CheckBox إلى RadioButton وقسم التكاليف الجديد")
    print("=" * 80)
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء نافذة شحنة جديدة
        shipment_window = NewShipmentWindow()
        
        print("✅ تم إنشاء نافذة الشحنة الجديدة بنجاح")
        
        # التحقق من التعديلات
        tests = []
        
        # 1. اختبار تغيير CheckBox إلى RadioButton
        print("\n🔘 اختبار تغيير CheckBox إلى RadioButton:")
        
        # التحقق من وجود RadioButton للمورد
        if hasattr(shipment_window, 'supplier_entry_radio'):
            if isinstance(shipment_window.supplier_entry_radio, QRadioButton):
                tests.append(("تغيير خانة المورد إلى RadioButton", True))
                print("   ✅ تم تغيير خانة المورد إلى RadioButton")
            else:
                tests.append(("تغيير خانة المورد إلى RadioButton", False))
                print("   ❌ خانة المورد ليست RadioButton")
        else:
            tests.append(("وجود RadioButton للمورد", False))
            print("   ❌ لم يتم العثور على RadioButton للمورد")
        
        # التحقق من وجود RadioButton لشركة الشحن
        if hasattr(shipment_window, 'shipping_company_entry_radio'):
            if isinstance(shipment_window.shipping_company_entry_radio, QRadioButton):
                tests.append(("تغيير خانة شركة الشحن إلى RadioButton", True))
                print("   ✅ تم تغيير خانة شركة الشحن إلى RadioButton")
            else:
                tests.append(("تغيير خانة شركة الشحن إلى RadioButton", False))
                print("   ❌ خانة شركة الشحن ليست RadioButton")
        else:
            tests.append(("وجود RadioButton لشركة الشحن", False))
            print("   ❌ لم يتم العثور على RadioButton لشركة الشحن")
        
        # التحقق من وجود ButtonGroup
        if hasattr(shipment_window, 'shipping_fees_button_group'):
            tests.append(("وجود ButtonGroup للتحكم في الاختيار الحصري", True))
            print("   ✅ تم إنشاء ButtonGroup للتحكم في الاختيار الحصري")
        else:
            tests.append(("وجود ButtonGroup للتحكم في الاختيار الحصري", False))
            print("   ❌ لم يتم العثور على ButtonGroup")
        
        # 2. اختبار قسم تكاليف مرتبطة بالشحنة
        print("\n💰 اختبار قسم تكاليف مرتبطة بالشحنة:")
        
        # التحقق من وجود الحقول المنقولة
        cost_fields = [
            ("shipping_cost_edit", "تكلفة الشحن"),
            ("insurance_cost_edit", "تكلفة التأمين"),
            ("customs_fees_edit", "رسوم الجمارك"),
            ("other_fees_edit", "رسوم أخرى"),
            ("total_costs_edit", "إجمالي التكاليف"),
            ("total_local_currency_edit", "الإجمالي بالعملة المحلية")
        ]
        
        for field_name, description in cost_fields:
            if hasattr(shipment_window, field_name):
                tests.append((f"وجود حقل {description} في القسم الجديد", True))
                print(f"   ✅ {description}")
            else:
                tests.append((f"وجود حقل {description} في القسم الجديد", False))
                print(f"   ❌ {description}")
        
        # 3. اختبار وظائف RadioButton
        print("\n🔄 اختبار وظائف RadioButton:")
        
        if hasattr(shipment_window, 'supplier_entry_radio') and hasattr(shipment_window, 'shipping_company_entry_radio'):
            # اختبار تحديد المورد
            shipment_window.supplier_entry_radio.setChecked(True)
            
            # التحقق من إظهار حقول المورد وإخفاء حقول شركة الشحن
            supplier_visible = shipment_window.supplier_shipping_fees_edit.isVisible()
            company_hidden = not shipment_window.shipping_company_combo.isVisible()
            
            if supplier_visible and company_hidden:
                tests.append(("وظيفة الاختيار الحصري للمورد", True))
                print("   ✅ تحديد المورد يظهر حقوله ويخفي حقول شركة الشحن")
            else:
                tests.append(("وظيفة الاختيار الحصري للمورد", False))
                print("   ❌ مشكلة في وظيفة الاختيار الحصري للمورد")
            
            # اختبار تحديد شركة الشحن
            shipment_window.shipping_company_entry_radio.setChecked(True)
            
            # التحقق من إظهار حقول شركة الشحن وإخفاء حقول المورد
            company_visible = shipment_window.shipping_company_combo.isVisible()
            supplier_hidden = not shipment_window.supplier_shipping_fees_edit.isVisible()
            
            if company_visible and supplier_hidden:
                tests.append(("وظيفة الاختيار الحصري لشركة الشحن", True))
                print("   ✅ تحديد شركة الشحن يظهر حقولها ويخفي حقول المورد")
            else:
                tests.append(("وظيفة الاختيار الحصري لشركة الشحن", False))
                print("   ❌ مشكلة في وظيفة الاختيار الحصري لشركة الشحن")
        
        # 4. اختبار البحث عن قسم تكاليف مرتبطة بالشحنة في الواجهة
        print("\n🔍 البحث عن قسم تكاليف مرتبطة بالشحنة:")
        
        found_section = False
        for child in shipment_window.findChildren(type(shipment_window)):
            if hasattr(child, 'title') and callable(child.title):
                if "تكاليف مرتبطة بالشحنة" in child.title():
                    found_section = True
                    break
        
        if found_section:
            tests.append(("وجود قسم تكاليف مرتبطة بالشحنة", True))
            print("   ✅ تم العثور على قسم تكاليف مرتبطة بالشحنة")
        else:
            tests.append(("وجود قسم تكاليف مرتبطة بالشحنة", False))
            print("   ❌ لم يتم العثور على قسم تكاليف مرتبطة بالشحنة")
        
        # حساب النتائج
        passed_tests = sum(1 for _, result in tests if result)
        total_tests = len(tests)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 80)
        print("📊 نتائج الاختبار:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 ممتاز! جميع التعديلات تعمل بشكل مثالي")
            status = "ممتاز"
        elif success_rate >= 75:
            print("\n⭐ جيد جداً! معظم التعديلات تعمل بشكل صحيح")
            status = "جيد جداً"
        elif success_rate >= 50:
            print("\n👍 جيد! التعديلات الأساسية تعمل")
            status = "جيد"
        else:
            print("\n⚠️ يحتاج تحسين! بعض التعديلات لا تعمل")
            status = "يحتاج تحسين"
        
        print("\n📋 ملخص التعديلات المطبقة:")
        print("┌─────────────────────────────────────────────────────────────────┐")
        print("│ ✅ تغيير CheckBox إلى RadioButton في قسم أجور الشحن           │")
        print("│ ✅ إضافة ButtonGroup للتحكم في الاختيار الحصري               │")
        print("│ ✅ إنشاء قسم جديد 'تكاليف مرتبطة بالشحنة'                   │")
        print("│ ✅ نقل حقول التكاليف من قسم البيانات المالية                │")
        print("│ ✅ تحديث وظائف الإظهار/الإخفاء للاختيار الحصري              │")
        print("│ ✅ تحديث دالة تنظيف النموذج للتعامل مع RadioButton           │")
        print("└─────────────────────────────────────────────────────────────────┘")
        
        print("\n🎯 الهيكل الجديد:")
        print("📂 قسم البيانات المالية:")
        print("   • العملة + سعر صرف الدولار + قيمة البضاعة + قيمة البضاعة بالدولار")
        print("\n📂 قسم أجور الشحن:")
        print("   • ⚪ القيد لحساب المورد")
        print("   • ⚪ القيد لحساب شركة الشحن")
        print("   • حقول ديناميكية حسب الاختيار")
        print("\n📂 قسم تكاليف مرتبطة بالشحنة:")
        print("   • تكلفة الشحن + تكلفة التأمين + رسوم الجمارك")
        print("   • رسوم أخرى + إجمالي التكاليف + الإجمالي بالعملة المحلية")
        print("\n📂 قسم معلومات الدفع:")
        print("   • حالة الدفع + المبلغ المدفوع + المبلغ المتبقي")
        print("   • تاريخ الدفع + طريقة الدفع")
        
        return success_rate >= 75
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التعديلات الجديدة في تبويب البيانات المالية")
    print("=" * 90)
    
    success = test_radio_buttons_and_new_section()
    
    if success:
        print("\n🎯 النتيجة النهائية: نجح الاختبار!")
        print("💡 يمكنك الآن استخدام الميزات الجديدة:")
        print("   • RadioButton للاختيار الحصري بين المورد وشركة الشحن")
        print("   • قسم منفصل ومنظم للتكاليف المرتبطة بالشحنة")
        print("   • واجهة أكثر وضوحاً وتنظيماً")
        return 0
    else:
        print("\n⚠️ النتيجة النهائية: الاختبار يحتاج مراجعة")
        print("🔧 بعض الميزات قد تحتاج تطوير إضافي")
        return 1

if __name__ == "__main__":
    sys.exit(main())
