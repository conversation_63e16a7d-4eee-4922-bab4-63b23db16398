#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة استيراد الإكسيل المتقدمة في شاشة الشحنة الجديدة
تدعم الآن الاستيراد الفردي والمتعدد مع دعم الحاويات المتعددة
"""

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_excel_import():
    """اختبار ميزة استيراد الإكسيل المتقدمة"""
    try:
        print("🚀 بدء اختبار ميزة استيراد الإكسيل المتقدمة")
        print("=" * 60)
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # استيراد النافذة
        from ui.shipments.new_shipment_window import NewShipmentWindow
        
        # إنشاء النافذة
        window = NewShipmentWindow()
        window.show()
        
        print("✅ تم فتح نافذة الشحنة الجديدة بنجاح!")
        print("\n🎯 الميزات الجديدة المتاحة:")
        print("   📊 زر 'استيراد إكسيل' محسن في شريط الأدوات")
        print("   🔄 خيار الاستيراد الفردي (الصف الأول فقط)")
        print("   📈 خيار الاستيراد المتعدد (جميع الأسطر)")
        print("   🏗️ إنشاء شحنات متعددة في قاعدة البيانات")
        print("   📦 دعم الحاويات المتعددة بفواصل مختلفة")
        print("   🎨 واجهة اختيار نوع الاستيراد المحسنة")
        print("   ⚡ معالجة أخطاء متقدمة وتقارير مفصلة")
        
        print("\n📋 الحقول المدعومة في الاستيراد:")
        supported_fields = [
            "التاريخ", "المورد", "بوليصة الشحن", "ملاحظات",
            "حالة الشحنة", "حالة الإفراج", "شركة الشحن", "رقم DHL",
            "ميناء الوصول", "تاريخ الوصول المتوقع", "رقم الحاوية (دعم متعدد)"
        ]
        for field in supported_fields:
            print(f"   • {field}")
        
        print("\n🧪 ملفات الاختبار المتاحة:")
        if os.path.exists('multiple_shipments_sample.xlsx'):
            print("   ✅ multiple_shipments_sample.xlsx (6 شحنات متنوعة)")
            print("      📦 تحتوي على حاويات متعددة بفواصل مختلفة")
            print("      🏢 موردين من دول مختلفة")
            print("      📅 تواريخ متنوعة")
        else:
            print("   ❌ multiple_shipments_sample.xlsx (غير موجود)")
        
        print("\n🔧 خطوات الاختبار المفصلة:")
        print("   1️⃣ انقر على زر 'استيراد إكسيل' 📊")
        print("   2️⃣ اختر ملف multiple_shipments_sample.xlsx")
        print("   3️⃣ ستظهر نافذة خيارات الاستيراد:")
        print("      🔹 معلومات الملف (عدد الأسطر والأعمدة)")
        print("      🔹 خيار الاستيراد الفردي")
        print("      🔹 خيار الاستيراد المتعدد مع تحذير")
        print("   4️⃣ اختر نوع الاستيراد:")
        print("      • فردي: يستورد الصف الأول فقط إلى النافذة الحالية")
        print("      • متعدد: ينشئ 6 شحنات جديدة في قاعدة البيانات")
        print("   5️⃣ راقب شريط التقدم ورسائل النجاح")
        print("   6️⃣ تحقق من النتائج في قاعدة البيانات")
        
        print("\n⚠️ تحذيرات مهمة:")
        warnings = [
            "الاستيراد المتعدد ينشئ شحنات دائمة في قاعدة البيانات",
            "تأكد من وجود نسخة احتياطية من قاعدة البيانات",
            "يتم إنشاء موردين جدد تلقائياً إذا لم يوجدوا",
            "أرقام الشحنات تُولد تلقائياً بتنسيق SH-YYYY-NNNN",
            "الحاويات المتعددة تُحلل تلقائياً من نص واحد"
        ]
        for warning in warnings:
            print(f"   ⚠️ {warning}")
        
        print("\n🎉 النافذة جاهزة للاختبار!")
        print("💡 استخدم Ctrl+C للخروج من الاختبار")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود جميع الملفات المطلوبة")
        return False
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    requirements = {
        'pandas': 'مكتبة pandas لقراءة ملفات الإكسيل',
        'openpyxl': 'مكتبة openpyxl لدعم ملفات .xlsx',
        'PySide6': 'مكتبة PySide6 للواجهة الرسومية'
    }
    
    missing = []
    
    for package, description in requirements.items():
        try:
            __import__(package)
            print(f"   ✅ {package}: متوفر")
        except ImportError:
            print(f"   ❌ {package}: غير متوفر - {description}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
        print("💡 لتثبيت المكتبات المفقودة:")
        print(f"   pip install {' '.join(missing)}")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True

def show_feature_summary():
    """عرض ملخص الميزات الجديدة"""
    print("\n📊 ملخص الميزات الجديدة في استيراد الإكسيل:")
    print("=" * 60)
    
    features = [
        {
            "title": "🎯 اختيار نوع الاستيراد",
            "description": "نافذة حوار لاختيار الاستيراد الفردي أو المتعدد"
        },
        {
            "title": "📦 دعم الحاويات المتعددة",
            "description": "تحليل تلقائي للحاويات المفصولة بـ: , ; | / -"
        },
        {
            "title": "🏗️ إنشاء شحنات متعددة",
            "description": "إنشاء شحنة منفصلة لكل صف في ملف الإكسيل"
        },
        {
            "title": "🔄 توليد أرقام تلقائي",
            "description": "توليد أرقام شحنات فريدة بتنسيق SH-YYYY-NNNN"
        },
        {
            "title": "🏢 إدارة الموردين",
            "description": "إنشاء موردين جدد تلقائياً إذا لم يوجدوا"
        },
        {
            "title": "📅 معالجة التواريخ",
            "description": "تحويل تلقائي لتنسيقات التواريخ المختلفة"
        },
        {
            "title": "⚡ معالجة الأخطاء",
            "description": "تقارير مفصلة عن الأخطاء والتحذيرات"
        },
        {
            "title": "📊 شريط التقدم",
            "description": "عرض تقدم العملية للاستيراد المتعدد"
        }
    ]
    
    for feature in features:
        print(f"\n{feature['title']}")
        print(f"   {feature['description']}")
    
    print("\n" + "=" * 60)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار ميزة استيراد الإكسيل المتقدمة")
    print("=" * 60)
    
    # عرض ملخص الميزات
    show_feature_summary()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ لا يمكن متابعة الاختبار بدون المكتبات المطلوبة")
        return
    
    # فحص ملفات الاختبار
    print("\n📁 فحص ملفات الاختبار...")
    if not os.path.exists('multiple_shipments_sample.xlsx'):
        print("⚠️ ملف الاختبار غير موجود")
        print("💡 شغل test_multiple_shipments_import.py أولاً لإنشاء ملف الاختبار")
        
        try:
            print("🔄 محاولة إنشاء ملف الاختبار...")
            import subprocess
            result = subprocess.run([sys.executable, 'test_multiple_shipments_import.py'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print("✅ تم إنشاء ملف الاختبار بنجاح!")
            else:
                print(f"❌ فشل في إنشاء ملف الاختبار: {result.stderr}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف الاختبار: {e}")
    else:
        print("✅ ملف الاختبار موجود")
    
    # بدء الاختبار
    print("\n🚀 بدء اختبار الميزة...")
    test_excel_import()

if __name__ == "__main__":
    main()
