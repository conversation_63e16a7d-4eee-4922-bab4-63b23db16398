#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحميل بيانات الشحنة بدون واجهة المستخدم
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment

def test_shipment_data_loading():
    """اختبار تحميل بيانات الشحنة"""
    try:
        print("=== اختبار تحميل بيانات الشحنة ===")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن شحنة للاختبار
        shipment = session.query(Shipment).first()
        if not shipment:
            print("❌ لا توجد شحنات للاختبار")
            return False
        
        print(f"📦 اختبار الشحنة: {shipment.shipment_number}")
        print(f"   - معرف الشحنة: {shipment.id}")
        
        # اختبار الحقول الأساسية
        print("\n📋 الحقول الأساسية:")
        print(f"   - رقم الشحنة: {shipment.shipment_number}")
        print(f"   - معرف المورد: {shipment.supplier_id}")
        print(f"   - حالة الشحنة: {shipment.shipment_status}")
        print(f"   - حالة الإفراج: {shipment.clearance_status}")
        
        # اختبار الحقول الجديدة
        print("\n🆕 الحقول الجديدة:")
        try:
            shipping_policy = shipment.shipping_policy
            container_number = shipment.container_number
            print(f"   - بوليصة الشحن: {shipping_policy}")
            print(f"   - رقم الحاوية: {container_number}")
            print("   ✅ الحقول الجديدة متاحة")
        except AttributeError as e:
            print(f"   ❌ خطأ في الوصول للحقول الجديدة: {e}")
        
        # اختبار التواريخ
        print("\n📅 التواريخ:")
        print(f"   - تاريخ المغادرة المتوقع: {shipment.estimated_departure_date}")
        print(f"   - تاريخ المغادرة الفعلي: {shipment.actual_departure_date}")
        print(f"   - تاريخ الوصول المتوقع: {shipment.estimated_arrival_date}")
        print(f"   - تاريخ الإنشاء: {shipment.created_at}")
        
        # اختبار المورد
        print("\n🏢 بيانات المورد:")
        if shipment.supplier:
            print(f"   - اسم المورد: {shipment.supplier.name}")
            print(f"   - كود المورد: {shipment.supplier.code}")
        else:
            print("   - لا يوجد مورد مرتبط")
        
        # اختبار الحقول الأخرى
        print("\n📝 حقول أخرى:")
        print(f"   - ملاحظات: {shipment.notes}")
        print(f"   - رقم التتبع: {shipment.tracking_number}")
        print(f"   - بوليصة الشحن: {shipment.bill_of_lading}")
        print(f"   - شركة الشحن: {shipment.shipping_company}")
        print(f"   - رقم فاتورة المورد: {shipment.supplier_invoice_number}")
        
        session.close()
        
        print("\n✅ تم اختبار تحميل البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحميل البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_date_handling():
    """اختبار معالجة التواريخ"""
    try:
        print("\n=== اختبار معالجة التواريخ ===")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        shipment = session.query(Shipment).first()
        if not shipment:
            return False
        
        # محاكاة منطق تحميل التاريخ من الكود
        date_to_use = None
        if shipment.estimated_departure_date:
            date_to_use = shipment.estimated_departure_date
            print(f"✅ استخدام تاريخ المغادرة المتوقع: {date_to_use}")
        elif shipment.actual_departure_date:
            date_to_use = shipment.actual_departure_date
            print(f"✅ استخدام تاريخ المغادرة الفعلي: {date_to_use}")
        elif shipment.estimated_arrival_date:
            date_to_use = shipment.estimated_arrival_date
            print(f"✅ استخدام تاريخ الوصول المتوقع: {date_to_use}")
        elif shipment.created_at:
            date_to_use = shipment.created_at
            print(f"✅ استخدام تاريخ الإنشاء: {date_to_use}")
        else:
            print("⚠️ لا يوجد تاريخ متاح")
        
        if date_to_use and hasattr(date_to_use, 'year'):
            print(f"   - السنة: {date_to_use.year}")
            print(f"   - الشهر: {date_to_use.month}")
            print(f"   - اليوم: {date_to_use.day}")
            print("✅ التاريخ صالح للاستخدام")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التواريخ: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 اختبار إصلاح مشكلة تحميل بيانات الشحنة")
    print("=" * 50)
    
    # اختبار تحميل البيانات
    if test_shipment_data_loading():
        print("\n" + "=" * 30)
        
        # اختبار معالجة التواريخ
        if test_date_handling():
            print("\n🎉 تم حل جميع مشاكل تحميل البيانات!")
            print("✅ الآن يمكن:")
            print("   • تحميل بيانات الشحنة بدون أخطاء")
            print("   • الوصول للحقول الجديدة")
            print("   • معالجة التواريخ بشكل آمن")
            print("   • تحميل بيانات المورد")
        else:
            print("⚠️ مشكلة في معالجة التواريخ")
    else:
        print("❌ مشكلة في تحميل البيانات")
