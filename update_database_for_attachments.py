#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحديث قاعدة البيانات لإضافة حقول المرفقات
"""

import sqlite3
import os
import sys

def update_database():
    """تحديث قاعدة البيانات لإضافة حقول المرفقات"""
    
    # مسار قاعدة البيانات
    db_path = "data/proshipment.db"
    
    if not os.path.exists(db_path):
        print(f"خطأ: ملف قاعدة البيانات غير موجود: {db_path}")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("بدء تحديث قاعدة البيانات...")
        
        # قائمة الحقول الجديدة للمرفقات
        attachment_fields = [
            ("initial_documents_files", "TEXT", "مسارات ملفات المستندات الأولية (JSON)"),
            ("dn_documents_files", "TEXT", "مسارات ملفات مستندات DN (JSON)"),
            ("customs_documents_files", "TEXT", "مسارات ملفات المستندات الجمركية (JSON)"),
            ("bill_of_lading_files", "TEXT", "مسارات ملفات بوليصة الشحن (JSON)"),
            ("items_images_files", "TEXT", "مسارات ملفات صور الأصناف (JSON)"),
            ("other_documents_files", "TEXT", "مسارات ملفات مستندات أخرى (JSON)")
        ]
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='shipments'")
        if not cursor.fetchone():
            print("خطأ: جدول الشحنات غير موجود")
            return False
        
        # التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(shipments)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة الحقول الجديدة
        for field_name, field_type, comment in attachment_fields:
            if field_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE shipments ADD COLUMN {field_name} {field_type}"
                    cursor.execute(sql)
                    print(f"✓ تم إضافة الحقل: {field_name} - {comment}")
                except sqlite3.Error as e:
                    print(f"✗ فشل في إضافة الحقل {field_name}: {e}")
                    return False
            else:
                print(f"○ الحقل موجود مسبقاً: {field_name}")
        
        # حفظ التغييرات
        conn.commit()
        print("\n✓ تم تحديث قاعدة البيانات بنجاح!")
        
        # عرض معلومات الجدول المحدث
        print("\nمعلومات جدول الشحنات المحدث:")
        cursor.execute("PRAGMA table_info(shipments)")
        columns = cursor.fetchall()
        
        print(f"عدد الأعمدة: {len(columns)}")
        print("الأعمدة الجديدة للمرفقات:")
        for field_name, _, _ in attachment_fields:
            for col in columns:
                if col[1] == field_name:
                    print(f"  - {field_name}: {col[2]}")
                    break
        
        return True
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"خطأ عام: {e}")
        return False
    finally:
        if conn:
            conn.close()

def verify_update():
    """التحقق من نجاح التحديث"""
    db_path = "data/proshipment.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الحقول الجديدة
        cursor.execute("PRAGMA table_info(shipments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_fields = [
            "initial_documents_files",
            "dn_documents_files", 
            "customs_documents_files",
            "bill_of_lading_files",
            "items_images_files",
            "other_documents_files"
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in columns:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"خطأ: الحقول التالية مفقودة: {missing_fields}")
            return False
        else:
            print("✓ جميع حقول المرفقات موجودة في قاعدة البيانات")
            return True
            
    except Exception as e:
        print(f"خطأ في التحقق: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("سكريبت تحديث قاعدة البيانات - إضافة حقول المرفقات")
    print("=" * 60)
    
    # تحديث قاعدة البيانات
    if update_database():
        print("\n" + "=" * 60)
        print("التحقق من التحديث...")
        print("=" * 60)
        
        # التحقق من نجاح التحديث
        if verify_update():
            print("\n✓ تم تحديث قاعدة البيانات بنجاح وجميع الحقول موجودة!")
            print("\nيمكنك الآن استخدام ميزة المرفقات في نظام إدارة الشحنات.")
        else:
            print("\n✗ فشل في التحقق من التحديث")
            sys.exit(1)
    else:
        print("\n✗ فشل في تحديث قاعدة البيانات")
        sys.exit(1)

if __name__ == "__main__":
    main()
