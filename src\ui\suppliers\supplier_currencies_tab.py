# -*- coding: utf-8 -*-
"""
تبويب ربط الموردين بالعملات
Supplier Currencies Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QCheckBox,
                               QTextEdit, QDoubleSpinBox, QAbstractItemView, QSplitter)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier, Currency, SupplierCurrency


class SupplierCurrenciesTab(QWidget):
    """تبويب ربط الموردين بالعملات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_supplier_currency_id = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء splitter للتحكم في الأحجام
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - نموذج الإدخال
        form_widget = self.create_form_section()
        form_widget.setMaximumWidth(450)
        form_widget.setMinimumWidth(400)
        
        # الجانب الأيمن - الجدول
        table_widget = self.create_table_section()
        
        splitter.addWidget(form_widget)
        splitter.addWidget(table_widget)
        splitter.setStretchFactor(0, 0)  # النموذج لا يتمدد
        splitter.setStretchFactor(1, 1)  # الجدول يتمدد
        
        main_layout.addWidget(splitter)
    
    def create_form_section(self):
        """إنشاء قسم النموذج"""
        form_group = QGroupBox("ربط مورد بعملة")
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(15, 15, 15, 15)

        # نموذج الإدخال
        input_form = QFormLayout()
        input_form.setSpacing(10)

        # البحث عن المورد
        self.supplier_search = QLineEdit()
        self.supplier_search.setPlaceholderText("ابحث عن المورد...")
        self.supplier_search.textChanged.connect(self.on_supplier_search_changed)
        self.supplier_search.setMinimumHeight(35)
        self.supplier_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("البحث عن المورد:", self.supplier_search)

        # قائمة الموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.setMinimumHeight(35)
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("المورد:", self.supplier_combo)

        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(35)
        self.currency_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("العملة:", self.currency_combo)

        # العملة المفضلة
        self.is_preferred_check = QCheckBox("العملة المفضلة للمورد")
        self.is_preferred_check.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                padding: 5px;
            }
        """)
        input_form.addRow("", self.is_preferred_check)

        # سعر الصرف المخصص
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.001, 999999.999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(0.0)
        self.exchange_rate_spin.setSpecialValueText("استخدام السعر الافتراضي")
        self.exchange_rate_spin.setMinimumHeight(35)
        self.exchange_rate_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("سعر الصرف المخصص:", self.exchange_rate_spin)

        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات حول ربط المورد بالعملة...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        input_form.addRow("الملاحظات:", self.notes_edit)

        form_layout.addLayout(input_form)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.add_button = QPushButton("إضافة ربط")
        self.add_button.clicked.connect(self.add_supplier_currency)
        self.add_button.setMinimumHeight(40)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_supplier_currency)
        self.update_button.setEnabled(False)
        self.update_button.setMinimumHeight(40)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)

        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)

        form_layout.addLayout(buttons_layout)
        
        return form_group
    
    def create_table_section(self):
        """إنشاء قسم الجدول"""
        table_group = QGroupBox("ربط الموردين بالعملات")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # الجدول
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(7)
        self.currencies_table.setHorizontalHeaderLabels([
            "المورد", "العملة", "الرمز", "مفضلة", "سعر مخصص", "الملاحظات", "الحالة"
        ])
        
        # إعدادات الجدول
        self.currencies_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.horizontalHeader().setStretchLastSection(True)
        self.currencies_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.currencies_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # المورد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الرمز
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # مفضلة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # سعر مخصص
        header.setSectionResizeMode(5, QHeaderView.Stretch)           # الملاحظات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة

        # ربط إشارة التحديد
        self.currencies_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.currencies_table)

        # أزرار الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_supplier_currency)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        
        return table_group

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).order_by(Supplier.name).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)

            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")
        finally:
            session.close()

    def load_currencies(self):
        """تحميل قائمة العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()

            self.currency_combo.clear()
            self.currency_combo.addItem("-- اختر عملة --", None)

            for currency in currencies:
                display_text = f"{currency.name} ({currency.code})"
                if currency.symbol:
                    display_text += f" - {currency.symbol}"
                self.currency_combo.addItem(display_text, currency.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل العملات: {str(e)}")
        finally:
            session.close()

    def load_data(self):
        """تحميل بيانات ربط الموردين بالعملات"""
        # تحميل القوائم المنسدلة
        self.load_suppliers()
        self.load_currencies()

        # تحميل بيانات الجدول
        session = self.db_manager.get_session()
        try:
            supplier_currencies = session.query(SupplierCurrency).join(
                Supplier
            ).join(
                Currency
            ).filter(
                SupplierCurrency.is_active == True
            ).order_by(
                Supplier.name, Currency.name
            ).all()

            self.currencies_table.setRowCount(len(supplier_currencies))

            for row, sc in enumerate(supplier_currencies):
                # المورد
                supplier_item = QTableWidgetItem(sc.supplier.name)
                supplier_item.setData(Qt.UserRole, sc.id)
                self.currencies_table.setItem(row, 0, supplier_item)

                # العملة
                currency_item = QTableWidgetItem(sc.currency.name)
                self.currencies_table.setItem(row, 1, currency_item)

                # الرمز
                symbol_item = QTableWidgetItem(sc.currency.symbol or "")
                self.currencies_table.setItem(row, 2, symbol_item)

                # مفضلة
                preferred_item = QTableWidgetItem("✓" if sc.is_preferred else "")
                preferred_item.setTextAlignment(Qt.AlignCenter)
                self.currencies_table.setItem(row, 3, preferred_item)

                # سعر مخصص
                rate_text = ""
                if sc.exchange_rate_override and sc.exchange_rate_override > 0:
                    rate_text = f"{sc.exchange_rate_override:.4f}"
                else:
                    rate_text = f"{sc.currency.exchange_rate:.4f} (افتراضي)"
                rate_item = QTableWidgetItem(rate_text)
                self.currencies_table.setItem(row, 4, rate_item)

                # الملاحظات
                notes_item = QTableWidgetItem(sc.notes or "")
                self.currencies_table.setItem(row, 5, notes_item)

                # الحالة
                status_item = QTableWidgetItem("نشط" if sc.is_active else "غير نشط")
                self.currencies_table.setItem(row, 6, status_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def on_supplier_search_changed(self):
        """عند تغيير نص البحث"""
        self.search_timer.stop()
        self.search_timer.start(300)  # انتظار 300ms قبل البحث

    def perform_search(self):
        """تنفيذ البحث الفوري"""
        search_text = self.supplier_search.text().strip().lower()

        if not search_text:
            # إذا كان البحث فارغ، أظهر جميع الموردين
            self.load_suppliers()
            return

        session = self.db_manager.get_session()
        try:
            # البحث في اسم المورد أو الكود
            suppliers = session.query(Supplier).filter(
                Supplier.is_active == True,
                (Supplier.name.ilike(f'%{search_text}%') |
                 Supplier.code.ilike(f'%{search_text}%'))
            ).order_by(Supplier.name).all()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)

            for supplier in suppliers:
                display_text = supplier.name
                if supplier.code:
                    display_text += f" ({supplier.code})"
                self.supplier_combo.addItem(display_text, supplier.id)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()

    def add_supplier_currency(self):
        """إضافة ربط جديد بين مورد وعملة"""
        if not self.validate_form():
            return

        supplier_id = self.supplier_combo.currentData()
        currency_id = self.currency_combo.currentData()

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود ربط مسبق
            existing = session.query(SupplierCurrency).filter_by(
                supplier_id=supplier_id,
                currency_id=currency_id
            ).first()

            if existing:
                QMessageBox.warning(self, "تحذير", "هذا الربط موجود بالفعل!")
                return

            # إذا كانت العملة مفضلة، إلغاء تفضيل العملات الأخرى للمورد
            if self.is_preferred_check.isChecked():
                session.query(SupplierCurrency).filter_by(
                    supplier_id=supplier_id,
                    is_preferred=True
                ).update({SupplierCurrency.is_preferred: False})

            # إنشاء الربط الجديد
            exchange_rate = None
            if self.exchange_rate_spin.value() > 0:
                exchange_rate = self.exchange_rate_spin.value()

            supplier_currency = SupplierCurrency(
                supplier_id=supplier_id,
                currency_id=currency_id,
                is_preferred=self.is_preferred_check.isChecked(),
                exchange_rate_override=exchange_rate,
                notes=self.notes_edit.toPlainText().strip() or None
            )

            session.add(supplier_currency)
            session.commit()

            QMessageBox.information(self, "نجح", "تم إضافة الربط بنجاح!")
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الربط: {str(e)}")
        finally:
            session.close()

    def update_supplier_currency(self):
        """تحديث ربط موجود"""
        if not self.current_supplier_currency_id:
            return

        if not self.validate_form():
            return

        supplier_id = self.supplier_combo.currentData()
        currency_id = self.currency_combo.currentData()

        session = self.db_manager.get_session()
        try:
            # الحصول على الربط الحالي
            supplier_currency = session.query(SupplierCurrency).get(self.current_supplier_currency_id)
            if not supplier_currency:
                QMessageBox.warning(self, "خطأ", "الربط غير موجود!")
                return

            # التحقق من عدم وجود ربط مكرر (إذا تم تغيير المورد أو العملة)
            if (supplier_currency.supplier_id != supplier_id or
                supplier_currency.currency_id != currency_id):
                existing = session.query(SupplierCurrency).filter_by(
                    supplier_id=supplier_id,
                    currency_id=currency_id
                ).filter(
                    SupplierCurrency.id != self.current_supplier_currency_id
                ).first()

                if existing:
                    QMessageBox.warning(self, "تحذير", "هذا الربط موجود بالفعل!")
                    return

            # إذا كانت العملة مفضلة، إلغاء تفضيل العملات الأخرى للمورد
            if self.is_preferred_check.isChecked():
                session.query(SupplierCurrency).filter_by(
                    supplier_id=supplier_id,
                    is_preferred=True
                ).filter(
                    SupplierCurrency.id != self.current_supplier_currency_id
                ).update({SupplierCurrency.is_preferred: False})

            # تحديث البيانات
            supplier_currency.supplier_id = supplier_id
            supplier_currency.currency_id = currency_id
            supplier_currency.is_preferred = self.is_preferred_check.isChecked()
            supplier_currency.exchange_rate_override = (
                self.exchange_rate_spin.value() if self.exchange_rate_spin.value() > 0 else None
            )
            supplier_currency.notes = self.notes_edit.toPlainText().strip() or None

            session.commit()

            QMessageBox.information(self, "نجح", "تم تحديث الربط بنجاح!")
            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الربط: {str(e)}")
        finally:
            session.close()

    def delete_supplier_currency(self):
        """حذف ربط محدد"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ربط للحذف")
            return

        # الحصول على معرف الربط
        supplier_currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
        if not supplier_currency_id:
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الربط؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        session = self.db_manager.get_session()
        try:
            supplier_currency = session.query(SupplierCurrency).get(supplier_currency_id)
            if supplier_currency:
                # حذف ناعم - تعطيل الربط بدلاً من الحذف
                supplier_currency.is_active = False
                session.commit()

                QMessageBox.information(self, "نجح", "تم حذف الربط بنجاح!")
                self.load_data()
            else:
                QMessageBox.warning(self, "خطأ", "الربط غير موجود!")

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الربط: {str(e)}")
        finally:
            session.close()

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.currencies_table.currentRow()
        if current_row >= 0:
            self.load_selected_supplier_currency()

    def load_selected_supplier_currency(self):
        """تحميل بيانات الربط المحدد"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            return

        supplier_currency_id = self.currencies_table.item(current_row, 0).data(Qt.UserRole)
        if not supplier_currency_id:
            return

        session = self.db_manager.get_session()
        try:
            supplier_currency = session.query(SupplierCurrency).get(supplier_currency_id)
            if not supplier_currency:
                return

            self.current_supplier_currency_id = supplier_currency.id

            # تعيين المورد
            supplier_index = self.supplier_combo.findData(supplier_currency.supplier_id)
            if supplier_index >= 0:
                self.supplier_combo.setCurrentIndex(supplier_index)

            # تعيين العملة
            currency_index = self.currency_combo.findData(supplier_currency.currency_id)
            if currency_index >= 0:
                self.currency_combo.setCurrentIndex(currency_index)

            # تعيين باقي البيانات
            self.is_preferred_check.setChecked(supplier_currency.is_preferred)

            if supplier_currency.exchange_rate_override:
                self.exchange_rate_spin.setValue(supplier_currency.exchange_rate_override)
            else:
                self.exchange_rate_spin.setValue(0.0)

            self.notes_edit.setPlainText(supplier_currency.notes or "")

            # تفعيل زر التحديث
            self.add_button.setEnabled(False)
            self.update_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.current_supplier_currency_id = None
        self.supplier_search.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.currency_combo.setCurrentIndex(0)
        self.is_preferred_check.setChecked(False)
        self.exchange_rate_spin.setValue(0.0)
        self.notes_edit.clear()

        # إعادة تفعيل زر الإضافة
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if self.supplier_combo.currentData() is None:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
            self.supplier_combo.setFocus()
            return False

        if self.currency_combo.currentData() is None:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة")
            self.currency_combo.setFocus()
            return False

        return True
