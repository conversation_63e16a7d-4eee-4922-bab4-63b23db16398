#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الاستيراد فقط
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication

def test_import():
    """اختبار الاستيراد"""
    print("🧪 اختبار الاستيراد...")
    
    try:
        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication")
        
        # استيراد النافذة
        from src.ui.dialogs.shipment_data_filler_dialog import ShipmentDataFillerDialog
        print("✅ تم استيراد ShipmentDataFillerDialog")
        
        # إنشاء النافذة
        dialog = ShipmentDataFillerDialog()
        print("✅ تم إنشاء النافذة")
        
        print("🎉 الاختبار نجح!")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_import()
