#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الاستيراد المتعدد
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import_fix():
    """اختبار إصلاح مشكلة الاستيراد"""
    try:
        print("🔍 اختبار إصلاح مشكلة الاستيراد المتعدد...")
        
        # اختبار استيراد النافذة
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        print("✅ تم استيراد NewShipmentWindow بنجاح")
        
        # اختبار وجود الدوال المطلوبة
        required_methods = [
            'import_from_excel',
            'import_multiple_shipments', 
            'import_single_shipment',
            'parse_multiple_containers'
        ]
        
        for method in required_methods:
            if hasattr(NewShipmentWindow, method):
                print(f"✅ الدالة {method} موجودة")
            else:
                print(f"❌ الدالة {method} غير موجودة")
                return False
        
        # اختبار استيراد المكتبات المطلوبة
        try:
            import pandas as pd
            print("✅ مكتبة pandas متوفرة")
        except ImportError:
            print("❌ مكتبة pandas غير متوفرة")
            print("💡 قم بتثبيتها: pip install pandas")
            return False
            
        try:
            import openpyxl
            print("✅ مكتبة openpyxl متوفرة")
        except ImportError:
            print("❌ مكتبة openpyxl غير متوفرة")
            print("💡 قم بتثبيتها: pip install openpyxl")
            return False
        
        # اختبار إنشاء النافذة
        try:
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            window = NewShipmentWindow()
            print("✅ تم إنشاء النافذة بنجاح")
            
            # اختبار وجود زر الاستيراد
            if hasattr(window, 'import_excel_action'):
                print("✅ زر استيراد الإكسيل موجود")
            else:
                print("⚠️ زر استيراد الإكسيل غير موجود")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            return False
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def test_database_imports():
    """اختبار استيراد نماذج قاعدة البيانات"""
    try:
        print("\n🔍 اختبار استيراد نماذج قاعدة البيانات...")
        
        from src.database.database_manager import DatabaseManager
        print("✅ تم استيراد DatabaseManager")

        from src.database.models import Shipment, Container, Supplier
        print("✅ تم استيراد النماذج: Shipment, Container, Supplier")
        
        # اختبار إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الحصول على جلسة
        session = db_manager.get_session()
        print("✅ تم الحصول على جلسة قاعدة البيانات")
        session.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاح مشكلة الاستيراد المتعدد")
    print("=" * 50)
    
    # اختبار الاستيراد الأساسي
    if not test_import_fix():
        print("\n❌ فشل في الاختبار الأساسي")
        return
    
    # اختبار قاعدة البيانات
    if not test_database_imports():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return
    
    print("\n✅ تم إصلاح جميع مشاكل الاستيراد بنجاح!")
    print("\n🎯 الميزات المتاحة الآن:")
    print("   • استيراد فردي من الإكسيل")
    print("   • استيراد متعدد من الإكسيل")
    print("   • دعم الحاويات المتعددة")
    print("   • إنشاء موردين تلقائياً")
    print("   • معالجة التواريخ المختلفة")
    
    print("\n💡 لاختبار الميزة:")
    print("   1. شغل التطبيق الرئيسي")
    print("   2. افتح شاشة الشحنة الجديدة")
    print("   3. انقر على زر 'استيراد إكسيل'")
    print("   4. اختر ملف الإكسيل")
    print("   5. اختر نوع الاستيراد (فردي أو متعدد)")

if __name__ == "__main__":
    main()
