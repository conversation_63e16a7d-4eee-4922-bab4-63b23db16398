#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لنافذة تعبئة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QPushButton

def test_simple_dialog():
    """اختبار نافذة بسيطة"""
    print("🧪 اختبار نافذة بسيطة...")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء نافذة بسيطة
        dialog = QDialog()
        dialog.setWindowTitle("اختبار")
        dialog.resize(400, 300)
        
        layout = QVBoxLayout()
        label = QLabel("النافذة تعمل بنجاح!")
        button = QPushButton("إغلاق")
        button.clicked.connect(dialog.close)
        
        layout.addWidget(label)
        layout.addWidget(button)
        dialog.setLayout(layout)
        
        print("✅ تم إنشاء النافذة البسيطة بنجاح")
        
        # عرض النافذة
        dialog.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_dialog()
