# -*- coding: utf-8 -*-
"""
أداة فحص الأصناف المكررة في قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Item
from sqlalchemy import func

def check_duplicate_items():
    """فحص الأصناف المكررة في قاعدة البيانات"""
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        print("🔍 فحص الأصناف المكررة في قاعدة البيانات...")
        print("=" * 50)
        
        # البحث عن الأكواد المكررة
        duplicate_codes = session.query(
            Item.code,
            func.count(Item.code).label('count')
        ).group_by(Item.code).having(func.count(Item.code) > 1).all()
        
        if not duplicate_codes:
            print("✅ لا توجد أكواد مكررة في قاعدة البيانات")
            return
        
        print(f"❌ تم العثور على {len(duplicate_codes)} كود مكرر:")
        print()
        
        total_duplicates = 0
        for code, count in duplicate_codes:
            print(f"📦 الكود: '{code}' - مكرر {count} مرة")
            
            # عرض تفاصيل الأصناف المكررة
            items = session.query(Item).filter_by(code=code).all()
            for i, item in enumerate(items, 1):
                print(f"   {i}. ID: {item.id} - الاسم: {item.name}")
            
            total_duplicates += count - 1  # عدد النسخ الزائدة
            print()
        
        print(f"📊 إجمالي النسخ الزائدة: {total_duplicates}")
        print()
        
        # اقتراح الحلول
        print("💡 الحلول المقترحة:")
        print("1. حذف النسخ المكررة والاحتفاظ بالأحدث")
        print("2. دمج البيانات من النسخ المكررة")
        print("3. تعديل الأكواد لجعلها فريدة")
        print()
        
        # عرض الكود المشكل
        problem_code = '0197-001-'
        problem_items = session.query(Item).filter_by(code=problem_code).all()
        if problem_items:
            print(f"🔴 الكود المشكل '{problem_code}':")
            for i, item in enumerate(problem_items, 1):
                print(f"   {i}. ID: {item.id} - الاسم: {item.name} - تاريخ الإنشاء: {item.created_at}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
    
    finally:
        session.close()

def fix_duplicate_items():
    """إصلاح الأصناف المكررة"""
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        print("🔧 إصلاح الأصناف المكررة...")
        print("=" * 50)
        
        # البحث عن الأكواد المكررة
        duplicate_codes = session.query(
            Item.code,
            func.count(Item.code).label('count')
        ).group_by(Item.code).having(func.count(Item.code) > 1).all()
        
        if not duplicate_codes:
            print("✅ لا توجد أكواد مكررة للإصلاح")
            return
        
        fixed_count = 0
        for code, count in duplicate_codes:
            print(f"🔧 إصلاح الكود المكرر: '{code}'")
            
            # الحصول على جميع الأصناف بنفس الكود
            items = session.query(Item).filter_by(code=code).order_by(Item.created_at.desc()).all()
            
            # الاحتفاظ بالأحدث وحذف الباقي
            keep_item = items[0]  # الأحدث
            delete_items = items[1:]  # الباقي
            
            print(f"   ✅ الاحتفاظ بـ: ID {keep_item.id} - {keep_item.name}")
            
            for item in delete_items:
                print(f"   🗑️  حذف: ID {item.id} - {item.name}")
                session.delete(item)
                fixed_count += 1
        
        # حفظ التغييرات
        session.commit()
        print(f"✅ تم إصلاح {fixed_count} صنف مكرر")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأصناف المكررة: {str(e)}")
        session.rollback()
    
    finally:
        session.close()

if __name__ == "__main__":
    print("أداة فحص وإصلاح الأصناف المكررة")
    print("=" * 40)
    print("1. فحص الأصناف المكررة")
    print("2. إصلاح الأصناف المكررة")
    print("3. خروج")
    
    while True:
        choice = input("\nاختر العملية (1-3): ").strip()
        
        if choice == "1":
            check_duplicate_items()
        elif choice == "2":
            confirm = input("هل أنت متأكد من إصلاح الأصناف المكررة؟ (y/n): ").strip().lower()
            if confirm in ['y', 'yes', 'نعم']:
                fix_duplicate_items()
            else:
                print("تم إلغاء العملية")
        elif choice == "3":
            print("وداعاً!")
            break
        else:
            print("اختيار غير صحيح، يرجى المحاولة مرة أخرى")
