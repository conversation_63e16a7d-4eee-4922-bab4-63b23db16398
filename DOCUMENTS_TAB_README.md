# تبويب المستندات - الروابط التشعبية

## نظرة عامة

تم تطوير تبويب المستندات في شاشة "شحنة جديدة" ليشمل حقول روابط تشعبية محددة للمستندات المختلفة، بالإضافة إلى قسم المستندات الإضافية الموجود مسبقاً.

## الميزات الجديدة

### 1. قسم روابط المستندات المحددة

يحتوي على 6 حقول رئيسية:

- **المستندات الأولية**: رابط للمستندات الأولية للشحنة
- **المستندات (DN)**: رابط لمستندات DN
- **المستندات المرسلة للجمارك**: رابط للمستندات المرسلة للجمارك
- **بوليصة الشحن**: رابط لبوليصة الشحن
- **صور الأصناف**: رابط لصور الأصناف
- **مستندات أخرى**: رابط لأي مستندات أخرى

### 2. نافذة إضافة الرابط المحسنة

- **واجهة سهلة الاستخدام**: نافذة حوار مخصصة لإدخال الروابط
- **التحقق من صحة الرابط**: التحقق التلقائي من صيغة الرابط
- **اختبار الرابط**: إمكانية اختبار الرابط قبل الحفظ
- **أنواع المصادر**: اختيار نوع المصدر (Google Drive, OneDrive, إلخ)
- **الوصف**: إضافة وصف للرابط

### 3. الروابط التشعبية التفاعلية

- **تنسيق مرئي**: الروابط تظهر بلون أزرق مع خط تحتي
- **النقر للفتح**: النقر على الرابط يفتحه في المتصفح
- **معلومات إضافية**: عرض معلومات الرابط عند التمرير

## كيفية الاستخدام

### إضافة رابط جديد

1. انقر على زر "إضافة رابط" بجانب الحقل المطلوب
2. ستظهر نافذة إضافة الرابط
3. أدخل الرابط في الحقل المخصص
4. اختر نوع المصدر من القائمة المنسدلة
5. أضف وصفاً للرابط (اختياري)
6. انقر على "اختبار الرابط" للتأكد من صحته
7. انقر على "حفظ" لحفظ الرابط

### فتح رابط موجود

- انقر مباشرة على الرابط في الحقل لفتحه في المتصفح

### تعديل رابط موجود

1. انقر على زر "إضافة رابط" بجانب الحقل
2. ستظهر النافذة مع الرابط الحالي
3. قم بتعديل الرابط أو الوصف
4. انقر على "حفظ"

## التحديثات التقنية

### قاعدة البيانات

تم إضافة الحقول التالية إلى جدول `shipments`:

```sql
- initial_documents_url: رابط المستندات الأولية
- dn_documents_url: رابط مستندات DN  
- customs_documents_url: رابط المستندات المرسلة للجمارك
- bill_of_lading_url: رابط بوليصة الشحن
- items_images_url: رابط صور الأصناف
- other_documents_url: رابط مستندات أخرى
```

### الملفات الجديدة

- `src/ui/shipments/add_link_dialog.py`: نافذة إضافة الرابط
- `test_documents_tab.py`: ملف اختبار التبويب

### الملفات المحدثة

- `src/database/models.py`: إضافة حقول الروابط
- `src/ui/shipments/new_shipment_window.py`: تحديث تبويب المستندات

## الاختبار

لاختبار الميزة الجديدة:

```bash
python test_documents_tab.py
```

## الملاحظات

- جميع الروابط يتم حفظها في قاعدة البيانات
- الروابط تدعم جميع البروتوكولات (http, https)
- يمكن استخدام روابط من خدمات التخزين السحابي
- النظام يتحقق من صحة الروابط قبل الحفظ
- واجهة المستخدم تدعم اللغة العربية بالكامل

## الدعم الفني

في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق التطوير.
