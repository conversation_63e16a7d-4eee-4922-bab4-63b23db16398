#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ثبات تاريخ الشحنة عند التعديل
"""

import sys
import os
from datetime import datetime, date
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QDate

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier

def test_shipment_date_persistence():
    """اختبار ثبات تاريخ الشحنة"""
    
    app = QApplication(sys.argv)
    
    print("🗓️ اختبار ثبات تاريخ الشحنة عند التعديل")
    print("=" * 80)
    
    db_manager = DatabaseManager()
    
    try:
        # إنشاء جلسة قاعدة البيانات
        session = db_manager.get_session()
        
        # البحث عن شحنة موجودة للاختبار
        existing_shipment = session.query(Shipment).first()
        
        if not existing_shipment:
            print("❌ لا توجد شحنات في قاعدة البيانات للاختبار")
            return False
        
        print(f"📦 اختبار الشحنة: {existing_shipment.shipment_number}")
        
        # عرض التاريخ الحالي
        original_date = existing_shipment.shipment_date
        print(f"📅 التاريخ الأصلي: {original_date}")
        
        # محاكاة تحميل الشحنة للتعديل
        print("\n🔄 محاكاة تحميل الشحنة للتعديل...")
        
        # التحقق من منطق تحميل التاريخ
        date_to_use = None
        if existing_shipment.shipment_date:
            date_to_use = existing_shipment.shipment_date
            print(f"✅ تم العثور على تاريخ الشحنة المحفوظ: {date_to_use}")
        elif existing_shipment.created_at:
            date_to_use = existing_shipment.created_at
            print(f"⚠️ استخدام تاريخ الإنشاء: {date_to_use}")
        else:
            date_to_use = datetime.now().date()
            print(f"⚠️ استخدام التاريخ الحالي: {date_to_use}")
        
        # التحقق من أن التاريخ المحمل يطابق التاريخ الأصلي
        if original_date and date_to_use:
            # تحويل التواريخ للمقارنة
            if hasattr(original_date, 'date'):
                original_date_obj = original_date.date()
            else:
                original_date_obj = original_date
                
            if hasattr(date_to_use, 'date'):
                loaded_date_obj = date_to_use.date()
            else:
                loaded_date_obj = date_to_use
            
            if original_date_obj == loaded_date_obj:
                print("✅ التاريخ ثابت - لم يتغير عند التحميل للتعديل")
                test_result = True
            else:
                print(f"❌ التاريخ تغير! الأصلي: {original_date_obj}, المحمل: {loaded_date_obj}")
                test_result = False
        else:
            print("⚠️ لا يمكن مقارنة التواريخ - أحدهما فارغ")
            test_result = False
        
        # اختبار إضافي: محاكاة حفظ الشحنة مرة أخرى
        print("\n💾 اختبار حفظ الشحنة مرة أخرى...")
        
        # محاكاة تحديث بيانات أخرى (ليس التاريخ)
        existing_shipment.notes = "تم تحديث الملاحظات للاختبار"
        session.commit()
        
        # إعادة تحميل الشحنة من قاعدة البيانات
        session.refresh(existing_shipment)
        
        # التحقق من أن التاريخ لم يتغير
        updated_date = existing_shipment.shipment_date
        print(f"📅 التاريخ بعد الحفظ: {updated_date}")
        
        if original_date == updated_date:
            print("✅ التاريخ ثابت بعد الحفظ")
            final_result = test_result
        else:
            print(f"❌ التاريخ تغير بعد الحفظ! الأصلي: {original_date}, الجديد: {updated_date}")
            final_result = False
        
        session.close()
        
        print("\n" + "=" * 80)
        if final_result:
            print("🎉 نجح الاختبار! تاريخ الشحنة ثابت ولا يتغير عند التعديل")
        else:
            print("❌ فشل الاختبار! تاريخ الشحنة يتغير عند التعديل")
        
        return final_result
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_shipment_date_persistence()
    sys.exit(0 if success else 1)
