import sys
import os
sys.path.append('src')

from utils.shipping_company_validator import ShippingCompanyValidator

print('🔍 اختبار سريع للمدقق...')

validator = ShippingCompanyValidator()
print('✅ تم إنشاء المدقق بنجاح')

# اختبار بسيط
test_name = 'MSC'
result = validator.validate_and_correct(test_name)
print(f'اختبار {test_name}: صحيح = {result["is_valid"]}')

test_name = 'MSK'
result = validator.validate_and_correct(test_name)
suggestions_count = len(result.get('suggestions', []))
print(f'اختبار {test_name}: {suggestions_count} اقتراحات')

if result.get('suggestions'):
    best = result['suggestions'][0]
    print(f'أفضل اقتراح: {best["suggested_name"]}')

print('✅ المدقق يعمل بنجاح!')
