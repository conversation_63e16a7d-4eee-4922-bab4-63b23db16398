#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار البيانات في قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, Supplier, Container, ShipmentItem

def test_database():
    """اختبار البيانات في قاعدة البيانات"""
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        print("=== اختبار قاعدة البيانات ===")
        
        # عدد الشحنات
        shipments_count = session.query(Shipment).filter(Shipment.is_active == True).count()
        print(f"عدد الشحنات النشطة: {shipments_count}")
        
        # عدد الموردين
        suppliers_count = session.query(Supplier).count()
        print(f"عدد الموردين: {suppliers_count}")
        
        # عدد الحاويات
        containers_count = session.query(Container).count()
        print(f"عدد الحاويات: {containers_count}")
        
        # عدد الأصناف
        items_count = session.query(ShipmentItem).count()
        print(f"عدد الأصناف: {items_count}")
        
        # عرض أول 5 شحنات
        print("\n=== أول 5 شحنات ===")
        shipments = session.query(Shipment).outerjoin(Supplier).filter(
            Shipment.is_active == True
        ).order_by(Shipment.created_at.desc()).limit(5).all()
        
        for i, shipment in enumerate(shipments, 1):
            print(f"{i}. رقم الشحنة: {shipment.shipment_number}")
            print(f"   المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}")
            print(f"   الحالة: {shipment.status}")
            print(f"   تاريخ الإنشاء: {shipment.created_at}")
            print("   ---")
        
        session.close()
        
    except Exception as e:
        print(f"خطأ في اختبار قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database()
