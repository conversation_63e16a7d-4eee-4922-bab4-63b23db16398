#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تكامل نوافذ الحوار الجديدة في طلبات الشراء
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def test_dialog_integration():
    """اختبار تكامل نوافذ الحوار"""
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        # استيراد النوافذ المطلوبة
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        from src.ui.shipments.add_link_dialog import AddLinkDialog
        from src.ui.shipments.attachments_manager_dialog import AttachmentsManagerDialog
        
        print("✅ تم استيراد جميع النوافذ بنجاح")
        
        # إنشاء نافذة طلبات الشراء
        purchase_window = PurchaseOrdersWindow()
        
        # التحقق من وجود الدوال الجديدة
        if hasattr(purchase_window, 'add_link_dialog'):
            print("✅ دالة add_link_dialog موجودة")
        else:
            print("❌ دالة add_link_dialog غير موجودة")
            
        if hasattr(purchase_window, 'add_file_attachment'):
            print("✅ دالة add_file_attachment موجودة")
        else:
            print("❌ دالة add_file_attachment غير موجودة")
            
        if hasattr(purchase_window, 'open_url'):
            print("✅ دالة open_url موجودة")
        else:
            print("❌ دالة open_url غير موجودة")
        
        # اختبار إنشاء نافذة إضافة الرابط
        try:
            link_dialog = AddLinkDialog(purchase_window, "اختبار", "", "")
            print("✅ تم إنشاء نافذة إضافة الرابط بنجاح")
            link_dialog.close()
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة إضافة الرابط: {e}")
        
        # اختبار إنشاء نافذة إدارة المرفقات
        try:
            attachments_dialog = AttachmentsManagerDialog(purchase_window, "اختبار", [])
            print("✅ تم إنشاء نافذة إدارة المرفقات بنجاح")
            attachments_dialog.close()
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة إدارة المرفقات: {e}")
        
        # عرض نافذة طلبات الشراء للاختبار اليدوي
        purchase_window.show()
        
        print("\n🎉 تم تكامل النوافذ بنجاح!")
        print("📋 يمكنك الآن اختبار:")
        print("   • الضغط على أزرار الروابط في تبويب المستندات")
        print("   • الضغط على أزرار المرفقات في تبويب المستندات")
        print("   • التحقق من ظهور النوافذ الجديدة")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = test_dialog_integration()
    sys.exit(exit_code)
