#!/usr/bin/env python3
"""
اختبار وضع الإدخال
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("اختبار وضع الإدخال...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # اختبار وضع الإدخال
        print("إنشاء نافذة وضع الإدخال...")
        entry_window = PurchaseOrdersWindow(mode="entry")
        print("تم إنشاء النافذة بنجاح")
        
        # التحقق من التبويبات
        if hasattr(entry_window, 'details_tabs'):
            print(f"عدد التبويبات: {entry_window.details_tabs.count()}")
        else:
            print("لا توجد تبويبات")
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
