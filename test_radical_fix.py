#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل الجذري لمشكلة التعليق في وضع التعديل
Test radical fix for edit mode hanging issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer
import time

def test_radical_fix():
    """اختبار الحل الجذري لمشكلة التعليق في وضع التعديل"""
    print("🚀 اختبار الحل الجذري لمشكلة التعليق في وضع التعديل")
    print("=" * 70)
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # اختبار 1: استيراد النافذة
        print("🔍 اختبار 1: استيراد نافذة الشحنة الجديدة...")
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        print("✅ تم استيراد النافذة بنجاح")
        
        # اختبار 2: إنشاء النافذة في وضع التعديل
        print("\n🔍 اختبار 2: إنشاء نافذة في وضع التعديل...")
        edit_window = NewShipmentWindow(shipment_id=1)  # shipment_id يحدد وضع التعديل تلقائياً
        print("✅ تم إنشاء النافذة في وضع التعديل بنجاح")
        print(f"   📋 وضع التعديل: {edit_window.is_edit_mode}")
        print(f"   📋 معرف الشحنة: {edit_window.current_shipment_id}")
        
        # اختبار 3: فحص الحل الجذري - إزالة زر الإلغاء من Progress Dialog
        print("\n🔍 اختبار 3: فحص إزالة زر الإلغاء من Progress Dialog...")
        
        # محاكاة بيانات للحفظ
        test_shipment_data = {
            'shipment_number': 'TEST-001',
            'shipping_company': 'Test Company',
            'bill_of_lading': 'TEST-BOL-001'
        }
        test_items = []
        test_containers = []
        test_documents = []
        
        # بدء عملية الحفظ (بدون تنفيذ فعلي)
        edit_window._is_saving = True
        edit_window.start_async_save(test_shipment_data, test_items, test_containers, test_documents)
        
        # التحقق من أن Progress Dialog لا يحتوي على زر إلغاء
        if edit_window.progress_dialog:
            cancel_button = edit_window.progress_dialog.findChild(type(None), "cancel")
            assert cancel_button is None or not cancel_button.isVisible(), "❌ زر الإلغاء لا يزال موجوداً"
            print("✅ تم إزالة زر الإلغاء من Progress Dialog بنجاح")
        
        # اختبار 4: فحص دالة cancel_save المحسنة
        print("\n🔍 اختبار 4: فحص دالة cancel_save المحسنة...")
        
        # استدعاء دالة cancel_save (يجب ألا تفعل شيئاً)
        edit_window.cancel_save()
        print("✅ دالة cancel_save تم تعطيلها بنجاح (لا تظهر رسالة إلغاء)")
        
        # اختبار 5: فحص تحسينات Worker Class
        print("\n🔍 اختبار 5: فحص تحسينات Worker Class...")
        
        from src.ui.shipments.new_shipment_window import ShipmentSaveWorker
        worker = ShipmentSaveWorker()
        
        # التحقق من وجود التحسينات
        assert hasattr(worker, '_should_stop'), "❌ متغير _should_stop غير موجود"
        assert hasattr(worker, 'stop'), "❌ دالة stop غير موجودة"
        assert hasattr(worker, 'save_shipment_async'), "❌ دالة save_shipment_async غير موجودة"
        
        print("✅ تحسينات Worker Class موجودة")
        
        # اختبار 6: فحص معالجة الأخطاء المحسنة
        print("\n🔍 اختبار 6: فحص معالجة الأخطاء المحسنة...")
        
        # التحقق من وجود دوال معالجة الأخطاء
        assert hasattr(edit_window, 'on_save_error'), "❌ دالة on_save_error غير موجودة"
        assert hasattr(edit_window, 'on_save_completed'), "❌ دالة on_save_completed غير موجودة"
        assert hasattr(edit_window, 'cleanup_save_thread'), "❌ دالة cleanup_save_thread غير موجودة"
        
        print("✅ معالجة الأخطاء المحسنة موجودة")
        
        # اختبار 7: فحص حماية البيانات في وضع التعديل
        print("\n🔍 اختبار 7: فحص حماية البيانات في وضع التعديل...")
        
        # التحقق من وجود load_shipment_data المحسنة
        assert hasattr(edit_window, 'load_shipment_data'), "❌ دالة load_shipment_data غير موجودة"
        
        print("✅ حماية البيانات في وضع التعديل موجودة")
        
        # تنظيف
        print("\n🔍 تنظيف الاختبار...")
        edit_window._is_saving = False
        if hasattr(edit_window, 'cleanup_resources'):
            edit_window.cleanup_resources()
        
        print("✅ تم تنظيف الموارد بنجاح")
        
        print("\n" + "=" * 70)
        print("🎉 الحل الجذري تم تطبيقه بنجاح!")
        print("📋 التحسينات المطبقة:")
        print("   ✓ إزالة زر الإلغاء من Progress Dialog")
        print("   ✓ تعطيل دالة cancel_save")
        print("   ✓ تحسين معالجة الأخطاء في Worker")
        print("   ✓ حماية البيانات في وضع التعديل")
        print("   ✓ تحسين إدارة جلسات قاعدة البيانات")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_fixes():
    """اختبار الإصلاحات المحددة"""
    print("\n🔍 اختبار الإصلاحات المحددة...")
    
    try:
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        
        # إنشاء نافذة للاختبار
        window = NewShipmentWindow(shipment_id=1)  # وضع التعديل
        
        # اختبار 1: التحقق من عدم وجود رسالة "تم إلغاء الحفظ"
        print("   🔸 اختبار عدم ظهور رسالة الإلغاء...")
        window.cancel_save()  # يجب ألا تظهر رسالة
        print("   ✅ لا توجد رسالة إلغاء")
        
        # اختبار 2: التحقق من حالة الحفظ
        print("   🔸 اختبار حالة الحفظ...")
        assert hasattr(window, '_is_saving'), "❌ متغير _is_saving غير موجود"
        assert window._is_saving == False, "❌ حالة الحفظ الأولية خاطئة"
        print("   ✅ حالة الحفظ صحيحة")
        
        # اختبار 3: التحقق من تنظيف الموارد
        print("   🔸 اختبار تنظيف الموارد...")
        if hasattr(window, 'cleanup_resources'):
            window.cleanup_resources()
        print("   ✅ تنظيف الموارد يعمل")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبارات المحددة: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_radical_fix()
    success2 = test_specific_fixes()
    
    if success1 and success2:
        print("\n🎊 جميع الاختبارات نجحت! الحل الجذري مطبق بنجاح")
        print("💡 يمكن الآن اختبار النظام في وضع التعديل بدون مشاكل")
        sys.exit(0)
    else:
        print("\n💥 بعض الاختبارات فشلت! يرجى مراجعة الأخطاء")
        sys.exit(1)
