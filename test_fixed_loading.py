#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة setValue في تحميل طلبات الشراء
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.database.database_manager import DatabaseManager
from src.utils.arabic_support import setup_arabic_support

def main():
    """اختبار إصلاح مشكلة setValue"""
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # إعداد دعم اللغة العربية
    setup_arabic_support(app)
    
    # إعداد قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    print("🚀 اختبار إصلاح مشكلة setValue في تحميل طلبات الشراء")
    print("=" * 60)
    print("✅ الإصلاحات المطبقة:")
    print("   - إصلاح خطأ 'supplier_name_edit' إلى 'supplier_edit'")
    print("   - إصلاح خطأ 'setValue' على QLineEdit إلى 'setText'")
    print("   - إضافة فحص hasattr قبل استخدام الحقول")
    print("   - تحسين معالجة الأخطاء")
    print()
    
    try:
        # إنشاء نافذة الشحنة الجديدة
        window = NewShipmentWindow()
        
        # اختبار تحميل البيانات التجريبية
        window.add_test_items()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم إنشاء النافذة بنجاح!")
        print("📋 تحقق من:")
        print("   - أعمدة التواريخ في جدول الأصناف")
        print("   - عدم ظهور أخطاء setValue")
        print("   - إمكانية تحميل طلبات الشراء")
        
        # رسالة للمستخدم
        QMessageBox.information(
            window,
            "اختبار ناجح",
            "✅ تم إصلاح جميع المشاكل بنجاح!\n\n"
            "الإصلاحات المطبقة:\n"
            "• إصلاح خطأ supplier_name_edit\n"
            "• إصلاح خطأ setValue على QLineEdit\n"
            "• إضافة أعمدة التواريخ\n"
            "• إصلاح الوصول لنظام الموردين\n\n"
            "يمكنك الآن استخدام النظام بشكل طبيعي."
        )
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        QMessageBox.critical(
            None,
            "خطأ",
            f"❌ فشل في الاختبار:\n{str(e)}"
        )
        return
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
