#!/usr/bin/env python3
"""
اختبار نهائي شامل لنافذة طلبات الشراء
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_complete_window():
    """اختبار شامل للنافذة"""
    try:
        print("🔍 بدء الاختبار الشامل...")
        
        # 1. اختبار الاستيراد
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")
        
        # 2. اختبار إنشاء التطبيق
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication بنجاح")
        
        # 3. اختبار إنشاء النافذة
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # 4. اختبار العناصر الأساسية
        assert hasattr(window, 'orders_table'), "جدول الطلبات غير موجود"
        assert hasattr(window, 'search_edit'), "حقل البحث غير موجود"
        assert hasattr(window, 'status_filter_combo'), "فلتر الحالة غير موجود"
        assert hasattr(window, 'supplier_filter_combo'), "فلتر المورد غير موجود"
        print("✅ جميع العناصر الأساسية موجودة")
        
        # 5. اختبار الجدول
        assert window.orders_table.columnCount() == 12, f"عدد الأعمدة خطأ: {window.orders_table.columnCount()}"
        print("✅ الجدول يحتوي على 12 عمود")
        
        # 6. اختبار عناوين الأعمدة
        expected_headers = [
            "رقم الطلب", "تاريخ الطلب", "المورد", "إجمالي الأصناف", "إجمالي الكمية",
            "إجمالي القيمة", "العملة", "حالة الطلب", "تاريخ التسليم المتوقع",
            "الكمية المسلمة", "الكمية المتبقية", "ملاحظات"
        ]
        
        for i, expected_header in enumerate(expected_headers):
            actual_header = window.orders_table.horizontalHeaderItem(i).text()
            assert actual_header == expected_header, f"عنوان العمود {i} خطأ: متوقع '{expected_header}' لكن وجد '{actual_header}'"
        
        print("✅ جميع عناوين الأعمدة صحيحة")
        
        # 7. اختبار الدوال الأساسية
        assert hasattr(window, 'load_orders'), "دالة تحميل الطلبات غير موجودة"
        assert hasattr(window, 'populate_orders_table'), "دالة ملء الجدول غير موجودة"
        assert hasattr(window, 'search_orders'), "دالة البحث غير موجودة"
        assert hasattr(window, 'filter_orders'), "دالة الفلترة غير موجودة"
        print("✅ جميع الدوال الأساسية موجودة")
        
        # 8. اختبار الاستيراد للدوال المساعدة
        try:
            from src.utils.formatters import format_date, format_currency
            print("✅ تم استيراد دوال التنسيق بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد دوال التنسيق: {e}")
            return False
        
        # 9. اختبار إنشاء جدول فارغ
        window.populate_orders_table([])
        assert window.orders_table.rowCount() == 0, "الجدول يجب أن يكون فارغاً"
        print("✅ تم اختبار الجدول الفارغ بنجاح")
        
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ النافذة جاهزة للاستخدام")
        print("✅ تم إصلاح جميع المشاكل:")
        print("   - مشكلة currency_combo")
        print("   - مشكلة QTableWidgetItem")
        print("   - مشكلة shipped_quantity -> delivered_quantity")
        print("   - عناوين الأعمدة")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_complete_window():
        print("\n🚀 النافذة جاهزة تماماً للاستخدام!")
        print("📋 التصميم الجديد:")
        print("   - جدول متكامل مع البحث والفلترة")
        print("   - 12 عمود شامل لجميع البيانات")
        print("   - تصميم يطابق نافذة إدارة الشحنات")
    else:
        print("\n❌ هناك مشاكل تحتاج إلى إصلاح")
        sys.exit(1)
