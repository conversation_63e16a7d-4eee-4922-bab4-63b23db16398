#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح خطأ تحميل الطلب
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_load_order_fix():
    """اختبار إصلاح خطأ تحميل الطلب"""
    print("🔄 اختبار إصلاح خطأ load_order_items...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # فحص وجود الدالة
        if hasattr(window, 'load_order_items'):
            print("✅ دالة load_order_items موجودة")
            
            # فحص signature الدالة
            import inspect
            sig = inspect.signature(window.load_order_items)
            params = list(sig.parameters.keys())
            print(f"✅ معاملات الدالة: {params}")
            
            if 'order_id' in params:
                print("✅ معامل order_id موجود في الدالة")
            else:
                print("❌ معامل order_id غير موجود")
                
        else:
            print("❌ دالة load_order_items غير موجودة")
        
        print("🎉 اختبار الإصلاح مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_load_order_fix()
    sys.exit(0 if success else 1)
