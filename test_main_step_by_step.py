#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق الرئيسي خطوة بخطوة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_step_by_step():
    """اختبار التطبيق خطوة بخطوة"""
    print("🔍 اختبار التطبيق الرئيسي خطوة بخطوة...")
    
    try:
        print("1. استيراد PySide6...")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTranslator, QLocale, Qt
        from PySide6.QtGui import QFont
        print("✅ تم استيراد PySide6 بنجاح")
        
        print("2. إنشاء QApplication...")
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication بنجاح")
        
        print("3. استيراد arabic_support...")
        from src.utils.arabic_support import setup_arabic_support
        print("✅ تم استيراد arabic_support بنجاح")
        
        print("4. تطبيق setup_arabic_support...")
        setup_arabic_support(app)
        print("✅ تم تطبيق setup_arabic_support بنجاح")
        
        print("5. استيراد DatabaseManager...")
        from src.database.database_manager import DatabaseManager
        print("✅ تم استيراد DatabaseManager بنجاح")
        
        print("6. إنشاء DatabaseManager...")
        db_manager = DatabaseManager()
        print("✅ تم إنشاء DatabaseManager بنجاح")
        
        print("7. تهيئة قاعدة البيانات...")
        db_manager.initialize_database()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        print("8. استيراد MainWindow...")
        from src.ui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
        
        print("9. إنشاء MainWindow...")
        main_window = MainWindow()
        print("✅ تم إنشاء MainWindow بنجاح")
        
        print("10. عرض النافذة...")
        main_window.show()
        print("✅ تم عرض النافذة بنجاح")
        
        print("🎉 جميع الخطوات نجحت!")
        print("💡 النافذة مفتوحة الآن - اضغط Ctrl+C للإغلاق")
        
        # تشغيل التطبيق لمدة قصيرة للاختبار
        import time
        time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الخطوة الحالية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_step_by_step()
