# تحسينات تبويب الأصناف - شاشة طلبات الشراء

## نظرة عامة
تم تطبيق التحسينات المطلوبة على تبويب الأصناف في شاشة طلبات الشراء.

## التحسينات المنجزة ✅

### 1. إضافة أعمدة التواريخ
```python
# جدول الأصناف المحدث
self.items_table.setColumnCount(10)
self.items_table.setHorizontalHeaderLabels([
    "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة", 
    "تاريخ الإنتاج", "تاريخ الانتهاء", "الخصم", "الإجمالي", "المسلم", "المتبقي"
])
```

**الأعمدة الجديدة:**
- **العمود 4:** تاريخ الإنتاج
- **العمود 5:** تاريخ الانتهاء

### 2. إصلاح حساب الإجمالي والكمية المتبقية

#### **المشكلة السابقة:**
- عمود الإجمالي لا يتأثر بتغيير الكمية والسعر
- عمود المتبقي يظهر 1 بدلاً من الكمية المدخلة

#### **الحل المطبق:**
```python
def on_item_changed(self, item):
    """معالجة تغيير خلية في جدول الأصناف"""
    if not item:
        return
        
    row = item.row()
    col = item.column()
    
    # إذا تم تغيير الكمية (عمود 2) أو السعر (عمود 3) أو الخصم (عمود 6)
    if col in [2, 3, 6]:
        try:
            # الحصول على القيم
            quantity = float(self.items_table.item(row, 2).text()) if self.items_table.item(row, 2) else 0.0
            unit_price = float(self.items_table.item(row, 3).text()) if self.items_table.item(row, 3) else 0.0
            discount = float(self.items_table.item(row, 6).text()) if self.items_table.item(row, 6) else 0.0
            
            # حساب الإجمالي
            total = (quantity * unit_price) - discount
            
            # تحديث عمود الإجمالي
            self.items_table.item(row, 7).setText(f"{total:.2f}")
            
            # تحديث الكمية المتبقية
            delivered = float(self.items_table.item(row, 8).text()) if self.items_table.item(row, 8) else 0.0
            remaining = quantity - delivered
            self.items_table.item(row, 9).setText(f"{remaining:.2f}")
            
            # تحديث الإجمالي العام
            self.calculate_total()
            
        except ValueError:
            pass
```

### 3. تحديث نافذة تعديل الأصناف

#### **إضافة حقول التواريخ:**
```python
# تاريخ الإنتاج
self.production_date_edit = QDateEdit()
self.production_date_edit.setCalendarPopup(True)
self.production_date_edit.setSpecialValueText("غير محدد")
self.production_date_edit.setDate(QDate.currentDate())
form_layout.addRow("تاريخ الإنتاج:", self.production_date_edit)

# تاريخ الانتهاء
self.expiry_date_edit = QDateEdit()
self.expiry_date_edit.setCalendarPopup(True)
self.expiry_date_edit.setSpecialValueText("غير محدد")
self.expiry_date_edit.setDate(QDate.currentDate().addDays(365))  # سنة من الآن افتراضياً
form_layout.addRow("تاريخ الانتهاء:", self.expiry_date_edit)
```

#### **تحديث دالة تحميل البيانات:**
```python
def load_data(self):
    """تحميل البيانات من الجدول"""
    # ... الحقول الأساسية
    
    # تحميل تاريخ الإنتاج
    production_date_text = self.table.item(self.row, 4).text() if self.table.item(self.row, 4) else ""
    if production_date_text:
        try:
            production_date = QDate.fromString(production_date_text, "yyyy-MM-dd")
            if production_date.isValid():
                self.production_date_edit.setDate(production_date)
        except:
            pass
    
    # تحميل تاريخ الانتهاء
    expiry_date_text = self.table.item(self.row, 5).text() if self.table.item(self.row, 5) else ""
    if expiry_date_text:
        try:
            expiry_date = QDate.fromString(expiry_date_text, "yyyy-MM-dd")
            if expiry_date.isValid():
                self.expiry_date_edit.setDate(expiry_date)
        except:
            pass
```

### 4. تحديث دالة الحفظ

#### **حفظ التواريخ في الجدول:**
```python
def accept(self):
    """قبول التعديلات"""
    # ... الحقول الأساسية
    
    # تحديث تاريخ الإنتاج
    production_date = self.production_date_edit.date().toString("yyyy-MM-dd")
    if self.table.item(self.row, 4):
        self.table.item(self.row, 4).setText(production_date)
    else:
        self.table.setItem(self.row, 4, QTableWidgetItem(production_date))
    
    # تحديث تاريخ الانتهاء
    expiry_date = self.expiry_date_edit.date().toString("yyyy-MM-dd")
    if self.table.item(self.row, 5):
        self.table.item(self.row, 5).setText(expiry_date)
    else:
        self.table.setItem(self.row, 5, QTableWidgetItem(expiry_date))
```

## التخطيط الجديد للجدول

| العمود | الاسم | الوصف |
|--------|-------|--------|
| 0 | كود الصنف | رمز الصنف الفريد |
| 1 | اسم الصنف | اسم الصنف |
| 2 | الكمية | الكمية المطلوبة |
| 3 | سعر الوحدة | سعر الوحدة الواحدة |
| 4 | **تاريخ الإنتاج** | **تاريخ إنتاج الصنف (جديد)** |
| 5 | **تاريخ الانتهاء** | **تاريخ انتهاء صلاحية الصنف (جديد)** |
| 6 | الخصم | مبلغ الخصم |
| 7 | الإجمالي | الإجمالي المحسوب تلقائياً |
| 8 | المسلم | الكمية المسلمة |
| 9 | المتبقي | الكمية المتبقية (محسوبة تلقائياً) |

## المعادلات المطبقة

### حساب الإجمالي:
```
الإجمالي = (الكمية × سعر الوحدة) - الخصم
```

### حساب الكمية المتبقية:
```
المتبقي = الكمية - المسلم
```

## المميزات الجديدة

### 🎯 **حساب تلقائي فوري**
- تحديث الإجمالي عند تغيير الكمية أو السعر أو الخصم
- تحديث الكمية المتبقية عند تغيير الكمية أو المسلم
- تحديث الإجمالي العام للطلب تلقائياً

### 📅 **إدارة التواريخ**
- تاريخ الإنتاج مع تقويم منبثق
- تاريخ الانتهاء مع تقويم منبثق
- قيم افتراضية ذكية (تاريخ اليوم للإنتاج، سنة من الآن للانتهاء)

### 🔧 **معالجة الأخطاء**
- معالجة آمنة للقيم غير الصحيحة
- تحويل تلقائي للنصوص إلى أرقام
- حماية من الأخطاء في التواريخ

### 📊 **تحديث قاعدة البيانات**
- حفظ التواريخ في قاعدة البيانات (يتطلب تحديث النموذج)
- معالجة التواريخ الفارغة
- تحويل صحيح لصيغة التاريخ

## كيفية الاستخدام

### إضافة صنف جديد:
1. انقر على "إضافة صنف"
2. اختر الصنف من القائمة
3. سيتم إضافة الصنف بقيم افتراضية
4. عدل الكمية والسعر حسب الحاجة
5. سيتم حساب الإجمالي تلقائياً

### تعديل صنف موجود:
1. انقر على الصف المطلوب
2. انقر على "تعديل صنف"
3. عدل القيم في النافذة المنبثقة
4. أدخل تواريخ الإنتاج والانتهاء
5. انقر "موافق" لحفظ التغييرات

### التحديث التلقائي:
- عند تغيير الكمية → يتم تحديث الإجمالي والمتبقي
- عند تغيير السعر → يتم تحديث الإجمالي
- عند تغيير الخصم → يتم تحديث الإجمالي
- عند تغيير المسلم → يتم تحديث المتبقي

## الملفات المحدثة

### `src/ui/suppliers/purchase_orders_window.py`
- ✅ تحديث `create_items_tab()` - إضافة أعمدة التواريخ
- ✅ تحديث `add_item()` - دعم الأعمدة الجديدة
- ✅ إضافة `on_item_changed()` - حساب تلقائي
- ✅ تحديث `ItemEditDialog` - حقول التواريخ
- ✅ تحديث `calculate_total()` - الأعمدة الجديدة
- ✅ تحديث `save_order_items()` - حفظ التواريخ

### `test_purchase_orders_enhanced.py`
- ✅ تحديث رسائل الاختبار

---

**تم إنجاز جميع التحسينات المطلوبة بنجاح! ✅**

**المشاكل المحلولة:**
- ✅ إضافة عمودي تاريخ الإنتاج وتاريخ الانتهاء
- ✅ إصلاح حساب الإجمالي التلقائي
- ✅ إصلاح حساب الكمية المتبقية
- ✅ تحديث فوري للحسابات عند التعديل
