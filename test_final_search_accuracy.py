#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل لدقة البحث المحسن
Final Comprehensive Search Accuracy Test
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_complete_search_workflow():
    """اختبار سير العمل الكامل للبحث المحسن"""
    try:
        from src.services.web_scraping_service import WebScrapingService
        
        print("🔍 اختبار سير العمل الكامل للبحث المحسن")
        print("=" * 60)
        
        web_service = WebScrapingService()
        
        # اختبار مع حاوية COSCO
        test_container = "COSU1234567"
        print(f"📦 اختبار مع رقم الحاوية: {test_container}")
        
        # 1. البحث في جميع المصادر
        print("\n🌐 البحث في جميع المصادر...")
        results = await web_service.search_all_carriers(container_number=test_container)
        
        if results:
            print(f"✅ تم العثور على {len(results)} نتيجة")
            
            # 2. عرض تفاصيل النتائج
            print("\n📊 تفاصيل النتائج:")
            for i, result in enumerate(results, 1):
                source = getattr(result, 'source', 'Unknown')
                confidence = getattr(result, 'confidence_score', 0)
                status = getattr(result, 'status', 'Unknown')
                vessel = getattr(result, 'vessel_name', 'Unknown')
                
                print(f"   {i}. {source}")
                print(f"      درجة الثقة: {confidence}%")
                print(f"      الحالة: {status}")
                print(f"      السفينة: {vessel}")
                print()
            
            # 3. استخراج أفضل البيانات
            print("🏆 استخراج أفضل البيانات...")
            best_data = web_service.extract_best_data_enhanced(results)
            
            if best_data:
                print("✅ تم استخراج أفضل البيانات:")
                
                # عرض البيانات المهمة
                important_fields = [
                    'status', 'vessel_name', 'port_of_loading', 
                    'port_of_discharge', 'shipping_method', 'final_destination'
                ]
                
                for field in important_fields:
                    if field in best_data:
                        print(f"   • {field}: {best_data[field]}")
                
                # عرض معلومات الجودة
                quality = best_data.get('data_quality', 'Unknown')
                sources_used = best_data.get('sources_used', [])
                total_sources = best_data.get('total_sources', 0)
                
                print(f"\n📈 معلومات الجودة:")
                print(f"   • جودة البيانات: {quality}")
                print(f"   • المصادر المستخدمة: {len(sources_used)}")
                print(f"   • إجمالي المصادر: {total_sources}")
                
                # 4. التحقق من الحقول المطلوبة
                print("\n✅ التحقق من الحقول المطلوبة:")
                required_fields = [
                    'status', 'vessel_name', 'port_of_loading', 
                    'port_of_discharge', 'shipping_method', 'final_destination'
                ]
                
                missing_fields = []
                for field in required_fields:
                    if field in best_data and best_data[field]:
                        print(f"   ✅ {field}: موجود")
                    else:
                        print(f"   ❌ {field}: مفقود")
                        missing_fields.append(field)
                
                # 5. تقييم النجاح
                success_rate = ((len(required_fields) - len(missing_fields)) / len(required_fields)) * 100
                print(f"\n🎯 معدل النجاح: {success_rate:.1f}%")
                
                if success_rate >= 80:
                    print("🎉 نجح الاختبار بامتياز!")
                    return True
                elif success_rate >= 60:
                    print("✅ نجح الاختبار بشكل جيد")
                    return True
                else:
                    print("⚠️ نجح الاختبار جزئياً")
                    return False
            else:
                print("❌ فشل في استخراج أفضل البيانات")
                return False
        else:
            print("❌ لم يتم العثور على أي نتائج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_carriers():
    """اختبار مع شركات شحن متعددة"""
    try:
        from src.services.web_scraping_service import WebScrapingService
        
        print("\n🚢 اختبار مع شركات شحن متعددة")
        print("=" * 60)
        
        web_service = WebScrapingService()
        
        # حاويات من شركات مختلفة
        test_cases = [
            ("MSCU1234567", "MSC"),
            ("COSU7654321", "COSCO"),
            ("MSKU9876543", "MAERSK")
        ]
        
        success_count = 0
        
        for container, expected_carrier in test_cases:
            print(f"\n📦 اختبار {container} ({expected_carrier}):")
            
            # البحث السريع (مصدر واحد للسرعة)
            fallback_data = web_service._create_enhanced_fallback_data_searates(container, None)
            
            if fallback_data:
                actual_carrier = getattr(fallback_data, 'carrier', 'Unknown')
                confidence = getattr(fallback_data, 'confidence_score', 0)
                status = getattr(fallback_data, 'status', 'Unknown')
                
                print(f"   الشركة: {actual_carrier}")
                print(f"   درجة الثقة: {confidence}%")
                print(f"   الحالة: {status}")
                
                # التحقق من دقة تحديد الشركة
                if expected_carrier in actual_carrier:
                    print(f"   ✅ تحديد الشركة صحيح")
                    success_count += 1
                else:
                    print(f"   ❌ تحديد الشركة خاطئ")
                
                # التحقق من ترجمة الحالة
                if any(char in status for char in 'أبتثجحخدذرزسشصضطظعغفقكلمنهوي'):
                    print(f"   ✅ الحالة مترجمة")
                else:
                    print(f"   ⚠️ الحالة غير مترجمة")
            else:
                print(f"   ❌ فشل في إنشاء البيانات")
        
        success_rate = (success_count / len(test_cases)) * 100
        print(f"\n🎯 معدل نجاح تحديد الشركات: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الشركات المتعددة: {e}")
        return False

async def test_confidence_scoring():
    """اختبار نظام درجة الثقة"""
    try:
        from src.services.web_scraping_service import WebScrapingService, ShipmentData
        
        print("\n📊 اختبار نظام درجة الثقة")
        print("=" * 60)
        
        web_service = WebScrapingService()
        
        # بيانات تجريبية بمستويات جودة مختلفة
        test_cases = [
            {
                'name': 'بيانات كاملة عالية الجودة',
                'data': ShipmentData(
                    container_number="COSU1234567",
                    carrier="COSCO",
                    vessel_name="COSCO SHIPPING PANAMA",
                    status="في الطريق",
                    port_of_loading="Shanghai, China",
                    port_of_discharge="Jeddah, Saudi Arabia",
                    shipping_method="FCL",
                    shipping_type="Sea",
                    final_destination="Jeddah, Saudi Arabia - Port Terminal",
                    tracking_number="TRK234567",
                    source="SeaRates (Real Data)"
                ),
                'expected_range': (80, 100)
            },
            {
                'name': 'بيانات متوسطة الجودة',
                'data': ShipmentData(
                    container_number="MSCU7654321",
                    carrier="MSC",
                    vessel_name="MSC MEDITERRANEAN",
                    status="In Transit",
                    source="Enhanced Data"
                ),
                'expected_range': (40, 70)
            },
            {
                'name': 'بيانات أساسية',
                'data': ShipmentData(
                    container_number="TEST123456",
                    source="Demo Data"
                ),
                'expected_range': (0, 30)
            }
        ]
        
        all_passed = True
        
        for test_case in test_cases:
            name = test_case['name']
            data = test_case['data']
            expected_min, expected_max = test_case['expected_range']
            
            score = web_service.calculate_confidence_score(data)
            
            print(f"\n{name}:")
            print(f"   درجة الثقة: {score:.1f}%")
            print(f"   النطاق المتوقع: {expected_min}-{expected_max}%")
            
            if expected_min <= score <= expected_max:
                print(f"   ✅ ضمن النطاق المتوقع")
            else:
                print(f"   ❌ خارج النطاق المتوقع")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ خطأ في اختبار درجة الثقة: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار النهائي"""
    print("🧪 الاختبار النهائي الشامل لدقة البحث المحسن")
    print("=" * 70)
    
    tests = [
        ("سير العمل الكامل", test_complete_search_workflow),
        ("شركات شحن متعددة", test_multiple_carriers),
        ("نظام درجة الثقة", test_confidence_scoring)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        print("-" * 50)
        
        try:
            if await test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار النهائي:")
    print(f"• الاختبارات الناجحة: {passed}/{total}")
    print(f"• معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت بامتياز!")
        print("\n📋 ملخص التحسينات:")
        print("┌─────────────────────────────────────────────────────────┐")
        print("│ ✅ بحث في 6 مصادر متعددة وموثوقة                      │")
        print("│ ✅ نظام تقييم جودة متقدم مع درجات ثقة دقيقة           │")
        print("│ ✅ بيانات احتياطية واقعية حسب كل شركة شحن            │")
        print("│ ✅ ترجمة وتوحيد شامل لحالات الشحنة                   │")
        print("│ ✅ تحسين شامل لاستخراج البيانات من مصادر مختلفة       │")
        print("│ ✅ خوارزمية ذكية لاختيار أفضل النتائج                │")
        print("└─────────────────────────────────────────────────────────┘")
        print("\n🎯 النظام جاهز للاستخدام الإنتاجي بدقة عالية!")
        
    elif passed >= total * 0.8:
        print("\n⭐ معظم الاختبارات نجحت!")
        print("🔧 النظام يعمل بشكل ممتاز مع تحسينات طفيفة")
        
    else:
        print("\n⚠️ النظام يحتاج مراجعة إضافية")
        print("🔧 بعض التحسينات تحتاج تطوير أكثر")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
