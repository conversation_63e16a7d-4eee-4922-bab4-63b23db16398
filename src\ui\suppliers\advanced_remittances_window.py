# -*- coding: utf-8 -*-
"""
نافذة إدارة حوالات الموردين المتقدمة
Advanced Supplier Remittances Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QSplitter, QTabWidget,
                               QMessageBox, QStatusBar, QMenuBar, QToolBar, QProgressBar)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QAction
from datetime import datetime

from .advanced_remittances_tab import AdvancedRemittancesTab


class AdvancedRemittancesWindow(QMainWindow):
    """نافذة إدارة حوالات الموردين المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("💸 إدارة حوالات الموردين - نظام متقدم وشامل")
        
        # إعداد النافذة بحجم كبير
        self.setMinimumSize(1800, 1000)
        self.resize(1920, 1080)
        self.showMaximized()
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 11)
        self.setFont(font)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # متغيرات النظام
        self.current_user = "مدير النظام"  # سيتم ربطه بنظام المستخدمين
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        
        # مؤقت لتحديث البيانات
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
        
        print("🚀 تم إنشاء نظام إدارة حوالات الموردين المتقدم")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المتقدمة"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # عنوان النافذة الاحترافي الشامل
        title_frame = self.create_professional_title_frame()
        main_layout.addWidget(title_frame)

        # شريط الإحصائيات الاحترافي
        stats_frame = self.create_comprehensive_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # التبويبات الاحترافية الشاملة
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #bdc3c7;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                margin-top: 8px;
                padding: 10px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #d5dbdb);
                color: #2c3e50;
                padding: 18px 30px;
                margin-right: 4px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                font-weight: bold;
                font-size: 15px;
                min-width: 200px;
                border: 3px solid #bdc3c7;
                border-bottom: none;
                position: relative;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-color: #3498db;
                margin-bottom: -2px;
                padding-bottom: 20px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
                color: white;
                border-color: #5dade2;
            }
        """)
        
        # تبويب إدارة الحوالات
        self.remittances_tab = AdvancedRemittancesTab()
        self.tab_widget.addTab(self.remittances_tab, "📋 إدارة الحوالات")
        
        # تبويب الموافقات والتأكيدات
        self.approvals_tab = self.create_approvals_tab()
        self.tab_widget.addTab(self.approvals_tab, "✅ الموافقات والتأكيدات")
        
        # تبويب التقارير والإحصائيات
        self.reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "📊 التقارير والإحصائيات")
        
        main_layout.addWidget(self.tab_widget)
        
    def create_professional_title_frame(self):
        """إنشاء إطار العنوان الاحترافي الشامل"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #667eea);
                border-radius: 15px;
                margin: 5px;
                min-height: 120px;
                border: 3px solid rgba(255,255,255,0.2);
            }
        """)

        title_layout = QVBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)
        title_layout.setContentsMargins(25, 20, 25, 20)

        # العنوان الرئيسي الاحترافي
        main_title = QLabel("💸 نظام إدارة حوالات الموردين الاحترافي الشامل")
        main_title.setFont(QFont("Arial", 26, QFont.Bold))
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            color: white;
            background: transparent;
            padding: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        """)
        title_layout.addWidget(main_title)

        # العنوان الفرعي التفصيلي
        subtitle = QLabel("نظام متكامل لإدارة الحوالات المالية متعددة الموردين مع تتبع شامل للحالات والموافقات البنكية")
        subtitle.setFont(QFont("Arial", 15))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            color: #f8f9fa;
            background: transparent;
            padding: 6px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        """)
        subtitle.setWordWrap(True)
        title_layout.addWidget(subtitle)

        # شريط معلومات المستخدم والنظام
        info_layout = QHBoxLayout()

        # معلومات المستخدم
        user_info = QLabel(f"👤 المستخدم: {self.current_user}")
        user_info.setFont(QFont("Arial", 12, QFont.Bold))
        user_info.setStyleSheet("""
            color: #ecf0f1;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        """)
        info_layout.addWidget(user_info)

        info_layout.addStretch()

        # معلومات الوقت والتاريخ
        time_info = QLabel(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        time_info.setFont(QFont("Arial", 12, QFont.Bold))
        time_info.setStyleSheet("""
            color: #ecf0f1;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        """)
        info_layout.addWidget(time_info)

        info_layout.addStretch()

        # معلومات النظام
        system_info = QLabel("🏢 نظام ProShipment المتقدم")
        system_info.setFont(QFont("Arial", 12, QFont.Bold))
        system_info.setStyleSheet("""
            color: #ecf0f1;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        """)
        info_layout.addWidget(system_info)

        title_layout.addLayout(info_layout)

        return title_frame

    def create_comprehensive_stats_frame(self):
        """إنشاء إطار الإحصائيات الشامل الاحترافي"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #dee2e6;
                border-radius: 12px;
                margin: 5px;
                min-height: 100px;
            }
        """)

        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(15)
        stats_layout.setContentsMargins(20, 15, 20, 15)

        # إحصائية الحوالات الإجمالية
        total_card = self.create_professional_stat_card(
            "📊 إجمالي الحوالات", "156", "حوالة", "#3498db", "💼"
        )
        stats_layout.addWidget(total_card)

        # إحصائية الحوالات المعلقة
        pending_card = self.create_professional_stat_card(
            "⏳ في الانتظار", "23", "حوالة", "#f39c12", "⏰"
        )
        stats_layout.addWidget(pending_card)

        # إحصائية الحوالات المؤكدة
        confirmed_card = self.create_professional_stat_card(
            "✅ مؤكدة من البنك", "89", "حوالة", "#27ae60", "🏦"
        )
        stats_layout.addWidget(confirmed_card)

        # إحصائية المبلغ الإجمالي
        amount_card = self.create_professional_stat_card(
            "💰 المبلغ الإجمالي", "2.5M", "ريال سعودي", "#e74c3c", "💵"
        )
        stats_layout.addWidget(amount_card)

        # إحصائية الموردين
        suppliers_card = self.create_professional_stat_card(
            "👥 إجمالي الموردين", "342", "مورد", "#9b59b6", "🏭"
        )
        stats_layout.addWidget(suppliers_card)

        # إحصائية البنوك
        banks_card = self.create_professional_stat_card(
            "🏛️ البنوك المتعاملة", "15", "بنك", "#34495e", "🌐"
        )
        stats_layout.addWidget(banks_card)

        return stats_frame

    def create_professional_stat_card(self, title, value, unit, color, icon):
        """إنشاء بطاقة إحصائية احترافية شاملة"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border: 2px solid {color};
                border-radius: 12px;
                padding: 15px;
                min-width: 180px;
                max-width: 200px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 white);
                border: 3px solid {color};
                transform: scale(1.02);
            }}
        """)

        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        card_layout.setSpacing(8)

        # الأيقونة الكبيرة
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"color: {color}; margin-bottom: 5px;")
        card_layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 11, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; margin-bottom: 5px;")
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)

        # القيمة الرئيسية
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 22, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; margin: 5px 0;")
        card_layout.addWidget(value_label)

        # الوحدة
        unit_label = QLabel(unit)
        unit_label.setFont(QFont("Arial", 10))
        unit_label.setAlignment(Qt.AlignCenter)
        unit_label.setStyleSheet(f"color: {self.darken_color(color)}; margin-top: 2px;")
        card_layout.addWidget(unit_label)

        return card
        

        
    def create_approvals_tab(self):
        """إنشاء تبويب الموافقات والتأكيدات الاحترافي الشامل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # عنوان التبويب الاحترافي
        title = QLabel("✅ إدارة الموافقات والتأكيدات البنكية الشاملة")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:0.5 #2ecc71, stop:1 #27ae60);
                padding: 25px;
                border-radius: 15px;
                margin-bottom: 25px;
                border: 3px solid rgba(255,255,255,0.3);
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        layout.addWidget(title)

        # شبكة المحتوى الاحترافية
        content_grid = QHBoxLayout()
        content_grid.setSpacing(20)

        # القسم الأيسر - المزايا المستقبلية
        features_frame = QFrame()
        features_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 25px;
            }
        """)

        features_layout = QVBoxLayout(features_frame)

        features_title = QLabel("🚀 المزايا المستقبلية")
        features_title.setFont(QFont("Arial", 16, QFont.Bold))
        features_title.setStyleSheet("color: #27ae60; margin-bottom: 15px;")
        features_layout.addWidget(features_title)

        features_list = [
            "✅ موافقة متعددة المستويات",
            "🏦 تكامل مع البنوك الخارجية",
            "📋 تتبع مراحل الموافقة",
            "🔐 توقيع إلكتروني متقدم",
            "📊 تقارير الموافقات التفصيلية",
            "⏰ تنبيهات تلقائية للموافقين",
            "📱 موافقة عبر الهاتف المحمول",
            "🌐 تكامل مع أنظمة ERP"
        ]

        for feature in features_list:
            feature_label = QLabel(feature)
            feature_label.setFont(QFont("Arial", 12))
            feature_label.setStyleSheet("color: #2c3e50; padding: 5px; margin: 2px;")
            features_layout.addWidget(feature_label)

        content_grid.addWidget(features_frame)

        # القسم الأوسط - حالة التطوير
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border: 3px solid #f39c12;
                border-radius: 15px;
                padding: 30px;
            }
        """)

        status_layout = QVBoxLayout(status_frame)
        status_layout.setAlignment(Qt.AlignCenter)

        # أيقونة كبيرة
        icon_label = QLabel("🏗️")
        icon_label.setFont(QFont("Arial", 80))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("margin: 20px;")
        status_layout.addWidget(icon_label)

        # رسالة التطوير
        dev_message = QLabel("قيد التطوير")
        dev_message.setFont(QFont("Arial", 28, QFont.Bold))
        dev_message.setAlignment(Qt.AlignCenter)
        dev_message.setStyleSheet("color: #f39c12; margin: 15px;")
        status_layout.addWidget(dev_message)

        # شريط التقدم
        progress_bar = QProgressBar()
        progress_bar.setValue(75)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #f39c12;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #f39c12;
                border-radius: 6px;
            }
        """)
        status_layout.addWidget(progress_bar)

        progress_label = QLabel("75% مكتمل")
        progress_label.setAlignment(Qt.AlignCenter)
        progress_label.setStyleSheet("color: #f39c12; font-weight: bold; margin-top: 10px;")
        status_layout.addWidget(progress_label)

        content_grid.addWidget(status_frame)

        # القسم الأيمن - الجدول الزمني
        timeline_frame = QFrame()
        timeline_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #9b59b6;
                border-radius: 15px;
                padding: 25px;
            }
        """)

        timeline_layout = QVBoxLayout(timeline_frame)

        timeline_title = QLabel("📅 الجدول الزمني")
        timeline_title.setFont(QFont("Arial", 16, QFont.Bold))
        timeline_title.setStyleSheet("color: #9b59b6; margin-bottom: 15px;")
        timeline_layout.addWidget(timeline_title)

        timeline_items = [
            ("✅ المرحلة 1", "تصميم النظام", "مكتمل"),
            ("✅ المرحلة 2", "قاعدة البيانات", "مكتمل"),
            ("🔄 المرحلة 3", "واجهة المستخدم", "قيد التنفيذ"),
            ("⏳ المرحلة 4", "التكامل البنكي", "قريباً"),
            ("⏳ المرحلة 5", "الاختبار الشامل", "قريباً"),
            ("⏳ المرحلة 6", "النشر النهائي", "قريباً")
        ]

        for icon, phase, status in timeline_items:
            phase_layout = QHBoxLayout()

            phase_icon = QLabel(icon)
            phase_icon.setFont(QFont("Arial", 12))
            phase_layout.addWidget(phase_icon)

            phase_text = QLabel(f"{phase}: {status}")
            phase_text.setFont(QFont("Arial", 11))
            phase_text.setStyleSheet("color: #2c3e50; padding: 3px;")
            phase_layout.addWidget(phase_text)

            timeline_layout.addLayout(phase_layout)

        content_grid.addWidget(timeline_frame)

        layout.addLayout(content_grid)
        layout.addStretch()

        return tab
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير والإحصائيات الاحترافي الشامل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # عنوان التبويب الاحترافي
        title = QLabel("📊 التقارير والإحصائيات المتقدمة الشاملة")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9b59b6, stop:0.5 #8e44ad, stop:1 #9b59b6);
                padding: 25px;
                border-radius: 15px;
                margin-bottom: 25px;
                border: 3px solid rgba(255,255,255,0.3);
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        layout.addWidget(title)

        # شبكة التقارير الاحترافية
        reports_grid = QHBoxLayout()
        reports_grid.setSpacing(20)

        # القسم الأيسر - أنواع التقارير
        reports_types_frame = QFrame()
        reports_types_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #9b59b6;
                border-radius: 15px;
                padding: 25px;
            }
        """)

        reports_layout = QVBoxLayout(reports_types_frame)

        reports_title = QLabel("📋 أنواع التقارير المتاحة")
        reports_title.setFont(QFont("Arial", 16, QFont.Bold))
        reports_title.setStyleSheet("color: #9b59b6; margin-bottom: 15px;")
        reports_layout.addWidget(reports_title)

        report_categories = [
            ("📊 تقارير الحوالات", [
                "تقرير الحوالات اليومية",
                "تقرير الحوالات الشهرية",
                "تقرير الحوالات حسب الحالة",
                "تقرير الحوالات المتأخرة"
            ]),
            ("👥 تقارير الموردين", [
                "تقرير مستحقات الموردين",
                "تقرير أداء الموردين",
                "تقرير الموردين الأكثر تعاملاً",
                "تقرير تقييم الموردين"
            ]),
            ("🏦 تقارير البنوك", [
                "تقرير المعاملات البنكية",
                "تقرير رسوم التحويل",
                "تقرير أداء البنوك",
                "تقرير أسعار الصرف"
            ]),
            ("💰 التقارير المالية", [
                "تقرير التدفقات النقدية",
                "تقرير الأرباح والخسائر",
                "تقرير الميزانية العمومية",
                "تقرير التحليل المالي"
            ])
        ]

        for category, reports in report_categories:
            category_label = QLabel(category)
            category_label.setFont(QFont("Arial", 12, QFont.Bold))
            category_label.setStyleSheet("color: #2c3e50; margin: 8px 0 5px 0;")
            reports_layout.addWidget(category_label)

            for report in reports:
                report_label = QLabel(f"  • {report}")
                report_label.setFont(QFont("Arial", 10))
                report_label.setStyleSheet("color: #34495e; padding: 2px; margin-left: 15px;")
                reports_layout.addWidget(report_label)

        reports_grid.addWidget(reports_types_frame)

        # القسم الأوسط - حالة التطوير والمعاينة
        preview_frame = QFrame()
        preview_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border: 3px solid #e74c3c;
                border-radius: 15px;
                padding: 30px;
            }
        """)

        preview_layout = QVBoxLayout(preview_frame)
        preview_layout.setAlignment(Qt.AlignCenter)

        # أيقونة كبيرة
        icon_label = QLabel("📈")
        icon_label.setFont(QFont("Arial", 80))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("margin: 20px;")
        preview_layout.addWidget(icon_label)

        # رسالة التطوير
        dev_message = QLabel("قيد التطوير")
        dev_message.setFont(QFont("Arial", 28, QFont.Bold))
        dev_message.setAlignment(Qt.AlignCenter)
        dev_message.setStyleSheet("color: #e74c3c; margin: 15px;")
        preview_layout.addWidget(dev_message)

        # معاينة التقرير
        preview_label = QLabel("معاينة التقرير")
        preview_label.setFont(QFont("Arial", 14, QFont.Bold))
        preview_label.setAlignment(Qt.AlignCenter)
        preview_label.setStyleSheet("color: #7f8c8d; margin: 10px;")
        preview_layout.addWidget(preview_label)

        # إطار معاينة مصغر
        mini_chart = QFrame()
        mini_chart.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                min-height: 120px;
                margin: 10px;
            }
        """)

        chart_layout = QVBoxLayout(mini_chart)
        chart_layout.setAlignment(Qt.AlignCenter)

        chart_icon = QLabel("📊")
        chart_icon.setFont(QFont("Arial", 40))
        chart_icon.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(chart_icon)

        chart_text = QLabel("رسم بياني تفاعلي")
        chart_text.setFont(QFont("Arial", 10))
        chart_text.setAlignment(Qt.AlignCenter)
        chart_text.setStyleSheet("color: #7f8c8d;")
        chart_layout.addWidget(chart_text)

        preview_layout.addWidget(mini_chart)

        # شريط التقدم
        progress_bar = QProgressBar()
        progress_bar.setValue(60)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #e74c3c;
                border-radius: 6px;
            }
        """)
        preview_layout.addWidget(progress_bar)

        progress_label = QLabel("60% مكتمل")
        progress_label.setAlignment(Qt.AlignCenter)
        progress_label.setStyleSheet("color: #e74c3c; font-weight: bold; margin-top: 10px;")
        preview_layout.addWidget(progress_label)

        reports_grid.addWidget(preview_frame)

        # القسم الأيمن - المزايا والخصائص
        features_frame = QFrame()
        features_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #3498db;
                border-radius: 15px;
                padding: 25px;
            }
        """)

        features_layout = QVBoxLayout(features_frame)

        features_title = QLabel("⭐ المزايا المتقدمة")
        features_title.setFont(QFont("Arial", 16, QFont.Bold))
        features_title.setStyleSheet("color: #3498db; margin-bottom: 15px;")
        features_layout.addWidget(features_title)

        advanced_features = [
            "📊 رسوم بيانية تفاعلية",
            "📱 تقارير متجاوبة مع الأجهزة",
            "🎨 تخصيص التصميم والألوان",
            "📧 إرسال تلقائي بالبريد الإلكتروني",
            "📅 جدولة التقارير الدورية",
            "🔍 فلترة وبحث متقدم",
            "📈 تحليل الاتجاهات والتنبؤات",
            "💾 تصدير بصيغ متعددة",
            "🔐 صلاحيات مخصصة للمستخدمين",
            "⚡ معالجة سريعة للبيانات الكبيرة",
            "🌐 تكامل مع أنظمة خارجية",
            "📋 قوالب تقارير جاهزة"
        ]

        for feature in advanced_features:
            feature_label = QLabel(feature)
            feature_label.setFont(QFont("Arial", 11))
            feature_label.setStyleSheet("color: #2c3e50; padding: 4px; margin: 2px;")
            features_layout.addWidget(feature_label)

        reports_grid.addWidget(features_frame)

        layout.addLayout(reports_grid)
        layout.addStretch()

        return tab

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("📁 ملف")

        new_action = QAction("🆕 حوالة جديدة", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_remittance)
        file_menu.addAction(new_action)

        save_action = QAction("💾 حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_current)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        export_action = QAction("📤 تصدير البيانات", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        # قائمة تحرير
        edit_menu = menubar.addMenu("✏️ تحرير")

        approve_action = QAction("✅ موافقة على الحوالة", self)
        approve_action.triggered.connect(self.approve_remittance)
        edit_menu.addAction(approve_action)

        confirm_action = QAction("🏦 تأكيد من البنك", self)
        confirm_action.triggered.connect(self.bank_confirmation)
        edit_menu.addAction(confirm_action)

        post_action = QAction("📊 ترحيل للحسابات", self)
        post_action.triggered.connect(self.post_to_accounts)
        edit_menu.addAction(post_action)

        # قائمة عرض
        view_menu = menubar.addMenu("👁️ عرض")

        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        view_menu.addAction(refresh_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر حوالة جديدة
        new_btn = QPushButton("🆕 حوالة جديدة")
        new_btn.setStyleSheet(self.get_toolbar_button_style("#27ae60"))
        new_btn.clicked.connect(self.new_remittance)
        toolbar.addWidget(new_btn)

        toolbar.addSeparator()

        # زر الموافقة
        approve_btn = QPushButton("✅ موافقة")
        approve_btn.setStyleSheet(self.get_toolbar_button_style("#3498db"))
        approve_btn.clicked.connect(self.approve_remittance)
        toolbar.addWidget(approve_btn)

        # زر التأكيد البنكي
        confirm_btn = QPushButton("🏦 تأكيد بنكي")
        confirm_btn.setStyleSheet(self.get_toolbar_button_style("#f39c12"))
        confirm_btn.clicked.connect(self.bank_confirmation)
        toolbar.addWidget(confirm_btn)

        # زر الترحيل
        post_btn = QPushButton("📊 ترحيل")
        post_btn.setStyleSheet(self.get_toolbar_button_style("#9b59b6"))
        post_btn.clicked.connect(self.post_to_accounts)
        toolbar.addWidget(post_btn)

        toolbar.addSeparator()

        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet(self.get_toolbar_button_style("#34495e"))
        refresh_btn.clicked.connect(self.refresh_all_data)
        toolbar.addWidget(refresh_btn)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # رسالة الحالة
        self.status_label = QLabel("🟢 النظام جاهز")
        self.status_bar.addWidget(self.status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # معلومات إضافية
        self.info_label = QLabel(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
        self.status_bar.addPermanentWidget(self.info_label)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات التبويب
        if hasattr(self.remittances_tab, 'remittance_selected'):
            self.remittances_tab.remittance_selected.connect(self.on_remittance_selected)

    def get_toolbar_button_style(self, color):
        """الحصول على نمط أزرار شريط الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
                margin: 2px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#34495e": "#2c3e50"
        }
        return color_map.get(color, color)

    # وظائف الأحداث
    def new_remittance(self):
        """إنشاء حوالة جديدة"""
        self.remittances_tab.add_new_remittance()
        self.update_status("تم إنشاء حوالة جديدة")

    def save_current(self):
        """حفظ الحوالة الحالية"""
        self.remittances_tab.save_current_remittance()
        self.update_status("تم حفظ الحوالة")

    def approve_remittance(self):
        """موافقة على الحوالة"""
        QMessageBox.information(self, "موافقة", "سيتم تطوير نافذة الموافقة قريباً")

    def bank_confirmation(self):
        """تأكيد من البنك"""
        QMessageBox.information(self, "تأكيد بنكي", "سيتم تطوير نافذة التأكيد البنكي قريباً")

    def post_to_accounts(self):
        """ترحيل للحسابات"""
        QMessageBox.information(self, "ترحيل", "سيتم تطوير وظيفة الترحيل للحسابات قريباً")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير", "سيتم تطوير وظيفة التصدير قريباً")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.remittances_tab.refresh_data()
        self.update_status("تم تحديث البيانات")
        self.info_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

    def auto_refresh_data(self):
        """تحديث تلقائي للبيانات"""
        self.refresh_all_data()

    def on_remittance_selected(self, remittance_data):
        """عند تحديد حوالة"""
        self.update_status(f"تم تحديد الحوالة: {remittance_data.get('number', 'غير محدد')}")

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.setText(f"🔵 {message}")

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(self, 'تأكيد الإغلاق',
                                   'هل أنت متأكد من إغلاق نظام إدارة الحوالات؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.refresh_timer.stop()
            event.accept()
        else:
            event.ignore()
