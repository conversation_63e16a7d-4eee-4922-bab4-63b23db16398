# -*- coding: utf-8 -*-
"""
نافذة إدارة حوالات الموردين المتقدمة
Advanced Supplier Remittances Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QSplitter, QTabWidget,
                               QMessageBox, QStatusBar, QMenuBar, QToolBar, QProgressBar)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QAction
from datetime import datetime

from .advanced_remittances_tab import AdvancedRemittancesTab


class AdvancedRemittancesWindow(QMainWindow):
    """نافذة إدارة حوالات الموردين المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("💸 إدارة حوالات الموردين - نظام متقدم وشامل")
        
        # إعداد النافذة بحجم كبير
        self.setMinimumSize(1800, 1000)
        self.resize(1920, 1080)
        self.showMaximized()
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 11)
        self.setFont(font)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # متغيرات النظام
        self.current_user = "مدير النظام"  # سيتم ربطه بنظام المستخدمين
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        
        # مؤقت لتحديث البيانات
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
        
        print("🚀 تم إنشاء نظام إدارة حوالات الموردين المتقدم")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المتقدمة"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # عنوان النافذة المتقدم
        title_frame = self.create_title_frame()
        main_layout.addWidget(title_frame)
        
        # شريط الإحصائيات السريعة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # التبويبات المتقدمة
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)
        
        # تبويب إدارة الحوالات
        self.remittances_tab = AdvancedRemittancesTab()
        self.tab_widget.addTab(self.remittances_tab, "📋 إدارة الحوالات")
        
        # تبويب الموافقات والتأكيدات
        self.approvals_tab = self.create_approvals_tab()
        self.tab_widget.addTab(self.approvals_tab, "✅ الموافقات والتأكيدات")
        
        # تبويب التقارير والإحصائيات
        self.reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "📊 التقارير والإحصائيات")
        
        main_layout.addWidget(self.tab_widget)
        
    def create_title_frame(self):
        """إنشاء إطار العنوان المتقدم"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 15px;
                margin: 5px;
                min-height: 100px;
            }
        """)
        
        title_layout = QVBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)
        
        # العنوان الرئيسي
        main_title = QLabel("💸 نظام إدارة حوالات الموردين المتقدم")
        main_title.setFont(QFont("Arial", 24, QFont.Bold))
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("color: white; background: transparent; padding: 8px;")
        title_layout.addWidget(main_title)
        
        # العنوان الفرعي
        subtitle = QLabel("نظام شامل لإدارة الحوالات المالية مع تتبع متقدم للحالات والموافقات")
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #f8f9fa; background: transparent; padding: 4px;")
        title_layout.addWidget(subtitle)
        
        # معلومات المستخدم والوقت
        user_info = QLabel(f"👤 المستخدم: {self.current_user} | 🕐 {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        user_info.setFont(QFont("Arial", 12))
        user_info.setAlignment(Qt.AlignCenter)
        user_info.setStyleSheet("color: #ecf0f1; background: transparent; padding: 4px;")
        title_layout.addWidget(user_info)
        
        return title_frame
        
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات السريعة"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                margin: 5px;
                min-height: 80px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)
        stats_layout.setContentsMargins(20, 15, 20, 15)
        
        # إحصائية الحوالات الإجمالية
        total_card = self.create_stat_card("📊 إجمالي الحوالات", "156", "#3498db")
        stats_layout.addWidget(total_card)
        
        # إحصائية الحوالات المعلقة
        pending_card = self.create_stat_card("⏳ في الانتظار", "23", "#f39c12")
        stats_layout.addWidget(pending_card)
        
        # إحصائية الحوالات المؤكدة
        confirmed_card = self.create_stat_card("✅ مؤكدة", "89", "#27ae60")
        stats_layout.addWidget(confirmed_card)
        
        # إحصائية المبلغ الإجمالي
        amount_card = self.create_stat_card("💰 المبلغ الإجمالي", "2.5M ريال", "#e74c3c")
        stats_layout.addWidget(amount_card)
        
        return stats_frame
        
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; margin-bottom: 5px;")
        card_layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        card_layout.addWidget(value_label)
        
        return card
        
    def create_approvals_tab(self):
        """إنشاء تبويب الموافقات والتأكيدات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # عنوان التبويب
        title = QLabel("✅ إدارة الموافقات والتأكيدات البنكية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # محتوى مؤقت
        content = QLabel("🚧 قيد التطوير - سيتم إضافة وظائف الموافقات والتأكيدات البنكية")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 50px;")
        layout.addWidget(content)
        
        return tab
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # عنوان التبويب
        title = QLabel("📊 التقارير والإحصائيات المتقدمة")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # محتوى مؤقت
        content = QLabel("🚧 قيد التطوير - سيتم إضافة التقارير والإحصائيات المتقدمة")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 50px;")
        layout.addWidget(content)
        
        return tab

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("📁 ملف")

        new_action = QAction("🆕 حوالة جديدة", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_remittance)
        file_menu.addAction(new_action)

        save_action = QAction("💾 حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_current)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        export_action = QAction("📤 تصدير البيانات", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        # قائمة تحرير
        edit_menu = menubar.addMenu("✏️ تحرير")

        approve_action = QAction("✅ موافقة على الحوالة", self)
        approve_action.triggered.connect(self.approve_remittance)
        edit_menu.addAction(approve_action)

        confirm_action = QAction("🏦 تأكيد من البنك", self)
        confirm_action.triggered.connect(self.bank_confirmation)
        edit_menu.addAction(confirm_action)

        post_action = QAction("📊 ترحيل للحسابات", self)
        post_action.triggered.connect(self.post_to_accounts)
        edit_menu.addAction(post_action)

        # قائمة عرض
        view_menu = menubar.addMenu("👁️ عرض")

        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        view_menu.addAction(refresh_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر حوالة جديدة
        new_btn = QPushButton("🆕 حوالة جديدة")
        new_btn.setStyleSheet(self.get_toolbar_button_style("#27ae60"))
        new_btn.clicked.connect(self.new_remittance)
        toolbar.addWidget(new_btn)

        toolbar.addSeparator()

        # زر الموافقة
        approve_btn = QPushButton("✅ موافقة")
        approve_btn.setStyleSheet(self.get_toolbar_button_style("#3498db"))
        approve_btn.clicked.connect(self.approve_remittance)
        toolbar.addWidget(approve_btn)

        # زر التأكيد البنكي
        confirm_btn = QPushButton("🏦 تأكيد بنكي")
        confirm_btn.setStyleSheet(self.get_toolbar_button_style("#f39c12"))
        confirm_btn.clicked.connect(self.bank_confirmation)
        toolbar.addWidget(confirm_btn)

        # زر الترحيل
        post_btn = QPushButton("📊 ترحيل")
        post_btn.setStyleSheet(self.get_toolbar_button_style("#9b59b6"))
        post_btn.clicked.connect(self.post_to_accounts)
        toolbar.addWidget(post_btn)

        toolbar.addSeparator()

        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet(self.get_toolbar_button_style("#34495e"))
        refresh_btn.clicked.connect(self.refresh_all_data)
        toolbar.addWidget(refresh_btn)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # رسالة الحالة
        self.status_label = QLabel("🟢 النظام جاهز")
        self.status_bar.addWidget(self.status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # معلومات إضافية
        self.info_label = QLabel(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
        self.status_bar.addPermanentWidget(self.info_label)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات التبويب
        if hasattr(self.remittances_tab, 'remittance_selected'):
            self.remittances_tab.remittance_selected.connect(self.on_remittance_selected)

    def get_toolbar_button_style(self, color):
        """الحصول على نمط أزرار شريط الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
                margin: 2px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#34495e": "#2c3e50"
        }
        return color_map.get(color, color)

    # وظائف الأحداث
    def new_remittance(self):
        """إنشاء حوالة جديدة"""
        self.remittances_tab.add_new_remittance()
        self.update_status("تم إنشاء حوالة جديدة")

    def save_current(self):
        """حفظ الحوالة الحالية"""
        self.remittances_tab.save_current_remittance()
        self.update_status("تم حفظ الحوالة")

    def approve_remittance(self):
        """موافقة على الحوالة"""
        QMessageBox.information(self, "موافقة", "سيتم تطوير نافذة الموافقة قريباً")

    def bank_confirmation(self):
        """تأكيد من البنك"""
        QMessageBox.information(self, "تأكيد بنكي", "سيتم تطوير نافذة التأكيد البنكي قريباً")

    def post_to_accounts(self):
        """ترحيل للحسابات"""
        QMessageBox.information(self, "ترحيل", "سيتم تطوير وظيفة الترحيل للحسابات قريباً")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير", "سيتم تطوير وظيفة التصدير قريباً")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.remittances_tab.refresh_data()
        self.update_status("تم تحديث البيانات")
        self.info_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

    def auto_refresh_data(self):
        """تحديث تلقائي للبيانات"""
        self.refresh_all_data()

    def on_remittance_selected(self, remittance_data):
        """عند تحديد حوالة"""
        self.update_status(f"تم تحديد الحوالة: {remittance_data.get('number', 'غير محدد')}")

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.setText(f"🔵 {message}")

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(self, 'تأكيد الإغلاق',
                                   'هل أنت متأكد من إغلاق نظام إدارة الحوالات؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.refresh_timer.stop()
            event.accept()
        else:
            event.ignore()
