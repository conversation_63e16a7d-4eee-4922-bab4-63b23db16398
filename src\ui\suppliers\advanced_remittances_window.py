# -*- coding: utf-8 -*-
"""
نافذة إدارة حوالات الموردين المتقدمة
Advanced Supplier Remittances Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QSplitter, QTabWidget,
                               QMessageBox, QStatusBar, QMenuBar, QToolBar, QProgressBar)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QAction
from datetime import datetime

from .advanced_remittances_tab import AdvancedRemittancesTab


class AdvancedRemittancesWindow(QMainWindow):
    """نافذة إدارة حوالات الموردين المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("💸 إدارة حوالات الموردين - نظام متقدم وشامل")
        
        # إعداد النافذة بحجم كبير
        self.setMinimumSize(1800, 1000)
        self.resize(1920, 1080)
        self.showMaximized()
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 11)
        self.setFont(font)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # متغيرات النظام
        self.current_user = "مدير النظام"  # سيتم ربطه بنظام المستخدمين
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        
        # مؤقت لتحديث البيانات
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
        
        print("🚀 تم إنشاء نظام إدارة حوالات الموردين المتقدم")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المتقدمة"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # عنوان النافذة المحسن
        title_frame = self.create_title_frame()
        main_layout.addWidget(title_frame)
        
        # التبويبات المحسنة
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                background-color: white;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 15px 25px;
                margin-right: 3px;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 180px;
                border: 2px solid #bdc3c7;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                border-color: #3498db;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
                border-color: #5dade2;
            }
        """)
        
        # تبويب إدارة الحوالات
        self.remittances_tab = AdvancedRemittancesTab()
        self.tab_widget.addTab(self.remittances_tab, "📋 إدارة الحوالات")
        
        # تبويب الموافقات والتأكيدات
        self.approvals_tab = self.create_approvals_tab()
        self.tab_widget.addTab(self.approvals_tab, "✅ الموافقات والتأكيدات")
        
        # تبويب التقارير والإحصائيات
        self.reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "📊 التقارير والإحصائيات")
        
        main_layout.addWidget(self.tab_widget)
        
    def create_title_frame(self):
        """إنشاء إطار العنوان المحسن"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 12px;
                margin: 5px;
                min-height: 70px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)

        # العنوان الرئيسي
        main_title = QLabel("💸 نظام إدارة حوالات الموردين المتقدم")
        main_title.setFont(QFont("Arial", 20, QFont.Bold))
        main_title.setStyleSheet("color: white; background: transparent;")
        title_layout.addWidget(main_title)

        # مساحة مرنة
        title_layout.addStretch()

        # معلومات المستخدم والوقت
        user_info = QLabel(f"👤 {self.current_user} | 🕐 {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        user_info.setFont(QFont("Arial", 12))
        user_info.setStyleSheet("color: #ecf0f1; background: transparent;")
        title_layout.addWidget(user_info)

        return title_frame
        

        
    def create_approvals_tab(self):
        """إنشاء تبويب الموافقات والتأكيدات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # عنوان التبويب
        title = QLabel("✅ إدارة الموافقات والتأكيدات البنكية")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)

        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 30px;
            }
        """)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setAlignment(Qt.AlignCenter)

        # أيقونة كبيرة
        icon_label = QLabel("🏗️")
        icon_label.setFont(QFont("Arial", 72))
        icon_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(icon_label)

        # رسالة التطوير
        dev_message = QLabel("قيد التطوير")
        dev_message.setFont(QFont("Arial", 24, QFont.Bold))
        dev_message.setAlignment(Qt.AlignCenter)
        dev_message.setStyleSheet("color: #7f8c8d; margin: 20px;")
        content_layout.addWidget(dev_message)

        # وصف مفصل
        description = QLabel("سيتم إضافة وظائف الموافقات والتأكيدات البنكية المتقدمة قريباً")
        description.setFont(QFont("Arial", 14))
        description.setAlignment(Qt.AlignCenter)
        description.setStyleSheet("color: #95a5a6; margin: 10px;")
        description.setWordWrap(True)
        content_layout.addWidget(description)

        layout.addWidget(content_frame)
        layout.addStretch()

        return tab
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # عنوان التبويب
        title = QLabel("📊 التقارير والإحصائيات المتقدمة")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)

        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 30px;
            }
        """)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setAlignment(Qt.AlignCenter)

        # أيقونة كبيرة
        icon_label = QLabel("📈")
        icon_label.setFont(QFont("Arial", 72))
        icon_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(icon_label)

        # رسالة التطوير
        dev_message = QLabel("قيد التطوير")
        dev_message.setFont(QFont("Arial", 24, QFont.Bold))
        dev_message.setAlignment(Qt.AlignCenter)
        dev_message.setStyleSheet("color: #7f8c8d; margin: 20px;")
        content_layout.addWidget(dev_message)

        # وصف مفصل
        description = QLabel("سيتم إضافة التقارير والإحصائيات المتقدمة للحوالات قريباً")
        description.setFont(QFont("Arial", 14))
        description.setAlignment(Qt.AlignCenter)
        description.setStyleSheet("color: #95a5a6; margin: 10px;")
        description.setWordWrap(True)
        content_layout.addWidget(description)

        layout.addWidget(content_frame)
        layout.addStretch()

        return tab

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("📁 ملف")

        new_action = QAction("🆕 حوالة جديدة", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_remittance)
        file_menu.addAction(new_action)

        save_action = QAction("💾 حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_current)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        export_action = QAction("📤 تصدير البيانات", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        # قائمة تحرير
        edit_menu = menubar.addMenu("✏️ تحرير")

        approve_action = QAction("✅ موافقة على الحوالة", self)
        approve_action.triggered.connect(self.approve_remittance)
        edit_menu.addAction(approve_action)

        confirm_action = QAction("🏦 تأكيد من البنك", self)
        confirm_action.triggered.connect(self.bank_confirmation)
        edit_menu.addAction(confirm_action)

        post_action = QAction("📊 ترحيل للحسابات", self)
        post_action.triggered.connect(self.post_to_accounts)
        edit_menu.addAction(post_action)

        # قائمة عرض
        view_menu = menubar.addMenu("👁️ عرض")

        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        view_menu.addAction(refresh_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر حوالة جديدة
        new_btn = QPushButton("🆕 حوالة جديدة")
        new_btn.setStyleSheet(self.get_toolbar_button_style("#27ae60"))
        new_btn.clicked.connect(self.new_remittance)
        toolbar.addWidget(new_btn)

        toolbar.addSeparator()

        # زر الموافقة
        approve_btn = QPushButton("✅ موافقة")
        approve_btn.setStyleSheet(self.get_toolbar_button_style("#3498db"))
        approve_btn.clicked.connect(self.approve_remittance)
        toolbar.addWidget(approve_btn)

        # زر التأكيد البنكي
        confirm_btn = QPushButton("🏦 تأكيد بنكي")
        confirm_btn.setStyleSheet(self.get_toolbar_button_style("#f39c12"))
        confirm_btn.clicked.connect(self.bank_confirmation)
        toolbar.addWidget(confirm_btn)

        # زر الترحيل
        post_btn = QPushButton("📊 ترحيل")
        post_btn.setStyleSheet(self.get_toolbar_button_style("#9b59b6"))
        post_btn.clicked.connect(self.post_to_accounts)
        toolbar.addWidget(post_btn)

        toolbar.addSeparator()

        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet(self.get_toolbar_button_style("#34495e"))
        refresh_btn.clicked.connect(self.refresh_all_data)
        toolbar.addWidget(refresh_btn)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # رسالة الحالة
        self.status_label = QLabel("🟢 النظام جاهز")
        self.status_bar.addWidget(self.status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # معلومات إضافية
        self.info_label = QLabel(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
        self.status_bar.addPermanentWidget(self.info_label)

    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات التبويب
        if hasattr(self.remittances_tab, 'remittance_selected'):
            self.remittances_tab.remittance_selected.connect(self.on_remittance_selected)

    def get_toolbar_button_style(self, color):
        """الحصول على نمط أزرار شريط الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
                margin: 2px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#34495e": "#2c3e50"
        }
        return color_map.get(color, color)

    # وظائف الأحداث
    def new_remittance(self):
        """إنشاء حوالة جديدة"""
        self.remittances_tab.add_new_remittance()
        self.update_status("تم إنشاء حوالة جديدة")

    def save_current(self):
        """حفظ الحوالة الحالية"""
        self.remittances_tab.save_current_remittance()
        self.update_status("تم حفظ الحوالة")

    def approve_remittance(self):
        """موافقة على الحوالة"""
        QMessageBox.information(self, "موافقة", "سيتم تطوير نافذة الموافقة قريباً")

    def bank_confirmation(self):
        """تأكيد من البنك"""
        QMessageBox.information(self, "تأكيد بنكي", "سيتم تطوير نافذة التأكيد البنكي قريباً")

    def post_to_accounts(self):
        """ترحيل للحسابات"""
        QMessageBox.information(self, "ترحيل", "سيتم تطوير وظيفة الترحيل للحسابات قريباً")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير", "سيتم تطوير وظيفة التصدير قريباً")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.remittances_tab.refresh_data()
        self.update_status("تم تحديث البيانات")
        self.info_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

    def auto_refresh_data(self):
        """تحديث تلقائي للبيانات"""
        self.refresh_all_data()

    def on_remittance_selected(self, remittance_data):
        """عند تحديد حوالة"""
        self.update_status(f"تم تحديد الحوالة: {remittance_data.get('number', 'غير محدد')}")

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.setText(f"🔵 {message}")

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(self, 'تأكيد الإغلاق',
                                   'هل أنت متأكد من إغلاق نظام إدارة الحوالات؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.refresh_timer.stop()
            event.accept()
        else:
            event.ignore()
