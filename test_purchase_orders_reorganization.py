#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إعادة تنظيم نافذة طلبات الشراء
Test Purchase Orders Window Reorganization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def test_purchase_orders_reorganization():
    """اختبار إعادة تنظيم نافذة طلبات الشراء"""
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        print("🔄 بدء اختبار إعادة تنظيم نافذة طلبات الشراء...")
        
        # إنشاء النافذة
        window = PurchaseOrdersWindow()
        window.show()
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # التحقق من وجود الزر الجديد في شريط الأدوات
        toolbar = window.findChild(window.__class__, 'toolbar')
        if toolbar:
            actions = toolbar.actions()
            list_action_found = False
            for action in actions:
                if "قائمة الطلبات" in action.text():
                    list_action_found = True
                    print("✅ تم العثور على زر قائمة الطلبات في شريط الأدوات")
                    break
            
            if not list_action_found:
                print("❌ لم يتم العثور على زر قائمة الطلبات")
        
        # التحقق من وجود دالة show_orders_list
        if hasattr(window, 'show_orders_list'):
            print("✅ تم إنشاء دالة show_orders_list")
            
            # اختبار فتح نافذة قائمة الطلبات
            try:
                window.show_orders_list()
                print("✅ تم فتح نافذة قائمة الطلبات بنجاح")
                
                # التحقق من وجود النافذة الفرعية
                if hasattr(window, 'orders_list_window') and window.orders_list_window:
                    list_window = window.orders_list_window
                    print("✅ تم إنشاء نافذة قائمة الطلبات المنفصلة")
                    
                    # التحقق من عناصر النافذة الفرعية
                    if hasattr(list_window, 'orders_table'):
                        print("✅ تم إنشاء جدول الطلبات في النافذة المنفصلة")
                    
                    if hasattr(list_window, 'search_edit'):
                        print("✅ تم إنشاء حقل البحث")
                    
                    if hasattr(list_window, 'stat_labels'):
                        print("✅ تم إنشاء بطاقات الإحصائيات")
                    
                    # إغلاق النافذة الفرعية
                    list_window.close()
                    print("✅ تم إغلاق نافذة قائمة الطلبات")
                
            except Exception as e:
                print(f"❌ خطأ في فتح نافذة قائمة الطلبات: {e}")
        else:
            print("❌ لم يتم العثور على دالة show_orders_list")
        
        # التحقق من التخطيط الجديد (إدخال الطلبات فقط)
        if hasattr(window, 'details_tabs'):
            print("✅ تم إنشاء تبويبات إدخال الطلبات")
        
        # التحقق من عدم وجود جدول الطلبات في النافذة الرئيسية
        if not hasattr(window, 'orders_table'):
            print("✅ تم إزالة جدول الطلبات من النافذة الرئيسية بنجاح")
        else:
            print("⚠️ جدول الطلبات ما زال موجود في النافذة الرئيسية")
        
        # التحقق من دالة load_order_by_number
        if hasattr(window, 'load_order_by_number'):
            print("✅ تم إنشاء دالة load_order_by_number")
        else:
            print("❌ لم يتم العثور على دالة load_order_by_number")
        
        print("\n📋 ملخص الاختبار:")
        print("✅ تم إعادة تنظيم نافذة طلبات الشراء بنجاح")
        print("✅ النافذة الرئيسية تركز على إدخال الطلبات فقط")
        print("✅ تم إنشاء نافذة منفصلة لقائمة الطلبات")
        print("✅ تم إضافة زر قائمة الطلبات في شريط الأدوات")
        print("✅ تم تنفيذ جميع الوظائف المطلوبة")
        
        # إغلاق النافذة
        window.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    app.quit()

if __name__ == "__main__":
    test_purchase_orders_reorganization()
