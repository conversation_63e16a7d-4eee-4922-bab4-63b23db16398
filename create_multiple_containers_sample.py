#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف إكسيل نموذجي مع حاويات متعددة لاختبار ميزة الاستيراد المحسنة
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_multiple_containers_sample():
    """إنشاء ملف إكسيل نموذجي مع حاويات متعددة"""
    try:
        print("🔄 إنشاء ملف إكسيل نموذجي مع حاويات متعددة...")
        
        # إنشاء بيانات نموذجية مع حاويات متعددة
        data = {
            'التاريخ': [
                '2025-07-06',
                '2025-07-07', 
                '2025-07-08',
                '2025-07-09'
            ],
            'المورد': [
                'شركة الإمارات للتجارة',
                'شركة الكويت التجارية', 
                'مؤسسة البحرين للاستيراد',
                'شركة قطر للتصدير'
            ],
            'بوليصة الشحن': [
                'BOL-2025-001',
                'BOL-2025-002',
                'BOL-2025-003', 
                'BOL-2025-004'
            ],
            'ملاحظات': [
                'شحنة مستوردة من دبي - حاويات متعددة',
                'شحنة عاجلة من الكويت',
                'شحنة منتجات إلكترونية',
                'شحنة مواد غذائية'
            ],
            'حالة الشحنة': [
                'تم الشحن',
                'في الطريق',
                'وصلت الميناء',
                'تم التسليم'
            ],
            'حالة الإفراج': [
                'مع الافراج',
                'بدون الافراج',
                'مع الافراج',
                'مع الافراج'
            ],
            'شركة الشحن': [
                'شركة الخليج للشحن',
                'الخطوط السعودية للشحن',
                'شركة الشرق الأوسط للنقل',
                'شركة العربية للشحن'
            ],
            'رقم DHL': [
                'DHL123456789',
                'DHL987654321',
                'DHL555666777',
                'DHL888999000'
            ],
            'ميناء الوصول': [
                'ميناء الملك عبدالعزيز',
                'ميناء الملك فهد',
                'ميناء جدة الإسلامي',
                'ميناء الدمام'
            ],
            'تاريخ الوصول المتوقع': [
                '2025-08-05',
                '2025-08-10',
                '2025-08-15',
                '2025-08-20'
            ],
            'رقم الحاوية': [
                # حاويات متعددة مفصولة بفاصلة
                'MSKU1234567, TCLU9876543, HJMU5555666',
                
                # حاويات متعددة مفصولة بفاصلة منقوطة
                'ABCD1111111; EFGH2222222; IJKL3333333',
                
                # حاويات متعددة مفصولة بخط مائل
                'MNOP4444444/QRST5555555/UVWX6666666',
                
                # حاوية واحدة فقط
                'ZYXW7777777'
            ]
        }
        
        # إنشاء DataFrame
        df = pd.DataFrame(data)
        
        # حفظ الملف
        filename = 'sample_multiple_containers.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء الملف بنجاح: {filename}")
        print(f"📍 مسار الملف: {os.path.abspath(filename)}")
        
        # طباعة معلومات الملف
        print("\n📋 محتويات الملف:")
        print("=" * 50)
        for i, row in df.iterrows():
            print(f"الصف {i+1}:")
            print(f"  التاريخ: {row['التاريخ']}")
            print(f"  المورد: {row['المورد']}")
            print(f"  بوليصة الشحن: {row['بوليصة الشحن']}")
            print(f"  حالة الشحنة: {row['حالة الشحنة']}")
            print(f"  حالة الإفراج: {row['حالة الإفراج']}")
            print(f"  الحاويات: {row['رقم الحاوية']}")

            # تحليل الحاويات
            containers = parse_containers_preview(row['رقم الحاوية'])
            print(f"  عدد الحاويات: {len(containers)}")
            for j, container in enumerate(containers, 1):
                print(f"    حاوية {j}: {container}")
            print("-" * 30)
        
        print("\n🎯 أنواع الفواصل المدعومة:")
        print("• الفاصلة العادية: ,")
        print("• الفاصلة العربية: ،")
        print("• الفاصلة المنقوطة: ;")
        print("• الفاصلة المنقوطة العربية: ؛")
        print("• الخط المائل: /")
        print("• الخط العمودي: |")
        print("• الشرطة: -")
        print("• سطر جديد: \\n")
        
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return None

def parse_containers_preview(container_data):
    """معاينة تحليل الحاويات (نسخة مبسطة)"""
    try:
        containers = []
        separators = [',', '،', ';', '؛', '\n', '\r\n', '|', '-', '/']
        
        container_data = str(container_data).strip()
        
        # البحث عن الفاصل المستخدم
        used_separator = None
        for sep in separators:
            if sep in container_data:
                used_separator = sep
                break
        
        if used_separator:
            parts = container_data.split(used_separator)
            for part in parts:
                cleaned_part = part.strip()
                if cleaned_part:
                    containers.append(cleaned_part)
        else:
            containers.append(container_data)
        
        return containers
        
    except Exception as e:
        print(f"خطأ في تحليل الحاويات: {e}")
        return [container_data] if container_data else []

if __name__ == "__main__":
    print("🚀 إنشاء ملف إكسيل نموذجي للحاويات المتعددة")
    print("=" * 60)
    
    filename = create_multiple_containers_sample()
    
    if filename:
        print(f"\n🎉 تم إنشاء الملف بنجاح!")
        print(f"📁 اسم الملف: {filename}")
        print("\n📝 تعليمات الاستخدام:")
        print("1. افتح شاشة الشحنة الجديدة")
        print("2. انقر على زر 'استيراد إكسيل'")
        print("3. اختر الملف المُنشأ")
        print("4. لاحظ كيف يتم استيراد كل حاوية على حدة")
    else:
        print("\n❌ فشل في إنشاء الملف!")
