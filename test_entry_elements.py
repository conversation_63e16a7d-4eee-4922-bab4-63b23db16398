#!/usr/bin/env python3
"""
اختبار عناصر النموذج في وضع الإدخال
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🔍 اختبار عناصر النموذج...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # إنشاء نافذة الإدخال
        print("📝 إنشاء نافذة الإدخال...")
        entry_window = PurchaseOrdersWindow(mode="entry")
        print("✅ تم إنشاء النافذة")
        
        # قائمة العناصر المطلوبة
        required_elements = [
            'order_number_edit',
            'order_date_edit', 
            'supplier_edit',
            'order_status_combo',
            'currency_combo',
            'exchange_rate_edit',
            'expected_delivery_date_edit',
            'actual_delivery_date_edit',
            'discount_amount_edit',
            'tax_amount_edit',
            'total_amount_edit'
        ]
        
        print("\n🔍 فحص العناصر المطلوبة:")
        missing_elements = []
        
        for element in required_elements:
            if hasattr(entry_window, element):
                print(f"✅ {element}")
            else:
                print(f"❌ {element} - غير موجود")
                missing_elements.append(element)
        
        if missing_elements:
            print(f"\n❌ العناصر المفقودة: {len(missing_elements)}")
            for element in missing_elements:
                print(f"   - {element}")
        else:
            print("\n🎉 جميع العناصر موجودة!")
        
        # اختبار التبويبات
        if hasattr(entry_window, 'details_tabs'):
            print(f"\n📋 عدد التبويبات: {entry_window.details_tabs.count()}")
            for i in range(entry_window.details_tabs.count()):
                tab_text = entry_window.details_tabs.tabText(i)
                print(f"   - التبويب {i+1}: {tab_text}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
