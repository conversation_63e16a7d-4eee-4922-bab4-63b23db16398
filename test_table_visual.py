#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مرئي لرأس جدول طلبات الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QTableWidget, QVBoxLayout, QWidget, QHeaderView
from PySide6.QtCore import Qt

class TestTableWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار رأس الجدول")
        self.setGeometry(100, 100, 800, 600)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # إنشاء جدول اختبار
        self.test_table = QTableWidget()
        self.test_table.setColumnCount(8)
        
        # تعيين تسميات الأعمدة
        headers = [
            "رقم الطلب", "التاريخ", "المورد", "الحالة",
            "عدد الأصناف", "المبلغ", "العملة", "الملاحظات"
        ]
        self.test_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الرأس
        h_header = self.test_table.horizontalHeader()
        h_header.setVisible(True)
        h_header.setSectionResizeMode(QHeaderView.Stretch)
        h_header.setFixedHeight(40)
        
        # إضافة بعض البيانات التجريبية
        self.test_table.setRowCount(3)
        for row in range(3):
            for col in range(8):
                from PySide6.QtWidgets import QTableWidgetItem
                item = QTableWidgetItem(f"بيانات {row+1}-{col+1}")
                self.test_table.setItem(row, col, item)
        
        # تنسيق بسيط
        self.test_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d5dbdb;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        layout.addWidget(self.test_table)
        
        print("✅ تم إنشاء جدول الاختبار")
        print(f"✅ رأس الجدول مرئي: {h_header.isVisible()}")
        print(f"✅ ارتفاع رأس الجدول: {h_header.height()}")

def test_visual_table():
    """اختبار مرئي للجدول"""
    print("🔄 بدء الاختبار المرئي لرأس الجدول...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # إنشاء نافذة الاختبار
    window = TestTableWindow()
    window.show()
    
    print("✅ تم عرض نافذة الاختبار")
    print("📝 يرجى فحص النافذة بصرياً للتأكد من ظهور رأس الجدول")
    
    # تشغيل التطبيق لمدة قصيرة
    import time
    time.sleep(2)
    
    return True

if __name__ == "__main__":
    success = test_visual_table()
    sys.exit(0 if success else 1)
