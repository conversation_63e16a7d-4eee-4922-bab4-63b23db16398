#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QMessageBox, QLabel
from PySide6.QtCore import Qt
from src.database.database_manager import DatabaseManager
from src.ui.shipments.new_shipment_window import NewShipmentWindow

class TestShipmentButtonsWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار أزرار شاشة الشحنة الجديدة")
        self.setGeometry(100, 100, 800, 600)
        
        # إعداد قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title_label = QLabel("اختبار أزرار التحكم في شاشة الشحنة الجديدة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات الاختبار
        info_label = QLabel("""
        هذا الاختبار يتحقق من وجود أزرار التحكم التالية في شاشة الشحنة الجديدة:
        
        ✅ زر إضافة (🆕 إضافة) - لإنشاء شحنة جديدة
        ✅ زر حفظ (💾 حفظ) - لحفظ الشحنة
        ✅ زر تعديل (✏️ تعديل) - لتعديل الشحنة المحفوظة
        ✅ زر خروج (🚪 خروج) - للخروج من النافذة
        
        اضغط على الزر أدناه لفتح شاشة الشحنة الجديدة واختبار الأزرار.
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #34495e;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(info_label)
        
        # زر فتح شاشة الشحنة الجديدة
        open_shipment_btn = QPushButton("🚢 فتح شاشة الشحنة الجديدة")
        open_shipment_btn.clicked.connect(self.open_new_shipment)
        open_shipment_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(open_shipment_btn)
        
        # زر اختبار الوظائف
        test_functions_btn = QPushButton("🧪 اختبار وظائف الأزرار")
        test_functions_btn.clicked.connect(self.test_button_functions)
        test_functions_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(test_functions_btn)
        
        # معلومات النتائج
        self.results_label = QLabel("انتظار بدء الاختبار...")
        self.results_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                margin: 10px;
                border: 1px solid #dee2e6;
            }
        """)
        layout.addWidget(self.results_label)
        
        layout.addStretch()
        
    def open_new_shipment(self):
        """فتح شاشة الشحنة الجديدة"""
        try:
            self.shipment_window = NewShipmentWindow(self)
            self.shipment_window.show()
            
            # التحقق من وجود الأزرار
            self.check_buttons_existence()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح شاشة الشحنة الجديدة:\n{str(e)}")
    
    def check_buttons_existence(self):
        """التحقق من وجود الأزرار المطلوبة"""
        results = []
        
        if hasattr(self.shipment_window, 'new_button'):
            results.append("✅ زر إضافة موجود")
        else:
            results.append("❌ زر إضافة غير موجود")
        
        if hasattr(self.shipment_window, 'save_button'):
            results.append("✅ زر حفظ موجود")
        else:
            results.append("❌ زر حفظ غير موجود")
        
        if hasattr(self.shipment_window, 'edit_button'):
            results.append("✅ زر تعديل موجود")
        else:
            results.append("❌ زر تعديل غير موجود")
        
        if hasattr(self.shipment_window, 'exit_button'):
            results.append("✅ زر خروج موجود")
        else:
            results.append("❌ زر خروج غير موجود")
        
        # عرض النتائج
        results_text = "نتائج فحص الأزرار:\n\n" + "\n".join(results)
        self.results_label.setText(results_text)
        
        # تحديث لون الخلفية حسب النتيجة
        if "❌" in results_text:
            self.results_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #721c24;
                    padding: 15px;
                    background-color: #f8d7da;
                    border-radius: 8px;
                    margin: 10px;
                    border: 1px solid #f5c6cb;
                }
            """)
        else:
            self.results_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #155724;
                    padding: 15px;
                    background-color: #d4edda;
                    border-radius: 8px;
                    margin: 10px;
                    border: 1px solid #c3e6cb;
                }
            """)
    
    def test_button_functions(self):
        """اختبار وظائف الأزرار"""
        if not hasattr(self, 'shipment_window') or not self.shipment_window.isVisible():
            QMessageBox.warning(self, "تحذير", "يجب فتح شاشة الشحنة الجديدة أولاً")
            return
        
        try:
            # اختبار وجود الوظائف
            functions_test = []
            
            if hasattr(self.shipment_window, 'new_shipment'):
                functions_test.append("✅ وظيفة إضافة شحنة جديدة موجودة")
            else:
                functions_test.append("❌ وظيفة إضافة شحنة جديدة غير موجودة")
            
            if hasattr(self.shipment_window, 'save_shipment'):
                functions_test.append("✅ وظيفة حفظ الشحنة موجودة")
            else:
                functions_test.append("❌ وظيفة حفظ الشحنة غير موجودة")
            
            if hasattr(self.shipment_window, 'edit_shipment'):
                functions_test.append("✅ وظيفة تعديل الشحنة موجودة")
            else:
                functions_test.append("❌ وظيفة تعديل الشحنة غير موجودة")
            
            if hasattr(self.shipment_window, 'clear_form'):
                functions_test.append("✅ وظيفة مسح النموذج موجودة")
            else:
                functions_test.append("❌ وظيفة مسح النموذج غير موجودة")
            
            # عرض نتائج اختبار الوظائف
            functions_text = "نتائج اختبار الوظائف:\n\n" + "\n".join(functions_test)
            
            QMessageBox.information(
                self,
                "نتائج اختبار الوظائف",
                functions_text
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في اختبار الوظائف:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestShipmentButtonsWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
