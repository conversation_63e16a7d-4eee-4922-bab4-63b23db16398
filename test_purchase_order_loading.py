#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحميل طلبات الشراء في شاشة الشحنة الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.database.database_manager import DatabaseManager
from src.utils.arabic_support import setup_arabic_support
from src.database.models import PurchaseOrder, Supplier, Item, PurchaseOrderItem
from src.database.session_manager import SessionManager

def create_test_purchase_order():
    """إنشاء طلب شراء تجريبي للاختبار"""
    session = SessionManager.get_session()
    try:
        # التحقق من وجود مورد
        supplier = session.query(Supplier).first()
        if not supplier:
            # إنشاء مورد تجريبي
            supplier = Supplier(
                name="شركة الاختبار للتجارة",
                code="TEST001",
                contact_person="أحمد محمد",
                phone="123456789",
                email="<EMAIL>",
                address="الرياض، المملكة العربية السعودية"
            )
            session.add(supplier)
            session.flush()
        
        # التحقق من وجود صنف
        item = session.query(Item).first()
        if not item:
            # إنشاء صنف تجريبي
            item = Item(
                code="ITEM001",
                name="صنف تجريبي",
                description="صنف للاختبار",
                unit="قطعة",
                weight=1.0,
                price=100.0
            )
            session.add(item)
            session.flush()
        
        # إنشاء طلب شراء تجريبي
        purchase_order = PurchaseOrder(
            order_number="PO-TEST-001",
            supplier_id=supplier.id,
            order_date=None,
            delivery_date=None,
            status="مؤكد",
            notes="طلب شراء تجريبي للاختبار"
        )
        session.add(purchase_order)
        session.flush()
        
        # إضافة صنف لطلب الشراء
        order_item = PurchaseOrderItem(
            purchase_order_id=purchase_order.id,
            item_id=item.id,
            quantity=10.0,
            unit_price=100.0,
            total_price=1000.0,
            delivered_quantity=0.0,
            remaining_quantity=10.0,
            notes="صنف تجريبي"
        )
        session.add(order_item)
        
        session.commit()
        print(f"✅ تم إنشاء طلب شراء تجريبي: {purchase_order.order_number}")
        return purchase_order.id
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء طلب الشراء: {str(e)}")
        return None
    finally:
        session.close()

def main():
    """اختبار تحميل طلبات الشراء"""
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # إعداد دعم اللغة العربية
    setup_arabic_support(app)
    
    # إعداد قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    print("🚀 اختبار تحميل طلبات الشراء في شاشة الشحنة الجديدة")
    print("=" * 60)
    
    # إنشاء طلب شراء تجريبي
    purchase_order_id = create_test_purchase_order()
    
    if purchase_order_id:
        print("✅ الإصلاحات المطبقة:")
        print("   - إصلاح خطأ 'supplier_name_edit' إلى 'supplier_edit'")
        print("   - إضافة تعيين معرف المورد كخاصية للحقل")
        print("   - تحسين تنسيق عرض اسم المورد مع الكود")
        print()
        
        # إنشاء نافذة الشحنة الجديدة
        window = NewShipmentWindow()
        
        # اختبار تحميل طلب الشراء
        try:
            window.load_purchase_order_items(purchase_order_id)
            print("✅ تم تحميل طلب الشراء بنجاح!")
            
            # عرض النافذة للمراجعة
            window.show()
            
            # رسالة للمستخدم
            QMessageBox.information(
                window,
                "اختبار ناجح",
                "✅ تم إصلاح مشكلة تحميل طلبات الشراء بنجاح!\n\n"
                "تم تحميل البيانات التالية:\n"
                "• بيانات المورد\n"
                "• أصناف طلب الشراء\n"
                "• أعمدة التواريخ الجديدة\n\n"
                "يمكنك الآن استخدام الشاشة بشكل طبيعي."
            )
            
        except Exception as e:
            print(f"❌ خطأ في تحميل طلب الشراء: {str(e)}")
            QMessageBox.critical(
                None,
                "خطأ",
                f"❌ فشل في تحميل طلب الشراء:\n{str(e)}"
            )
            return
    
    else:
        print("❌ فشل في إنشاء طلب شراء تجريبي")
        return
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
