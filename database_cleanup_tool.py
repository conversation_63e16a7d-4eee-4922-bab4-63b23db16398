#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تنظيف قاعدة البيانات - حذف آمن لجميع بيانات الشحنات
Database Cleanup Tool - Safe deletion of all shipment data
"""

import sys
import os
from datetime import datetime
from sqlalchemy import text

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, ShipmentItem

class DatabaseCleanupTool:
    """أداة تنظيف قاعدة البيانات"""
    
    def __init__(self):
        """تهيئة الأداة"""
        self.db_manager = DatabaseManager()
        self.cleanup_log = []
        
    def log_action(self, action: str, details: str = ""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {action}"
        if details:
            log_entry += f" - {details}"
        self.cleanup_log.append(log_entry)
        print(log_entry)
    
    def get_shipment_statistics(self):
        """الحصول على إحصائيات الشحنات قبل الحذف"""
        session = self.db_manager.get_session()
        try:
            # عدد الشحنات
            shipment_count = session.query(Shipment).count()
            
            # عدد أصناف الشحنات
            shipment_items_count = session.query(ShipmentItem).count()
            
            # إحصائيات تفصيلية
            stats = {
                'total_shipments': shipment_count,
                'total_shipment_items': shipment_items_count,
                'timestamp': datetime.now().isoformat()
            }
            
            self.log_action("📊 إحصائيات قاعدة البيانات", 
                          f"الشحنات: {shipment_count}, الأصناف: {shipment_items_count}")
            
            return stats
            
        except Exception as e:
            self.log_action("❌ خطأ في الحصول على الإحصائيات", str(e))
            return None
        finally:
            session.close()
    
    def backup_data_structure(self):
        """نسخ احتياطي لبنية البيانات"""
        try:
            session = self.db_manager.get_session()
            
            # الحصول على بنية الجداول
            tables_info = {}
            
            # معلومات جدول الشحنات
            result = session.execute(text("PRAGMA table_info(shipments)"))
            tables_info['shipments'] = [dict(row._mapping) for row in result]
            
            # معلومات جدول أصناف الشحنات
            result = session.execute(text("PRAGMA table_info(shipment_items)"))
            tables_info['shipment_items'] = [dict(row._mapping) for row in result]
            
            self.log_action("💾 تم حفظ بنية الجداول", f"جداول: {len(tables_info)}")
            
            return tables_info
            
        except Exception as e:
            self.log_action("❌ خطأ في نسخ بنية البيانات", str(e))
            return None
        finally:
            session.close()
    
    def delete_all_shipment_data(self):
        """حذف جميع بيانات الشحنات بشكل آمن"""
        session = self.db_manager.get_session()
        try:
            # بدء المعاملة
            session.begin()

            # 1. حذف أصناف الشحنات أولاً (بسبب العلاقات الخارجية)
            shipment_items_deleted = session.query(ShipmentItem).count()
            session.query(ShipmentItem).delete()
            self.log_action("🗑️ تم حذف أصناف الشحنات", f"عدد: {shipment_items_deleted}")

            # 2. حذف الشحنات
            shipments_deleted = session.query(Shipment).count()
            session.query(Shipment).delete()
            self.log_action("🗑️ تم حذف الشحنات", f"عدد: {shipments_deleted}")

            # تأكيد المعاملة أولاً
            session.commit()

            # 3. إعادة تعيين المعرفات التلقائية (في معاملة منفصلة)
            try:
                # التحقق من وجود جدول sqlite_sequence
                result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='sqlite_sequence'"))
                if result.fetchone():
                    session.execute(text("DELETE FROM sqlite_sequence WHERE name='shipments'"))
                    session.execute(text("DELETE FROM sqlite_sequence WHERE name='shipment_items'"))
                    session.commit()
                    self.log_action("🔄 تم إعادة تعيين المعرفات التلقائية")
                else:
                    self.log_action("ℹ️ جدول sqlite_sequence غير موجود - تخطي إعادة التعيين")
            except Exception as seq_error:
                self.log_action("⚠️ تحذير في إعادة تعيين المعرفات", str(seq_error))

            self.log_action("✅ تم حذف جميع بيانات الشحنات بنجاح",
                          f"شحنات: {shipments_deleted}, أصناف: {shipment_items_deleted}")

            return {
                'success': True,
                'shipments_deleted': shipments_deleted,
                'shipment_items_deleted': shipment_items_deleted
            }

        except Exception as e:
            session.rollback()
            self.log_action("❌ خطأ في حذف البيانات", str(e))
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            session.close()
    
    def verify_cleanup(self):
        """التحقق من نجاح عملية التنظيف"""
        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود شحنات
            remaining_shipments = session.query(Shipment).count()
            remaining_items = session.query(ShipmentItem).count()
            
            if remaining_shipments == 0 and remaining_items == 0:
                self.log_action("✅ تم التحقق من نجاح التنظيف", "لا توجد بيانات متبقية")
                return True
            else:
                self.log_action("⚠️ تحذير: بيانات متبقية", 
                              f"شحنات: {remaining_shipments}, أصناف: {remaining_items}")
                return False
                
        except Exception as e:
            self.log_action("❌ خطأ في التحقق", str(e))
            return False
        finally:
            session.close()
    
    def run_complete_cleanup(self):
        """تشغيل عملية التنظيف الكاملة"""
        self.log_action("🚀 بدء عملية تنظيف قاعدة البيانات")
        
        # 1. الحصول على الإحصائيات
        stats_before = self.get_shipment_statistics()
        
        # 2. نسخ احتياطي للبنية
        structure_backup = self.backup_data_structure()
        
        # 3. حذف البيانات
        cleanup_result = self.delete_all_shipment_data()
        
        # 4. التحقق من النتائج
        verification_result = self.verify_cleanup()
        
        # 5. الإحصائيات النهائية
        stats_after = self.get_shipment_statistics()
        
        # تقرير نهائي
        self.log_action("📋 تقرير التنظيف النهائي")
        self.log_action("=" * 50)
        
        if cleanup_result['success'] and verification_result:
            self.log_action("✅ تم تنظيف قاعدة البيانات بنجاح")
            self.log_action(f"📊 البيانات المحذوفة: {cleanup_result['shipments_deleted']} شحنة، {cleanup_result['shipment_items_deleted']} صنف")
        else:
            self.log_action("❌ فشل في تنظيف قاعدة البيانات")
            if 'error' in cleanup_result:
                self.log_action(f"السبب: {cleanup_result['error']}")
        
        return {
            'success': cleanup_result['success'] and verification_result,
            'stats_before': stats_before,
            'stats_after': stats_after,
            'cleanup_result': cleanup_result,
            'verification_result': verification_result,
            'log': self.cleanup_log
        }

def main():
    """الدالة الرئيسية"""
    print("🧹 أداة تنظيف قاعدة البيانات - حذف جميع بيانات الشحنات")
    print("=" * 60)
    
    # تأكيد من المستخدم
    confirm = input("⚠️ هذه العملية ستحذف جميع بيانات الشحنات نهائياً. هل تريد المتابعة؟ (yes/no): ")
    
    if confirm.lower() not in ['yes', 'y', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # تشغيل أداة التنظيف
    cleanup_tool = DatabaseCleanupTool()
    result = cleanup_tool.run_complete_cleanup()
    
    # عرض النتائج
    print("\n" + "=" * 60)
    if result['success']:
        print("✅ تم تنظيف قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في تنظيف قاعدة البيانات!")
    
    print("📋 سجل العمليات:")
    for log_entry in result['log']:
        print(f"  {log_entry}")

if __name__ == "__main__":
    main()
