#!/usr/bin/env python3
"""
اختبار إصلاح نافذة التحرير
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🔍 اختبار إصلاح نافذة التحرير...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # 1. إنشاء النافذة الرئيسية
        print("\n📋 إنشاء النافذة الرئيسية...")
        main_window = PurchaseOrdersWindow(mode="list")
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # 2. اختبار دالة open_edit_window
        print("\n🔧 اختبار دالة open_edit_window...")
        if hasattr(main_window, 'open_edit_window'):
            print("✅ دالة open_edit_window موجودة")
            
            # اختبار إنشاء نافذة التحرير (بدون معرف حقيقي)
            try:
                # محاولة فتح نافذة تحرير مع معرف وهمي
                print("📝 محاولة فتح نافذة تحرير...")
                main_window.open_edit_window(1)  # معرف وهمي للاختبار
                print("✅ تم إنشاء نافذة التحرير بنجاح")
                print("✅ النافذة تفتح في وضع الإدخال")
                
            except Exception as e:
                # متوقع أن يحدث خطأ في تحميل البيانات لأن المعرف وهمي
                if "expected_delivery_date_edit" in str(e):
                    print("❌ لا يزال هناك خطأ في expected_delivery_date_edit")
                    print(f"   الخطأ: {e}")
                    return False
                else:
                    print(f"✅ خطأ متوقع في تحميل البيانات (معرف وهمي): {e}")
                    print("✅ لكن لا يوجد خطأ في expected_delivery_date_edit")
        else:
            print("❌ دالة open_edit_window غير موجودة")
            return False
        
        # 3. اختبار إنشاء نافذة إدخال مباشرة مع تحميل البيانات
        print("\n📝 اختبار نافذة الإدخال مع تحميل البيانات...")
        try:
            entry_window = PurchaseOrdersWindow(mode="entry")
            
            # التحقق من وجود العناصر المطلوبة
            required_elements = ['expected_delivery_date_edit', 'actual_delivery_date_edit']
            for element in required_elements:
                if hasattr(entry_window, element):
                    print(f"✅ {element} موجود")
                else:
                    print(f"❌ {element} غير موجود")
                    return False
            
            # اختبار دالة load_order_details (مع معرف وهمي)
            if hasattr(entry_window, 'load_order_details'):
                print("✅ دالة load_order_details موجودة")
                try:
                    entry_window.load_order_details(999)  # معرف وهمي
                except Exception as e:
                    if "expected_delivery_date_edit" in str(e):
                        print("❌ لا يزال هناك خطأ في expected_delivery_date_edit")
                        return False
                    else:
                        print(f"✅ خطأ متوقع في قاعدة البيانات: {e}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة الإدخال: {e}")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ تم إصلاح مشكلة expected_delivery_date_edit")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار العام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 الإصلاح مكتمل وناجح!")
    else:
        print("\n❌ الإصلاح يحتاج مراجعة إضافية")
