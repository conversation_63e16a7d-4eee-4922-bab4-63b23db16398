#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات نموذجية لشركات الشحن كموردين
Create Sample Data for Shipping Companies as Suppliers
"""

import pandas as pd
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_shipping_companies_sample():
    """إنشاء ملف إكسيل نموذجي لشركات الشحن كموردين"""
    
    print("🚢 إنشاء بيانات نموذجية لشركات الشحن كموردين")
    print("=" * 60)
    
    # بيانات شركات الشحن العالمية
    shipping_companies_data = {
        'رقم المورد': [
            'SHIP001',
            'SHIP002', 
            'SHIP003',
            'SHIP004',
            'SHIP005',
            'SHIP006',
            'SHIP007',
            'SHIP008',
            'SHIP009',
            'SHIP010'
        ],
        'اسم المورد': [
            'شركة ماريسك للشحن',
            'شركة إم إس سي للشحن',
            'شركة كوسكو للشحن',
            'شركة سي إم إيه سي جي إم',
            'شركة إيفرجرين للشحن',
            'شركة هابج لويد',
            'شركة أو أو سي إل',
            'شركة يانج مينج',
            'شركة هيونداي للشحن التجاري',
            'شركة الخطوط السعودية للشحن'
        ],
        'الاسم الإنجليزي': [
            'Maersk Line',
            'Mediterranean Shipping Company (MSC)',
            'COSCO Shipping Lines',
            'CMA CGM Group',
            'Evergreen Marine Corporation',
            'Hapag-Lloyd AG',
            'Orient Overseas Container Line (OOCL)',
            'Yang Ming Marine Transport Corporation',
            'Hyundai Merchant Marine (HMM)',
            'Saudi National Shipping Company (Bahri)'
        ],
        'نوع المورد': [
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن',
            'شركة شحن'
        ],
        'الشخص المسؤول': [
            'أحمد محمد الشحن',
            'سارة أحمد التجارة',
            'محمد علي البحري',
            'فاطمة حسن اللوجستية',
            'عبدالله سالم النقل',
            'نورا خالد الشحن',
            'يوسف عمر البحري',
            'ليلى محمود التجارة',
            'حسام طارق اللوجستية',
            'رنا فهد الشحن'
        ],
        'الهاتف': [
            '+45-33-63-33-63',
            '+41-22-703-8888',
            '+86-21-3588-8888',
            '+33-4-88-91-90-00',
            '+886-2-2505-6688',
            '+49-40-3001-0',
            '+852-2833-7888',
            '+886-2-2455-9988',
            '+82-2-746-1114',
            '+966-13-847-8888'
        ],
        'الجوال': [
            '+966-50-123-4567',
            '+966-55-234-5678',
            '+966-56-345-6789',
            '+966-54-456-7890',
            '+966-53-567-8901',
            '+966-52-678-9012',
            '+966-51-789-0123',
            '+966-59-890-1234',
            '+966-58-901-2345',
            '+966-57-012-3456'
        ],
        'البريد الإلكتروني': [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ],
        'الموقع الإلكتروني': [
            'www.maersk.com',
            'www.msc.com',
            'www.cosco-shipping.com',
            'www.cma-cgm.com',
            'www.evergreen-marine.com',
            'www.hapag-lloyd.com',
            'www.oocl.com',
            'www.yangming.com',
            'www.hmm21.com',
            'www.bahri.sa'
        ],
        'الدولة': [
            'الدنمارك',
            'سويسرا',
            'الصين',
            'فرنسا',
            'تايوان',
            'ألمانيا',
            'هونج كونج',
            'تايوان',
            'كوريا الجنوبية',
            'السعودية'
        ],
        'المدينة': [
            'كوبنهاجن',
            'جنيف',
            'شنغهاي',
            'مرسيليا',
            'تايبيه',
            'هامبورج',
            'هونج كونج',
            'كيلونج',
            'سيول',
            'الدمام'
        ],
        'العنوان': [
            'مكتب الرياض - حي العليا - شارع الملك فهد',
            'مكتب جدة - حي الروضة - طريق الملك عبدالعزيز',
            'مكتب الدمام - حي الفيصلية - شارع الأمير محمد بن فهد',
            'مكتب الرياض - حي السليمانية - طريق الملك سلمان',
            'مكتب جدة - حي البلد - شارع قابل',
            'مكتب الدمام - حي الشاطئ - كورنيش الدمام',
            'مكتب الرياض - حي الملز - شارع التحلية',
            'مكتب جدة - حي الزهراء - طريق الأمير ماجد',
            'مكتب الدمام - حي الجلوية - شارع الملك سعود',
            'مكتب الدمام - المنطقة الصناعية الثانية'
        ],
        'الرمز البريدي': [
            '11564',
            '21442',
            '31952',
            '11564',
            '21589',
            '31952',
            '11564',
            '21442',
            '31952',
            '31952'
        ],
        'حد الائتمان': [
            500000.00,
            750000.00,
            600000.00,
            450000.00,
            400000.00,
            350000.00,
            300000.00,
            250000.00,
            200000.00,
            800000.00
        ],
        'مدة السداد': [
            30,
            45,
            30,
            60,
            30,
            45,
            30,
            30,
            45,
            30
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(shipping_companies_data)
    
    # حفظ الملف
    filename = 'شركات_الشحن_كموردين_نموذجي.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف: {filename}")
    print(f"📊 عدد شركات الشحن: {len(df)}")
    
    # عرض ملخص البيانات
    print("\n📋 ملخص شركات الشحن المضافة:")
    for i, row in df.iterrows():
        print(f"   {i+1:2d}. 🚢 {row['اسم المورد']}")
        print(f"       🌍 {row['الدولة']} - {row['المدينة']}")
        print(f"       📧 {row['البريد الإلكتروني']}")
        print()
    
    print("🎯 يمكنك الآن:")
    print("   1. فتح نافذة إدارة الموردين")
    print("   2. الضغط على 'استيراد من إكسيل'")
    print("   3. اختيار الملف المنشأ")
    print("   4. استيراد شركات الشحن كموردين من نوع 'شركة شحن'")
    
    return filename

def test_shipping_company_import():
    """اختبار استيراد شركة شحن"""
    print("\n🧪 اختبار استيراد شركة شحن")
    print("-" * 40)
    
    try:
        from src.database.models import Supplier
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مورد شركة شحن تجريبي
        maersk_supplier = Supplier(
            code="SHIP001",
            name="شركة ماريسك للشحن",
            name_en="Maersk Line",
            supplier_type="شركة شحن",
            contact_person="أحمد محمد الشحن",
            tax_number="300123456789",
            commercial_register="CR-2023-SHIP001",
            phone="+45-33-63-33-63",
            mobile="+966-50-123-4567",
            email="<EMAIL>",
            website="www.maersk.com",
            country="الدنمارك",
            city="كوبنهاجن",
            address="مكتب الرياض - حي العليا - شارع الملك فهد",
            postal_code="11564",
            credit_limit=500000.00,
            payment_terms=30,
            is_active=True
        )
        
        print("✅ تم إنشاء مورد شركة شحن بنجاح:")
        print(f"   📝 الاسم: {maersk_supplier.name}")
        print(f"   🏷️ النوع: {maersk_supplier.supplier_type}")
        print(f"   🌍 الدولة: {maersk_supplier.country}")
        print(f"   📞 الهاتف: {maersk_supplier.phone}")
        print(f"   📧 البريد: {maersk_supplier.email}")
        print(f"   💰 حد الائتمان: {maersk_supplier.credit_limit:,.2f} ريال")
        print(f"   📅 مدة السداد: {maersk_supplier.payment_terms} يوم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 إنشاء بيانات نموذجية لشركات الشحن كموردين")
    print("=" * 70)
    
    try:
        # إنشاء ملف البيانات النموذجية
        filename = create_shipping_companies_sample()
        
        # اختبار إنشاء مورد شركة شحن
        test_result = test_shipping_company_import()
        
        print("\n" + "=" * 70)
        print("📊 النتائج النهائية:")
        
        if test_result:
            print("✅ تم إنشاء البيانات النموذجية بنجاح")
            print("✅ تم اختبار إنشاء مورد شركة شحن بنجاح")
            
            print("\n🎉 جاهز للاستخدام!")
            print("📋 الخطوات التالية:")
            print("   1. افتح نظام إدارة الموردين")
            print("   2. اختر 'إضافة مورد جديد'")
            print("   3. اختر نوع المورد: 'شركة شحن'")
            print("   4. أدخل بيانات شركة الشحن")
            print("   5. أو استورد الملف النموذجي المنشأ")
            
            print(f"\n📁 الملف المنشأ: {filename}")
            print("🎯 يحتوي على 10 شركات شحن عالمية جاهزة للاستيراد")
            
        else:
            print("⚠️ هناك مشكلة في الاختبار")
        
        return test_result
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
