#!/usr/bin/env python3
"""
اختبار زر طلب جديد بعد الإصلاح
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_new_order_modes():
    """اختبار أوضاع النافذة المختلفة"""
    try:
        print("🔍 اختبار أوضاع النافذة...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        # 1. اختبار الوضع الافتراضي (قائمة)
        print("\n📋 اختبار وضع القائمة...")
        list_window = PurchaseOrdersWindow(mode="list")
        
        if hasattr(list_window, 'orders_table'):
            print("✅ جدول الطلبات موجود في وضع القائمة")
        else:
            print("❌ جدول الطلبات غير موجود")
        
        if hasattr(list_window, 'search_edit'):
            print("✅ حقل البحث موجود في وضع القائمة")
        else:
            print("❌ حقل البحث غير موجود")
        
        # 2. اختبار وضع الإدخال
        print("\n📝 اختبار وضع الإدخال...")
        entry_window = PurchaseOrdersWindow(mode="entry")
        
        if hasattr(entry_window, 'details_tabs'):
            print("✅ تبويبات الإدخال موجودة في وضع الإدخال")
            
            # التحقق من التبويبات
            tabs_count = entry_window.details_tabs.count()
            print(f"✅ عدد التبويبات: {tabs_count}")
            
            for i in range(tabs_count):
                tab_text = entry_window.details_tabs.tabText(i)
                print(f"   - التبويب {i+1}: {tab_text}")
                
        else:
            print("❌ تبويبات الإدخال غير موجودة")
        
        # 3. اختبار دالة new_order
        print("\n🆕 اختبار دالة طلب جديد...")
        main_window = PurchaseOrdersWindow(mode="list")
        
        if hasattr(main_window, 'new_order'):
            print("✅ دالة new_order موجودة")
            
            try:
                # محاكاة الضغط على زر طلب جديد
                main_window.new_order()
                print("✅ تم تنفيذ دالة new_order بنجاح")
                print("✅ يجب أن تفتح نافذة إدخال منفصلة")
                
            except Exception as e:
                print(f"❌ خطأ في تنفيذ new_order: {e}")
                
        else:
            print("❌ دالة new_order غير موجودة")
        
        print("\n🎉 انتهى اختبار الأوضاع!")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_new_order_modes():
        print("\n🚀 زر طلب جديد يعمل بشكل مثالي!")
        print("📋 الميزات الجديدة:")
        print("   - وضع القائمة: عرض جدول الطلبات مع البحث والفلترة")
        print("   - وضع الإدخال: نموذج إدخال مع تبويبات منظمة")
        print("   - زر طلب جديد يفتح نافذة إدخال منفصلة")
    else:
        print("\n❌ هناك مشكلة في الأوضاع الجديدة")
        sys.exit(1)
