#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة مراجعة إجراءات تعديل الشحنات
Shipment Edit Procedures Audit Tool - Comprehensive review of shipment editing functionality
"""

import sys
import os
import time
import threading
from datetime import datetime, date
from typing import Dict, List, Any, Tuple

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import (
        Shipment, ShipmentItem, Container, ShipmentDocument,
        Supplier, Item, Base
    )
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد النماذج: {e}")

class ShipmentEditProceduresAuditor:
    """مراجع إجراءات تعديل الشحنات"""
    
    def __init__(self, db_manager=None):
        """تهيئة مراجع إجراءات التعديل"""
        self.db_manager = db_manager or DatabaseManager()
        self.audit_log = []
        self.issues_found = []
        self.score = 0
        self.max_score = 100
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج المراجعة"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status == "ERROR":
            self.issues_found.append(f"{category}: {message}")
    
    def audit_edit_mode_detection(self) -> int:
        """مراجعة آلية اكتشاف وضع التعديل"""
        self.log_audit("وضع التعديل", "بدء مراجعة آلية اكتشاف وضع التعديل")
        score = 0
        
        try:
            # فحص ملف new_shipment_window.py
            window_file = "src/ui/shipments/new_shipment_window.py"
            if os.path.exists(window_file):
                with open(window_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود متغير is_edit_mode
                if "self.is_edit_mode = shipment_id is not None" in content:
                    self.log_audit("وضع التعديل", "✅ متغير is_edit_mode موجود ومُعرف بشكل صحيح")
                    score += 15
                else:
                    self.log_audit("وضع التعديل", "❌ متغير is_edit_mode غير موجود أو غير مُعرف بشكل صحيح", "ERROR")
                
                # فحص وجود current_shipment_id
                if "self.current_shipment_id = shipment_id" in content:
                    self.log_audit("وضع التعديل", "✅ متغير current_shipment_id موجود")
                    score += 10
                else:
                    self.log_audit("وضع التعديل", "❌ متغير current_shipment_id غير موجود", "ERROR")
                
                # فحص وجود دالة load_shipment_data
                if "def load_shipment_data(self):" in content:
                    self.log_audit("وضع التعديل", "✅ دالة load_shipment_data موجودة")
                    score += 15
                else:
                    self.log_audit("وضع التعديل", "❌ دالة load_shipment_data غير موجودة", "ERROR")
                
                # فحص استدعاء load_shipment_data في وضع التعديل
                if "if self.is_edit_mode:" in content and "load_shipment_data" in content:
                    self.log_audit("وضع التعديل", "✅ يتم استدعاء load_shipment_data في وضع التعديل")
                    score += 10
                else:
                    self.log_audit("وضع التعديل", "❌ لا يتم استدعاء load_shipment_data في وضع التعديل", "ERROR")
            
            else:
                self.log_audit("وضع التعديل", "❌ ملف new_shipment_window.py غير موجود", "ERROR")
        
        except Exception as e:
            self.log_audit("وضع التعديل", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("وضع التعديل", f"نتيجة مراجعة وضع التعديل: {score}/50")
        return score
    
    def audit_data_loading_procedures(self) -> int:
        """مراجعة إجراءات تحميل البيانات"""
        self.log_audit("تحميل البيانات", "بدء مراجعة إجراءات تحميل البيانات")
        score = 0
        
        try:
            # إنشاء شحنة تجريبية للاختبار
            session = self.db_manager.get_session()
            
            # الحصول على مورد وصنف للاختبار
            supplier = session.query(Supplier).first()
            item = session.query(Item).first()
            
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار التعديل",
                    contact_person="شخص الاتصال",
                    phone="123456789"
                )
                session.add(supplier)
                session.commit()
            
            if not item:
                item = Item(
                    code="EDIT-TEST-001",
                    name="صنف اختبار التعديل",
                    unit="قطعة"
                )
                session.add(item)
                session.commit()
            
            # إنشاء شحنة تجريبية
            test_shipment = Shipment(
                shipment_number="EDIT-AUDIT-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة اختبار التعديل",
                notes="شحنة اختبار لمراجعة التعديل"
            )
            session.add(test_shipment)
            session.flush()
            
            # إضافة أصناف تجريبية
            test_item = ShipmentItem(
                shipment_id=test_shipment.id,
                item_id=item.id,
                quantity=10.0,
                unit_price=100.0,
                notes="صنف اختبار التعديل"
            )
            session.add(test_item)
            
            # إضافة حاوية تجريبية
            test_container = Container(
                shipment_id=test_shipment.id,
                container_number="EDIT-CONT-001",
                container_type="عادية",
                container_size="20 قدم",
                status="فارغة"
            )
            session.add(test_container)
            session.commit()
            
            self.log_audit("تحميل البيانات", f"✅ تم إنشاء شحنة تجريبية رقم {test_shipment.id}")
            score += 20
            
            # اختبار تحميل البيانات
            loaded_shipment = session.query(Shipment).filter(
                Shipment.id == test_shipment.id
            ).first()
            
            if loaded_shipment:
                self.log_audit("تحميل البيانات", "✅ تم تحميل بيانات الشحنة الأساسية")
                score += 10
                
                # اختبار تحميل الأصناف
                loaded_items = session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == test_shipment.id
                ).all()
                
                if loaded_items:
                    self.log_audit("تحميل البيانات", f"✅ تم تحميل {len(loaded_items)} صنف")
                    score += 10
                else:
                    self.log_audit("تحميل البيانات", "❌ فشل في تحميل الأصناف", "ERROR")
                
                # اختبار تحميل الحاويات
                loaded_containers = session.query(Container).filter(
                    Container.shipment_id == test_shipment.id
                ).all()
                
                if loaded_containers:
                    self.log_audit("تحميل البيانات", f"✅ تم تحميل {len(loaded_containers)} حاوية")
                    score += 10
                else:
                    self.log_audit("تحميل البيانات", "❌ فشل في تحميل الحاويات", "ERROR")
            
            # تنظيف البيانات التجريبية
            session.query(ShipmentItem).filter(ShipmentItem.shipment_id == test_shipment.id).delete()
            session.query(Container).filter(Container.shipment_id == test_shipment.id).delete()
            session.delete(test_shipment)
            session.commit()
            session.close()
            
        except Exception as e:
            self.log_audit("تحميل البيانات", f"خطأ في اختبار تحميل البيانات: {str(e)}", "ERROR")
        
        self.log_audit("تحميل البيانات", f"نتيجة مراجعة تحميل البيانات: {score}/50")
        return score
    
    def audit_edit_save_procedures(self) -> int:
        """مراجعة إجراءات حفظ التعديلات"""
        self.log_audit("حفظ التعديلات", "بدء مراجعة إجراءات حفظ التعديلات")
        score = 0
        
        try:
            # فحص ملف new_shipment_window.py للتأكد من وجود منطق التعديل
            window_file = "src/ui/shipments/new_shipment_window.py"
            if os.path.exists(window_file):
                with open(window_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود منطق التعديل في دالة الحفظ
                if "if self.is_edit_mode and self.current_shipment_id:" in content:
                    self.log_audit("حفظ التعديلات", "✅ منطق التعديل موجود في دالة الحفظ")
                    score += 15
                else:
                    self.log_audit("حفظ التعديلات", "❌ منطق التعديل غير موجود في دالة الحفظ", "ERROR")
                
                # فحص استخدام session.get للحصول على الشحنة
                if "session.get(Shipment, self.current_shipment_id)" in content:
                    self.log_audit("حفظ التعديلات", "✅ يتم استخدام session.get للحصول على الشحنة")
                    score += 10
                else:
                    self.log_audit("حفظ التعديلات", "❌ لا يتم استخدام session.get للحصول على الشحنة", "ERROR")
                
                # فحص تحديث البيانات باستخدام setattr
                if "setattr(shipment, key, value)" in content:
                    self.log_audit("حفظ التعديلات", "✅ يتم تحديث البيانات باستخدام setattr")
                    score += 10
                else:
                    self.log_audit("حفظ التعديلات", "❌ لا يتم تحديث البيانات بشكل صحيح", "ERROR")
                
                # فحص حذف الأصناف والحاويات القديمة
                if "delete()" in content and "ShipmentItem" in content:
                    self.log_audit("حفظ التعديلات", "✅ يتم حذف الأصناف القديمة قبل إضافة الجديدة")
                    score += 10
                else:
                    self.log_audit("حفظ التعديلات", "❌ لا يتم حذف الأصناف القديمة", "ERROR")
                
                # فحص معالجة الأخطاء
                if "except Exception as e:" in content and "rollback" in content:
                    self.log_audit("حفظ التعديلات", "✅ معالجة الأخطاء موجودة مع rollback")
                    score += 5
                else:
                    self.log_audit("حفظ التعديلات", "❌ معالجة الأخطاء غير كافية", "ERROR")
            
        except Exception as e:
            self.log_audit("حفظ التعديلات", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("حفظ التعديلات", f"نتيجة مراجعة حفظ التعديلات: {score}/50")
        return score
    
    def audit_ui_integration(self) -> int:
        """مراجعة تكامل واجهة المستخدم مع التعديل"""
        self.log_audit("تكامل الواجهة", "بدء مراجعة تكامل واجهة المستخدم")
        score = 0
        
        try:
            # فحص ملف shipments_window.py
            shipments_file = "src/ui/shipments/shipments_window.py"
            if os.path.exists(shipments_file):
                with open(shipments_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود دالة edit_shipment
                if "def edit_shipment(self):" in content:
                    self.log_audit("تكامل الواجهة", "✅ دالة edit_shipment موجودة")
                    score += 10
                else:
                    self.log_audit("تكامل الواجهة", "❌ دالة edit_shipment غير موجودة", "ERROR")
                
                # فحص تمرير shipment_id للنافذة
                if "NewShipmentWindow(self, shipment_id=shipment_id)" in content:
                    self.log_audit("تكامل الواجهة", "✅ يتم تمرير shipment_id للنافذة")
                    score += 10
                else:
                    self.log_audit("تكامل الواجهة", "❌ لا يتم تمرير shipment_id للنافذة", "ERROR")
                
                # فحص ربط الإشارات
                if "shipment_saved.connect" in content:
                    self.log_audit("تكامل الواجهة", "✅ إشارة shipment_saved مربوطة")
                    score += 5
                else:
                    self.log_audit("تكامل الواجهة", "❌ إشارة shipment_saved غير مربوطة", "ERROR")
                
                # فحص قائمة الزر الأيمن
                if "edit_shipment_from_context_menu" in content:
                    self.log_audit("تكامل الواجهة", "✅ قائمة الزر الأيمن تدعم التعديل")
                    score += 5
                else:
                    self.log_audit("تكامل الواجهة", "❌ قائمة الزر الأيمن لا تدعم التعديل", "ERROR")
            
        except Exception as e:
            self.log_audit("تكامل الواجهة", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("تكامل الواجهة", f"نتيجة مراجعة تكامل الواجهة: {score}/30")
        return score
    
    def audit_data_validation_in_edit_mode(self) -> int:
        """مراجعة التحقق من صحة البيانات في وضع التعديل"""
        self.log_audit("التحقق من البيانات", "بدء مراجعة التحقق من صحة البيانات في وضع التعديل")
        score = 0
        
        try:
            # فحص ملف new_shipment_window.py
            window_file = "src/ui/shipments/new_shipment_window.py"
            if os.path.exists(window_file):
                with open(window_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص استثناء الشحنة الحالية من فحص التفرد
                if "if self.is_edit_mode and self.current_shipment_id:" in content and "filter(Shipment.id != self.current_shipment_id)" in content:
                    self.log_audit("التحقق من البيانات", "✅ يتم استثناء الشحنة الحالية من فحص التفرد")
                    score += 15
                else:
                    self.log_audit("التحقق من البيانات", "❌ لا يتم استثناء الشحنة الحالية من فحص التفرد", "ERROR")
                
                # فحص وجود دالة check_shipment_number_unique
                if "def check_shipment_number_unique" in content:
                    self.log_audit("التحقق من البيانات", "✅ دالة فحص تفرد رقم الشحنة موجودة")
                    score += 5
                else:
                    self.log_audit("التحقق من البيانات", "❌ دالة فحص تفرد رقم الشحنة غير موجودة", "ERROR")
            
        except Exception as e:
            self.log_audit("التحقق من البيانات", f"خطأ في المراجعة: {str(e)}", "ERROR")
        
        self.log_audit("التحقق من البيانات", f"نتيجة مراجعة التحقق من البيانات: {score}/20")
        return score
    
    def run_comprehensive_audit(self) -> Dict:
        """تشغيل المراجعة الشاملة لإجراءات تعديل الشحنات"""
        self.log_audit("مراجعة شاملة", "بدء المراجعة الشاملة لإجراءات تعديل الشحنات")
        
        # تشغيل جميع المراجعات
        edit_mode_score = self.audit_edit_mode_detection()
        data_loading_score = self.audit_data_loading_procedures()
        save_procedures_score = self.audit_edit_save_procedures()
        ui_integration_score = self.audit_ui_integration()
        data_validation_score = self.audit_data_validation_in_edit_mode()
        
        # حساب النتيجة الإجمالية
        total_score = edit_mode_score + data_loading_score + save_procedures_score + ui_integration_score + data_validation_score
        max_possible = 200  # 50 + 50 + 50 + 30 + 20
        percentage = (total_score / max_possible) * 100
        
        # تحديد التقييم
        if percentage >= 90:
            grade = "ممتاز"
        elif percentage >= 80:
            grade = "جيد جداً"
        elif percentage >= 70:
            grade = "جيد"
        elif percentage >= 60:
            grade = "مقبول"
        else:
            grade = "يحتاج تحسين"
        
        self.log_audit("مراجعة شاملة", f"النتيجة الإجمالية: {total_score}/{max_possible} ({percentage:.1f}%) - {grade}")
        
        return {
            'total_score': total_score,
            'max_score': max_possible,
            'percentage': percentage,
            'grade': grade,
            'detailed_scores': {
                'edit_mode_detection': edit_mode_score,
                'data_loading': data_loading_score,
                'save_procedures': save_procedures_score,
                'ui_integration': ui_integration_score,
                'data_validation': data_validation_score
            },
            'issues_found': self.issues_found,
            'audit_log': self.audit_log
        }

    def test_edit_functionality_integration(self) -> int:
        """اختبار تكامل وظائف التعديل عملياً"""
        self.log_audit("اختبار التكامل", "بدء اختبار تكامل وظائف التعديل")
        score = 0

        try:
            session = self.db_manager.get_session()

            # البحث عن بيانات موجودة أو إنشاء جديدة
            supplier = session.query(Supplier).first()
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار التكامل",
                    contact_person="شخص الاتصال",
                    phone="987654321"
                )
                session.add(supplier)
                session.flush()

            item = session.query(Item).first()
            if not item:
                item = Item(
                    code="INTEG-TEST-001",
                    name="صنف اختبار التكامل",
                    unit="قطعة"
                )
                session.add(item)
                session.flush()

            # إنشاء شحنة أصلية
            original_shipment = Shipment(
                shipment_number="INTEG-EDIT-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة الشحن الأصلية",
                notes="ملاحظات أصلية"
            )
            session.add(original_shipment)
            session.flush()

            # إضافة صنف أصلي
            original_item = ShipmentItem(
                shipment_id=original_shipment.id,
                item_id=item.id,
                quantity=5.0,
                unit_price=50.0,
                notes="صنف أصلي"
            )
            session.add(original_item)
            session.commit()

            self.log_audit("اختبار التكامل", "✅ تم إنشاء البيانات الأصلية")
            score += 10

            # محاكاة عملية التعديل
            # 1. تحميل الشحنة للتعديل
            shipment_to_edit = session.query(Shipment).filter(
                Shipment.id == original_shipment.id
            ).first()

            if shipment_to_edit:
                self.log_audit("اختبار التكامل", "✅ تم تحميل الشحنة للتعديل")
                score += 10

                # 2. تعديل البيانات
                setattr(shipment_to_edit, 'shipping_company', "شركة الشحن المحدثة")
                setattr(shipment_to_edit, 'notes', "ملاحظات محدثة")

                # 3. حذف الأصناف القديمة وإضافة جديدة
                session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == original_shipment.id
                ).delete()

                # إضافة أصناف جديدة
                new_item = ShipmentItem(
                    shipment_id=original_shipment.id,
                    item_id=item.id,
                    quantity=10.0,
                    unit_price=75.0,
                    notes="صنف محدث"
                )
                session.add(new_item)
                session.commit()

                self.log_audit("اختبار التكامل", "✅ تم تعديل البيانات وحفظها")
                score += 15

                # 4. التحقق من التعديلات
                updated_shipment = session.query(Shipment).filter(
                    Shipment.id == original_shipment.id
                ).first()

                if (updated_shipment and
                    getattr(updated_shipment, 'shipping_company', '') == "شركة الشحن المحدثة" and
                    getattr(updated_shipment, 'notes', '') == "ملاحظات محدثة"):
                    self.log_audit("اختبار التكامل", "✅ تم التحقق من تحديث البيانات الأساسية")
                    score += 10
                else:
                    self.log_audit("اختبار التكامل", "❌ فشل في تحديث البيانات الأساسية", "ERROR")

                # التحقق من الأصناف المحدثة
                updated_items = session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == original_shipment.id
                ).all()

                if (len(updated_items) == 1 and
                    getattr(updated_items[0], 'quantity', 0) == 10.0 and
                    getattr(updated_items[0], 'unit_price', 0) == 75.0):
                    self.log_audit("اختبار التكامل", "✅ تم التحقق من تحديث الأصناف")
                    score += 10
                else:
                    self.log_audit("اختبار التكامل", "❌ فشل في تحديث الأصناف", "ERROR")

                # 5. اختبار التحقق من تفرد رقم الشحنة في وضع التعديل
                duplicate_check = session.query(Shipment).filter(
                    Shipment.shipment_number == "INTEG-EDIT-001",
                    Shipment.id != original_shipment.id
                ).first()

                if not duplicate_check:
                    self.log_audit("اختبار التكامل", "✅ فحص التفرد يعمل بشكل صحيح في وضع التعديل")
                    score += 5
                else:
                    self.log_audit("اختبار التكامل", "❌ مشكلة في فحص التفرد", "ERROR")

            # تنظيف البيانات التجريبية فقط
            session.query(ShipmentItem).filter(ShipmentItem.shipment_id == original_shipment.id).delete()
            session.delete(original_shipment)
            # لا نحذف المورد والصنف إذا كانوا موجودين مسبقاً
            if getattr(item, 'code', '') == "INTEG-TEST-001":
                session.delete(item)
            if getattr(supplier, 'name', '') == "مورد اختبار التكامل":
                session.delete(supplier)
            session.commit()
            session.close()

        except Exception as e:
            self.log_audit("اختبار التكامل", f"خطأ في اختبار التكامل: {str(e)}", "ERROR")

        self.log_audit("اختبار التكامل", f"نتيجة اختبار التكامل: {score}/60")
        return score

def main():
    """الدالة الرئيسية"""
    try:
        print("🔍 أداة مراجعة إجراءات تعديل الشحنات")
        print("=" * 50)

        # إنشاء مراجع إجراءات التعديل
        auditor = ShipmentEditProceduresAuditor()

        # تشغيل المراجعة الشاملة
        results = auditor.run_comprehensive_audit()

        # تشغيل اختبار التكامل
        print("\n🧪 تشغيل اختبار التكامل العملي...")
        integration_score = auditor.test_edit_functionality_integration()
        results['detailed_scores']['integration_test'] = integration_score
        results['total_score'] += integration_score
        results['max_score'] += 60
        results['percentage'] = (results['total_score'] / results['max_score']) * 100

        # إعادة تحديد التقييم
        if results['percentage'] >= 90:
            results['grade'] = "ممتاز"
        elif results['percentage'] >= 80:
            results['grade'] = "جيد جداً"
        elif results['percentage'] >= 70:
            results['grade'] = "جيد"
        elif results['percentage'] >= 60:
            results['grade'] = "مقبول"
        else:
            results['grade'] = "يحتاج تحسين"

        print("\n" + "=" * 50)
        print("📊 تقرير المراجعة الشاملة:")
        print(f"• النتيجة الإجمالية: {results['total_score']}/{results['max_score']}")
        print(f"• النسبة المئوية: {results['percentage']:.1f}%")
        print(f"• التقييم: {results['grade']}")

        print("\n📋 النتائج التفصيلية:")
        for category, score in results['detailed_scores'].items():
            print(f"• {category}: {score}")

        if results['issues_found']:
            print(f"\n⚠️ المشاكل المكتشفة ({len(results['issues_found'])}):")
            for issue in results['issues_found']:
                print(f"  - {issue}")
        else:
            print("\n✅ لم يتم اكتشاف مشاكل")

        print("\n" + "=" * 50)
        if results['percentage'] >= 80:
            print("✅ إجراءات تعديل الشحنات في حالة ممتازة!")
        else:
            print("⚠️ إجراءات تعديل الشحنات تحتاج إلى تحسين")

        return results

    except Exception as e:
        print(f"❌ خطأ في مراجعة إجراءات التعديل: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
