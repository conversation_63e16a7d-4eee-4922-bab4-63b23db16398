#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار كامل لتدفق اختيار المورد والحفظ
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_complete_supplier_flow():
    """اختبار كامل لتدفق اختيار المورد"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار تدفق اختيار المورد الكامل ===")
        
        window = NewShipmentWindow()
        
        # الخطوة 1: التحقق من الحالة الأولية
        print("1️⃣ الحالة الأولية:")
        print(f"   - نص المورد: '{window.supplier_edit.text()}'")
        print(f"   - معرف المورد: {window.supplier_edit.property('supplier_id')}")
        print(f"   - التحقق من الصحة: {'نجح' if window.validate_data() else 'فشل'}")
        
        # الخطوة 2: ملء البيانات التجريبية (محاكاة اختيار المورد)
        print("\n2️⃣ ملء البيانات التجريبية:")
        window.fill_test_data()
        print(f"   - نص المورد: '{window.supplier_edit.text()}'")
        print(f"   - معرف المورد: {window.supplier_edit.property('supplier_id')}")
        
        # الخطوة 3: التحقق من صحة البيانات بعد ملء المورد
        print("\n3️⃣ التحقق من صحة البيانات:")
        validation_result = window.validate_data()
        print(f"   - نتيجة التحقق: {'✅ نجح' if validation_result else '❌ فشل'}")
        
        # الخطوة 4: اختبار محاكاة اختيار المورد يدوياً
        print("\n4️⃣ محاكاة اختيار المورد يدوياً:")
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Supplier
            
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            
            # الحصول على مورد للاختبار
            supplier = session.query(Supplier).filter(Supplier.is_active == True).first()
            
            if supplier:
                # محاكاة اختيار المورد (كما يحدث في search_supplier)
                supplier_text = f"{supplier.name} ({supplier.code})"
                window.supplier_edit.setText(supplier_text)
                window.selected_supplier_id = supplier.id
                window.supplier_edit.setProperty("supplier_id", supplier.id)
                
                print(f"   - تم اختيار المورد: {supplier.name}")
                print(f"   - نص المورد: '{window.supplier_edit.text()}'")
                print(f"   - معرف المورد: {window.supplier_edit.property('supplier_id')}")
                
                # التحقق من الصحة مرة أخرى
                final_validation = window.validate_data()
                print(f"   - التحقق النهائي: {'✅ نجح' if final_validation else '❌ فشل'}")
                
                session.close()
                return final_validation
            else:
                print("   - ❌ لم يتم العثور على موردين في النظام")
                return False
                
        except Exception as e:
            print(f"   - ❌ خطأ في محاكاة اختيار المورد: {str(e)}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_supplier_flow()
    print(f"\n{'='*50}")
    if success:
        print("🎉 تم حل مشكلة 'يرجى اختيار المورد' بنجاح!")
        print("✅ يمكن الآن حفظ الشحنات بعد اختيار المورد")
    else:
        print("⚠️ لا تزال هناك مشكلة في التحقق من المورد")
        print("❌ يرجى مراجعة الكود مرة أخرى")
