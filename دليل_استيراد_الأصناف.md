# دليل استيراد الأصناف من الإكسيل

## نظرة عامة
تم إضافة ميزة استيراد الأصناف من ملفات الإكسيل إلى نظام إدارة الشحنات. هذه الميزة تسمح بإضافة عدد كبير من الأصناف دفعة واحدة من ملف إكسيل.

## كيفية الاستخدام

### 1. إعداد ملف الإكسيل
يجب أن يحتوي ملف الإكسيل على الأعمدة التالية:

#### الأعمدة المطلوبة:
- **اسم الصنف**: اسم الصنف باللغة العربية (مطلوب)
- **كود الصنف**: كود فريد للصنف (مطلوب)

#### الأعمدة الاختيارية:
- **الاسم الإنجليزي**: اسم الصنف باللغة الإنجليزية
- **الوصف**: وصف تفصيلي للصنف
- **المجموعة**: اسم أو كود مجموعة الأصناف (يجب أن تكون موجودة مسبقاً)
- **وحدة القياس**: اسم أو رمز وحدة القياس (يجب أن تكون موجودة مسبقاً)
- **سعر التكلفة**: سعر تكلفة الصنف (رقم)
- **سعر البيع**: سعر بيع الصنف (رقم)
- **الوزن**: وزن الصنف بالكيلوجرام (رقم)
- **الأبعاد**: أبعاد الصنف (نص)
- **عبوة الكلي**: عدد القطع في العبوة الكلية (رقم صحيح)
- **عبوة الجزئي**: عدد القطع في العبوة الجزئية (رقم صحيح)
- **الجرام**: وزن الصنف بالجرام (رقم)
- **وزن الصنف**: وزن الصنف بالكيلوجرام (رقم)

### 2. خطوات الاستيراد

1. **افتح نافذة إدارة الأصناف**:
   - من القائمة الرئيسية، اختر "الأصناف"
   - اضغط على "إدارة الأصناف"

2. **اضغط على زر "استيراد من إكسيل"**:
   - ستجد الزر في أسفل النافذة بلون برتقالي

3. **اختر ملف الإكسيل**:
   - ستفتح نافذة اختيار الملف
   - اختر ملف الإكسيل المطلوب (.xlsx أو .xls)

4. **انتظر نتائج الاستيراد**:
   - سيظهر تقرير بعدد الأصناف المستوردة بنجاح
   - وعدد الأصناف التي فشل استيرادها مع تفاصيل الأخطاء

### 3. ملاحظات مهمة

#### قبل الاستيراد:
- تأكد من وجود مجموعات الأصناف المطلوبة في النظام
- تأكد من وجود وحدات القياس المطلوبة في النظام
- تأكد من أن أكواد الأصناف فريدة وغير مكررة

#### أثناء الاستيراد:
- سيتم تجاهل الصفوف التي تحتوي على أخطاء
- سيتم استيراد الصفوف الصحيحة فقط
- سيتم عرض تقرير مفصل بالأخطاء

#### بعد الاستيراد:
- سيتم تحديث جدول الأصناف تلقائياً
- يمكن مراجعة الأصناف المستوردة والتعديل عليها

### 4. خيارات الاستيراد الجديدة

عند الضغط على زر "استيراد من إكسيل"، ستظهر نافذة خيارات تسألك:

#### **ماذا تريد أن تفعل مع الأصناف الموجودة؟**

- **تحديث الأصناف الموجودة** (الخيار الافتراضي):
  - إذا كان الكود موجود في قاعدة البيانات، سيتم تحديث بياناته
  - مفيد عند تحديث معلومات الأصناف الموجودة

- **تجاهل الأصناف الموجودة**:
  - إذا كان الكود موجود في قاعدة البيانات، سيتم تجاهله
  - مفيد عند إضافة أصناف جديدة فقط

### 5. أمثلة على الأخطاء الشائعة

- **"اسم الصنف مطلوب"**: الخلية فارغة أو تحتوي على قيمة غير صالحة
- **"كود الصنف مطلوب"**: الخلية فارغة أو تحتوي على قيمة غير صالحة
- **"كود الصنف مكرر في الملف"**: نفس الكود مستخدم أكثر من مرة في الملف
- **"كود الصنف موجود بالفعل - تم التجاهل"**: الكود موجود في قاعدة البيانات وتم اختيار التجاهل
- **"المجموعة غير موجودة"**: اسم المجموعة غير موجود في النظام
- **"وحدة القياس غير موجودة"**: اسم وحدة القياس غير موجود في النظام

### 6. نصائح للحصول على أفضل النتائج

1. **استخدم الملف النموذجي**: تم إنشاء ملف `نموذج_استيراد_الأصناف.xlsx` كمرجع
2. **تحقق من البيانات**: راجع البيانات قبل الاستيراد
3. **تجنب الأكواد المكررة**: تأكد من عدم تكرار نفس الكود في الملف
4. **ابدأ بعينة صغيرة**: جرب استيراد عدد قليل من الأصناف أولاً
5. **أضف المجموعات أولاً**: تأكد من إضافة مجموعات الأصناف قبل الاستيراد
6. **أضف وحدات القياس أولاً**: تأكد من إضافة وحدات القياس قبل الاستيراد
7. **اختر الخيار المناسب**: حدد ما إذا كنت تريد تحديث الأصناف الموجودة أم تجاهلها

### 7. ملف الاختبار

تم إنشاء ملف `اختبار_استيراد_أصناف_مكررة.xlsx` لاختبار النظام المحسن:
- يحتوي على أكواد مكررة داخل الملف
- يحتوي على أكواد موجودة في قاعدة البيانات
- يحتوي على أكواد جديدة
- مفيد لاختبار جميع سيناريوهات الاستيراد

## الدعم الفني
في حالة مواجهة أي مشاكل، يرجى التحقق من:
- صيغة ملف الإكسيل (.xlsx أو .xls)
- وجود الأعمدة المطلوبة
- صحة البيانات في كل عمود
- وجود المجموعات ووحدات القياس في النظام
