#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تحديث جدول الشحنات بإضافة الحقول الجديدة
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from sqlalchemy import text

def update_shipment_table():
    """إضافة الحقول الجديدة لجدول الشحنات"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على الاتصال المباشر
        engine = db_manager.engine
        
        print("🔄 بدء تحديث جدول الشحنات...")
        
        # قائمة الحقول الجديدة المطلوب إضافتها
        new_fields = [
            {
                'name': 'shipping_policy',
                'definition': 'VARCHAR(200)',
                'comment': 'بوليصة الشحن'
            },
            {
                'name': 'container_number', 
                'definition': 'VARCHAR(100)',
                'comment': 'رقم الحاوية'
            }
        ]
        
        with engine.connect() as connection:
            # التحقق من وجود الجدول
            result = connection.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='shipments'
            """))
            
            if not result.fetchone():
                print("❌ جدول الشحنات غير موجود!")
                return False
            
            # الحصول على معلومات الأعمدة الحالية
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            existing_columns = [row[1] for row in result.fetchall()]
            
            print(f"📋 الأعمدة الموجودة: {len(existing_columns)}")
            
            # إضافة الحقول الجديدة
            for field in new_fields:
                if field['name'] not in existing_columns:
                    try:
                        alter_sql = f"""
                        ALTER TABLE shipments 
                        ADD COLUMN {field['name']} {field['definition']}
                        """
                        
                        connection.execute(text(alter_sql))
                        connection.commit()
                        print(f"✅ تم إضافة الحقل: {field['name']}")
                        
                    except Exception as e:
                        print(f"❌ خطأ في إضافة الحقل {field['name']}: {str(e)}")
                        
                else:
                    print(f"ℹ️ الحقل {field['name']} موجود بالفعل")
            
            # التحقق من النتيجة النهائية
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            final_columns = [row[1] for row in result.fetchall()]
            
            print(f"\n📊 إجمالي الأعمدة بعد التحديث: {len(final_columns)}")
            print("✅ تم تحديث جدول الشحنات بنجاح!")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_new_fields():
    """اختبار الحقول الجديدة"""
    try:
        from src.database.models import Shipment
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # اختبار إنشاء شحنة جديدة مع الحقول الجديدة
        print("\n🧪 اختبار الحقول الجديدة...")
        
        # البحث عن شحنة موجودة
        shipment = session.query(Shipment).first()
        if shipment:
            print(f"📦 اختبار الشحنة: {shipment.shipment_number}")
            
            # اختبار الوصول للحقول الجديدة
            shipping_policy = getattr(shipment, 'shipping_policy', 'غير موجود')
            container_number = getattr(shipment, 'container_number', 'غير موجود')
            
            print(f"   - بوليصة الشحن: {shipping_policy}")
            print(f"   - رقم الحاوية: {container_number}")
            
            # اختبار تحديث الحقول
            shipment.shipping_policy = "TEST-POLICY-001"
            shipment.container_number = "CONT-001"
            
            session.commit()
            print("✅ تم تحديث الحقول بنجاح!")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقول: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔧 تحديث جدول الشحنات - إضافة حقول جديدة")
    print("=" * 50)
    
    # تحديث الجدول
    if update_shipment_table():
        print("\n" + "=" * 50)
        
        # اختبار الحقول الجديدة
        if test_new_fields():
            print("\n🎉 تم تحديث وتجهيز جدول الشحنات بنجاح!")
            print("✅ يمكن الآن استخدام الحقول الجديدة:")
            print("   • بوليصة الشحن (shipping_policy)")
            print("   • رقم الحاوية (container_number)")
        else:
            print("\n⚠️ تم التحديث لكن هناك مشكلة في الاختبار")
    else:
        print("\n❌ فشل في تحديث جدول الشحنات")
