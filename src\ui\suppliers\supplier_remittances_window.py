# -*- coding: utf-8 -*-
"""
نافذة إدارة حوالات الموردين الجديدة - تصميم محسن
New Supplier Remittances Management Window - Enhanced Design
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QSplitter, QTabWidget,
                               QMessageBox, QStatusBar, QMenuBar, QToolBar)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QAction

from .supplier_remittances_tab import SupplierRemittancesTab


class SupplierRemittancesWindow(QMainWindow):
    """نافذة حوالات الموردين الجديدة - تصميم محسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("💸 إدارة حوالات الموردين - نظام محسن")
        
        # إعداد النافذة بحجم كبير لتجنب مشاكل التداخل
        self.setMinimumSize(1600, 900)
        self.resize(1800, 1000)
        
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 11)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # مؤقت لتحديث شريط الحالة
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(5000)  # تحديث كل 5 ثوان
        
        print("إنشاء نافذة حوالات الموردين جديدة...")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الجديدة"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # عنوان النافذة المحسن
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:0.5 #2980b9, stop:1 #1abc9c);
                border-radius: 12px;
                margin: 5px;
                min-height: 80px;
            }
        """)
        
        title_layout = QVBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel("💸 إدارة حوالات الموردين - النظام المحسن")
        title_label.setFont(QFont("Arial", 22, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: transparent;
                padding: 8px;
            }
        """)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("نظام شامل ومحسن لإدارة الحوالات المالية للموردين مع واجهة حديثة خالية من التداخل")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                background: transparent;
                padding: 4px;
            }
        """)
        title_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(title_frame)
        
        # تبويب حوالات الموردين الجديد
        self.remittances_tab = SupplierRemittancesTab()
        main_layout.addWidget(self.remittances_tab)
        
        print("تم إنشاء النافذة بنجاح، جاري عرضها...")
        
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")
        
        new_action = QAction("🆕 حوالة جديدة", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.remittances_tab.add_new_remittance)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("❌ إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة التحرير
        edit_menu = menubar.addMenu("✏️ تحرير")
        
        edit_action = QAction("📝 تعديل الحوالة", self)
        edit_action.setShortcut("Ctrl+E")
        edit_action.triggered.connect(self.remittances_tab.edit_selected_remittance)
        edit_menu.addAction(edit_action)
        
        delete_action = QAction("🗑️ حذف الحوالة", self)
        delete_action.setShortcut("Delete")
        delete_action.triggered.connect(self.remittances_tab.delete_selected_remittance)
        edit_menu.addAction(delete_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("👁️ عرض")
        
        refresh_action = QAction("🔄 تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.remittances_tab.refresh_data)
        view_menu.addAction(refresh_action)
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر حوالة جديدة
        new_action = QAction("🆕 حوالة جديدة", self)
        new_action.triggered.connect(self.remittances_tab.add_new_remittance)
        toolbar.addAction(new_action)
        
        toolbar.addSeparator()
        
        # زر تحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.remittances_tab.refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # زر حفظ
        save_action = QAction("💾 حفظ", self)
        save_action.triggered.connect(self.remittances_tab.save_current_remittance)
        toolbar.addAction(save_action)
        
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.showMessage("مرحباً بك في نظام إدارة حوالات الموردين المحسن - جاهز للاستخدام", 3000)
        
        # إضافة معلومات دائمة
        permanent_label = QLabel("النظام جاهز ✅")
        permanent_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 5px;")
        status_bar.addPermanentWidget(permanent_label)
        
    def update_status_bar(self):
        """تحديث شريط الحالة"""
        try:
            # عدد الحوالات
            total_remittances = self.remittances_tab.get_total_remittances_count()
            message = f"إجمالي الحوالات: {total_remittances} | النظام يعمل بكفاءة عالية"
            self.statusBar().showMessage(message)
        except Exception as e:
            self.statusBar().showMessage("جاري تحديث البيانات...")
            
    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        reply = QMessageBox.question(self, 'تأكيد الإغلاق',
                                   'هل تريد إغلاق نظام إدارة حوالات الموردين؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # إيقاف المؤقت
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
            event.accept()
            print("تم إغلاق نافذة حوالات الموردين")
        else:
            event.ignore()
            
    def show(self):
        """إظهار النافذة"""
        super().show()
        print("تم عرض النافذة")
