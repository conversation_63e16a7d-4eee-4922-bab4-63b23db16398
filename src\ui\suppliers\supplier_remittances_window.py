# -*- coding: utf-8 -*-
"""
نافذة حوالات الموردين المنفصلة
Supplier Remittances Standalone Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QMenuBar, QMenu, 
                               QToolBar, QStatusBar, QMessageBox, QSplitter)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QAction, QIcon

from .supplier_remittances_tab import SupplierRemittancesTab


class SupplierRemittancesWindow(QMainWindow):
    """نافذة حوالات الموردين المنفصلة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة حوالات الموردين - نظام إدارة الشحنات")
        
        # تعيين النافذة لتفتح في وضع ملء الشاشة
        self.setWindowState(Qt.WindowMaximized)
        
        # تعيين الحد الأدنى لحجم النافذة
        self.setMinimumSize(1200, 800)
        
        # تعيين أيقونة النافذة
        self.setWindowIcon(QIcon("assets/icons/remittances.png"))
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # مؤقت لتحديث شريط الحالة
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(5000)  # تحديث كل 5 ثوان

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 5px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)
        
        title_label = QLabel("💸 إدارة حوالات الموردين")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: transparent;
                padding: 10px;
            }
        """)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("نظام شامل لإدارة الحوالات المالية للموردين مع تتبع المراحل والتأكيدات")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                background: transparent;
                padding: 5px;
            }
        """)
        title_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(title_frame)
        
        # تبويب حوالات الموردين
        self.remittances_tab = SupplierRemittancesTab()
        main_layout.addWidget(self.remittances_tab)
        
        # شريط الأزرار السفلي
        self.create_bottom_buttons(main_layout)

    def create_bottom_buttons(self, layout):
        """إنشاء شريط الأزرار السفلي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 2px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 15, 20, 15)
        buttons_layout.addStretch()

        refresh_button = QPushButton("🔄 تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        export_button = QPushButton("📤 تصدير البيانات")
        export_button.clicked.connect(self.export_data)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)

        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addWidget(close_button)
        
        layout.addWidget(buttons_frame)

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #3498db;
            }
            QMenu {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
            }
            QMenu::item {
                padding: 8px 20px;
                color: #2c3e50;
            }
            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # قائمة الحوالات
        remittances_menu = menubar.addMenu("الحوالات")
        
        new_remittance_action = QAction("حوالة جديدة", self)
        new_remittance_action.triggered.connect(self.new_remittance)
        remittances_menu.addAction(new_remittance_action)
        
        remittances_menu.addSeparator()
        
        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.triggered.connect(self.refresh_data)
        remittances_menu.addAction(refresh_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        remittances_report_action = QAction("تقرير الحوالات", self)
        remittances_report_action.triggered.connect(self.generate_remittances_report)
        reports_menu.addAction(remittances_report_action)
        
        pending_remittances_action = QAction("الحوالات المعلقة", self)
        pending_remittances_action.triggered.connect(self.show_pending_remittances)
        reports_menu.addAction(pending_remittances_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("المساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #ecf0f1;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QToolBar::separator {
                background-color: #bdc3c7;
                width: 2px;
                margin: 5px;
            }
        """)
        
        # زر حوالة جديدة
        new_action = QAction("➕ حوالة جديدة", self)
        new_action.triggered.connect(self.new_remittance)
        new_action.setToolTip("إنشاء حوالة جديدة")
        toolbar.addAction(new_action)
        
        toolbar.addSeparator()
        
        # زر تحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        refresh_action.setToolTip("تحديث البيانات")
        toolbar.addAction(refresh_action)
        
        # زر تصدير
        export_action = QAction("📤 تصدير", self)
        export_action.triggered.connect(self.export_data)
        export_action.setToolTip("تصدير البيانات")
        toolbar.addAction(export_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 5px;
            }
        """)
        
        status_bar.showMessage("مرحباً بك في نظام إدارة حوالات الموردين")
        self.update_status_bar()

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        try:
            # يمكن إضافة إحصائيات هنا
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.statusBar().showMessage(f"آخر تحديث: {current_time}")
        except Exception as e:
            print(f"خطأ في تحديث شريط الحالة: {e}")

    def new_remittance(self):
        """إنشاء حوالة جديدة"""
        self.remittances_tab.clear_form()
        self.remittances_tab.remittance_number_edit.setFocus()

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            self.remittances_tab.load_data()
            QMessageBox.information(self, "تم", "تم تحديث البيانات بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحديث البيانات: {str(e)}")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(
            self,
            "تصدير البيانات",
            "سيتم تطوير ميزة تصدير البيانات قريباً"
        )

    def generate_remittances_report(self):
        """إنشاء تقرير الحوالات"""
        QMessageBox.information(
            self,
            "تقرير الحوالات",
            "سيتم تطوير تقارير الحوالات قريباً"
        )

    def show_pending_remittances(self):
        """عرض الحوالات المعلقة"""
        # تطبيق فلتر للحوالات المعلقة
        self.remittances_tab.status_filter_combo.setCurrentText("في الانتظار")
        self.remittances_tab.perform_search()

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self,
            "حول نظام حوالات الموردين",
            """
            <h3>نظام إدارة حوالات الموردين</h3>
            <p>نظام شامل ومتقدم لإدارة الحوالات المالية للموردين</p>
            
            <h4>الميزات الرئيسية:</h4>
            <ul>
                <li>إدارة الحوالات لعدة موردين في حوالة واحدة</li>
                <li>تتبع مراحل الحوالة من الإنشاء للترحيل</li>
                <li>انتظار تأكيد الاستلام من البنك الخارجي</li>
                <li>ترحيل تلقائي للحسابات بعد التأكيد</li>
                <li>إدارة العملات وأسعار الصرف</li>
                <li>تتبع الرسوم والمعلومات البنكية</li>
            </ul>
            
            <p><b>الإصدار:</b> 1.0</p>
            <p><b>تاريخ الإصدار:</b> 2024</p>
            """
        )

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإغلاق",
            "هل أنت متأكد من إغلاق نافذة حوالات الموردين؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إيقاف المؤقت
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
            event.accept()
        else:
            event.ignore()
