#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة order.status في نظام طلبات الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def test_purchase_orders_status_fix():
    """اختبار إصلاح مشكلة order.status"""
    print("🔄 بدء اختبار إصلاح مشكلة order.status...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار فتح نافذة قائمة الطلبات
        window.show_orders_list()
        print("✅ تم فتح نافذة قائمة الطلبات بنجاح")
        
        # اختبار تحميل الطلبات
        if hasattr(window, 'orders_list_window') and window.orders_list_window:
            window.orders_list_window.load_orders()
            print("✅ تم تحميل الطلبات بنجاح - لا توجد أخطاء order.status")
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_purchase_orders_status_fix()
    sys.exit(0 if success else 1)
