#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار فصل التعبئة التلقائية عن وضع التعديل
Test Auto-Fill Separation from Edit Mode
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.ui.widgets.smart_shipping_company_widget import SmartShippingCompanyWidget
    from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QLineEdit
    from PySide6.QtCore import Qt
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد المكونات المطلوبة: {e}")

class AutoFillSeparationTester:
    """مختبر فصل التعبئة التلقائية"""
    
    def __init__(self):
        """تهيئة المختبر"""
        self.test_results = []
        
    def log_test(self, test_name: str, result: str, status: str):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {test_name}: {result} - {status}"
        self.test_results.append(log_entry)
        print(log_entry)
    
    def test_widget_auto_fill_control(self):
        """اختبار التحكم في التعبئة التلقائية للواجهة الذكية"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            # إنشاء واجهة ذكية لشركة الشحن
            widget = SmartShippingCompanyWidget()
            
            # اختبار الحالة الافتراضية
            if widget.is_auto_fill_enabled():
                self.log_test("الحالة الافتراضية", "التعبئة التلقائية مفعلة افتراضياً", "✅ نجح")
            else:
                self.log_test("الحالة الافتراضية", "التعبئة التلقائية معطلة افتراضياً", "❌ فشل")
            
            # اختبار تعطيل التعبئة التلقائية
            widget.set_auto_fill_enabled(False)
            if not widget.is_auto_fill_enabled():
                self.log_test("تعطيل التعبئة التلقائية", "تم تعطيل التعبئة التلقائية بنجاح", "✅ نجح")
            else:
                self.log_test("تعطيل التعبئة التلقائية", "فشل في تعطيل التعبئة التلقائية", "❌ فشل")
            
            # اختبار تعيين اسم الشركة بدون تحقق تلقائي
            widget.set_company_name("MAERSK LINE", auto_validate=False)
            company_name = widget.get_company_name()
            if company_name == "MAERSK LINE":
                self.log_test("تعيين الاسم بدون تحقق", "تم تعيين اسم الشركة بدون تحقق تلقائي", "✅ نجح")
            else:
                self.log_test("تعيين الاسم بدون تحقق", f"فشل في تعيين الاسم: {company_name}", "❌ فشل")
            
            # اختبار إعادة تفعيل التعبئة التلقائية
            widget.set_auto_fill_enabled(True)
            if widget.is_auto_fill_enabled():
                self.log_test("إعادة تفعيل التعبئة التلقائية", "تم إعادة تفعيل التعبئة التلقائية بنجاح", "✅ نجح")
            else:
                self.log_test("إعادة تفعيل التعبئة التلقائية", "فشل في إعادة تفعيل التعبئة التلقائية", "❌ فشل")
            
            # اختبار تعيين اسم الشركة مع تحقق تلقائي
            widget.set_company_name("MSC", auto_validate=True)
            company_name = widget.get_company_name()
            if company_name == "MSC":
                self.log_test("تعيين الاسم مع تحقق", "تم تعيين اسم الشركة مع تحقق تلقائي", "✅ نجح")
            else:
                self.log_test("تعيين الاسم مع تحقق", f"فشل في تعيين الاسم: {company_name}", "❌ فشل")
            
            return True
            
        except Exception as e:
            self.log_test("اختبار الواجهة الذكية", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_edit_mode_simulation(self):
        """محاكاة وضع التعديل"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            # محاكاة وضع التعديل
            widget = SmartShippingCompanyWidget()
            
            # تعطيل التعبئة التلقائية (وضع التعديل)
            widget.set_auto_fill_enabled(False)
            
            # تعيين اسم شركة موجود (كما لو كان من قاعدة البيانات)
            widget.set_company_name("COSCO SHIPPING", auto_validate=False)
            
            # التحقق من أن التعبئة التلقائية معطلة
            if not widget.is_auto_fill_enabled():
                self.log_test("محاكاة وضع التعديل", "التعبئة التلقائية معطلة في وضع التعديل", "✅ نجح")
            else:
                self.log_test("محاكاة وضع التعديل", "التعبئة التلقائية لا تزال مفعلة", "❌ فشل")
            
            # التحقق من أن اسم الشركة تم تعيينه بدون تحقق
            company_name = widget.get_company_name()
            if company_name == "COSCO SHIPPING":
                self.log_test("تعيين البيانات في وضع التعديل", "تم تعيين البيانات بدون تحقق تلقائي", "✅ نجح")
            else:
                self.log_test("تعيين البيانات في وضع التعديل", f"فشل في تعيين البيانات: {company_name}", "❌ فشل")
            
            return True
            
        except Exception as e:
            self.log_test("محاكاة وضع التعديل", f"خطأ في المحاكاة: {str(e)}", "❌ فشل")
            return False
    
    def test_new_mode_simulation(self):
        """محاكاة وضع الإنشاء الجديد"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            # محاكاة وضع الإنشاء الجديد
            widget = SmartShippingCompanyWidget()
            
            # التأكد من أن التعبئة التلقائية مفعلة (الوضع الافتراضي)
            if widget.is_auto_fill_enabled():
                self.log_test("محاكاة وضع الإنشاء الجديد", "التعبئة التلقائية مفعلة في وضع الإنشاء الجديد", "✅ نجح")
            else:
                self.log_test("محاكاة وضع الإنشاء الجديد", "التعبئة التلقائية معطلة", "❌ فشل")
            
            # تعيين اسم شركة جديد مع تحقق تلقائي
            widget.set_company_name("EVERGREEN", auto_validate=True)
            
            # التحقق من أن اسم الشركة تم تعيينه
            company_name = widget.get_company_name()
            if company_name == "EVERGREEN":
                self.log_test("تعيين البيانات في وضع الإنشاء", "تم تعيين البيانات مع تحقق تلقائي", "✅ نجح")
            else:
                self.log_test("تعيين البيانات في وضع الإنشاء", f"فشل في تعيين البيانات: {company_name}", "❌ فشل")
            
            return True
            
        except Exception as e:
            self.log_test("محاكاة وضع الإنشاء الجديد", f"خطأ في المحاكاة: {str(e)}", "❌ فشل")
            return False
    
    def test_mode_switching(self):
        """اختبار التبديل بين الأوضاع"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            widget = SmartShippingCompanyWidget()
            
            # البدء في وضع الإنشاء الجديد
            initial_state = widget.is_auto_fill_enabled()
            
            # التبديل إلى وضع التعديل
            widget.set_auto_fill_enabled(False)
            edit_state = widget.is_auto_fill_enabled()
            
            # العودة إلى وضع الإنشاء الجديد
            widget.set_auto_fill_enabled(True)
            final_state = widget.is_auto_fill_enabled()
            
            if initial_state and not edit_state and final_state:
                self.log_test("التبديل بين الأوضاع", "التبديل بين الأوضاع يعمل بشكل صحيح", "✅ نجح")
            else:
                self.log_test("التبديل بين الأوضاع", f"فشل في التبديل: {initial_state} -> {edit_state} -> {final_state}", "❌ فشل")
            
            return True
            
        except Exception as e:
            self.log_test("التبديل بين الأوضاع", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار فصل التعبئة التلقائية عن وضع التعديل")
        print("=" * 60)
        
        tests = [
            ("اختبار التحكم في الواجهة الذكية", self.test_widget_auto_fill_control),
            ("محاكاة وضع التعديل", self.test_edit_mode_simulation),
            ("محاكاة وضع الإنشاء الجديد", self.test_new_mode_simulation),
            ("اختبار التبديل بين الأوضاع", self.test_mode_switching)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            print(f"\n🔍 {test_name}:")
            print("-" * 40)
            
            try:
                if test_function():
                    passed_tests += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {str(e)}")
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("✅ جميع الاختبارات نجحت! فصل التعبئة التلقائية يعمل بشكل صحيح")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه")
        
        return passed_tests, total_tests

def main():
    """الدالة الرئيسية"""
    try:
        tester = AutoFillSeparationTester()
        passed, total = tester.run_all_tests()
        
        print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
