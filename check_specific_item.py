# -*- coding: utf-8 -*-
"""
فحص صنف محدد في قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Item

def check_specific_item(item_code):
    """فحص صنف محدد بالكود"""
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        print(f"🔍 البحث عن الصنف بالكود: '{item_code}'")
        print("=" * 50)
        
        # البحث عن الصنف
        items = session.query(Item).filter_by(code=item_code).all()
        
        if not items:
            print(f"❌ لم يتم العثور على صنف بالكود '{item_code}'")
            return False
        
        print(f"✅ تم العثور على {len(items)} صنف بالكود '{item_code}':")
        print()
        
        for i, item in enumerate(items, 1):
            print(f"📦 الصنف {i}:")
            print(f"   ID: {item.id}")
            print(f"   الاسم: {item.name}")
            print(f"   الاسم الإنجليزي: {item.name_en or 'غير محدد'}")
            print(f"   الوصف: {item.description or 'غير محدد'}")
            print(f"   المجموعة: {item.group.name if item.group else 'غير محدد'}")
            print(f"   وحدة القياس: {item.unit.name if item.unit else 'غير محدد'}")
            print(f"   سعر التكلفة: {item.cost_price}")
            print(f"   سعر البيع: {item.selling_price}")
            print(f"   نشط: {'نعم' if item.is_active else 'لا'}")
            print(f"   تاريخ الإنشاء: {item.created_at}")
            print(f"   تاريخ التحديث: {item.updated_at}")
            print()
        
        return len(items) > 1  # إرجاع True إذا كان مكرر
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
        return False
    
    finally:
        session.close()

def list_all_items_with_similar_code(partial_code):
    """عرض جميع الأصناف التي تحتوي على جزء من الكود"""
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        print(f"🔍 البحث عن الأصناف التي تحتوي على: '{partial_code}'")
        print("=" * 50)
        
        # البحث عن الأصناف
        items = session.query(Item).filter(Item.code.like(f'%{partial_code}%')).all()
        
        if not items:
            print(f"❌ لم يتم العثور على أصناف تحتوي على '{partial_code}'")
            return
        
        print(f"✅ تم العثور على {len(items)} صنف:")
        print()
        
        for i, item in enumerate(items, 1):
            print(f"{i:3d}. الكود: '{item.code}' - الاسم: {item.name}")
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
    
    finally:
        session.close()

def get_database_stats():
    """إحصائيات قاعدة البيانات"""
    
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    try:
        print("📊 إحصائيات قاعدة البيانات:")
        print("=" * 30)
        
        # عدد الأصناف الإجمالي
        total_items = session.query(Item).count()
        print(f"إجمالي الأصناف: {total_items}")
        
        # عدد الأصناف النشطة
        active_items = session.query(Item).filter_by(is_active=True).count()
        print(f"الأصناف النشطة: {active_items}")
        
        # عدد الأصناف غير النشطة
        inactive_items = total_items - active_items
        print(f"الأصناف غير النشطة: {inactive_items}")
        
        # آخر 5 أصناف تم إضافتها
        print("\nآخر 5 أصناف تم إضافتها:")
        recent_items = session.query(Item).order_by(Item.created_at.desc()).limit(5).all()
        for i, item in enumerate(recent_items, 1):
            print(f"{i}. {item.code} - {item.name} ({item.created_at.strftime('%Y-%m-%d %H:%M')})")
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")
    
    finally:
        session.close()

if __name__ == "__main__":
    print("أداة فحص الأصناف المحددة")
    print("=" * 30)
    
    # فحص الكود المشكل
    problem_code = '0197-001-'
    print("1. فحص الكود المشكل")
    check_specific_item(problem_code)
    
    print("\n" + "="*50 + "\n")
    
    # البحث عن أكواد مشابهة
    print("2. البحث عن أكواد مشابهة")
    list_all_items_with_similar_code('0197-001')
    
    print("\n" + "="*50 + "\n")
    
    # إحصائيات قاعدة البيانات
    print("3. إحصائيات قاعدة البيانات")
    get_database_stats()
