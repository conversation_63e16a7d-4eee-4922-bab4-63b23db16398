#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص متخصصة لمشكلة التعليق في وضع التعديل
Specialized Debugger for Edit Mode Freeze Issues
"""

import sys
import os
import time
import threading
import traceback
from datetime import datetime
import signal
import psutil
from contextlib import contextmanager

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EditModeDebugger:
    """مشخص وضع التعديل"""
    
    def __init__(self):
        self.monitoring = False
        self.freeze_detected = False
        self.last_activity = time.time()
        self.activity_threshold = 5  # ثواني
        self.debug_log = []
        
    def log_activity(self, activity):
        """تسجيل النشاط"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {activity}"
        self.debug_log.append(log_entry)
        print(f"🔍 {log_entry}")
        self.last_activity = time.time()
        
    def start_freeze_detection(self):
        """بدء كشف التعليق"""
        self.monitoring = True
        self.freeze_thread = threading.Thread(target=self._monitor_freeze, daemon=True)
        self.freeze_thread.start()
        self.log_activity("بدء مراقبة التعليق")
        
    def stop_freeze_detection(self):
        """إيقاف كشف التعليق"""
        self.monitoring = False
        self.log_activity("إيقاف مراقبة التعليق")
        
    def _monitor_freeze(self):
        """مراقبة التعليق"""
        while self.monitoring:
            current_time = time.time()
            if current_time - self.last_activity > self.activity_threshold:
                if not self.freeze_detected:
                    self.freeze_detected = True
                    self.log_activity("⚠️ تم كشف تعليق محتمل!")
                    self._handle_freeze_detection()
            else:
                self.freeze_detected = False
                
            time.sleep(1)
            
    def _handle_freeze_detection(self):
        """معالجة كشف التعليق"""
        self.log_activity("📋 تفريغ معلومات التعليق...")
        
        # تفريغ تتبع المكدس
        self._dump_all_stacks()
        
        # تحليل الخيوط
        self._analyze_threads()
        
        # فحص قاعدة البيانات
        self._check_database_locks()
        
    def _dump_all_stacks(self):
        """تفريغ جميع المكدسات"""
        self.log_activity("📋 تفريغ مكدسات جميع الخيوط:")
        
        for thread_id, frame in sys._current_frames().items():
            thread_name = "غير معروف"
            for thread in threading.enumerate():
                if thread.ident == thread_id:
                    thread_name = thread.name
                    break
                    
            self.log_activity(f"🧵 خيط: {thread_name} (ID: {thread_id})")
            
            # تفريغ المكدس
            stack_trace = traceback.format_stack(frame)
            for line in stack_trace[-10:]:  # آخر 10 أسطر
                self.log_activity(f"   {line.strip()}")
                
    def _analyze_threads(self):
        """تحليل الخيوط"""
        threads = threading.enumerate()
        self.log_activity(f"📊 تحليل الخيوط - العدد: {len(threads)}")
        
        for thread in threads:
            status = "نشط" if thread.is_alive() else "متوقف"
            daemon = "خدمة" if thread.daemon else "رئيسي"
            self.log_activity(f"🧵 {thread.name}: {status} ({daemon})")
            
    def _check_database_locks(self):
        """فحص أقفال قاعدة البيانات"""
        try:
            self.log_activity("🗄️ فحص أقفال قاعدة البيانات...")
            
            from src.database.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # فحص الاتصالات النشطة
            if hasattr(db_manager.engine.pool, 'checkedout'):
                active_connections = len(db_manager.engine.pool.checkedout())
                self.log_activity(f"📊 الاتصالات النشطة: {active_connections}")
                
            # فحص الجلسات المفتوحة
            from sqlalchemy.orm import sessionmaker
            Session = sessionmaker(bind=db_manager.engine)
            
            # محاولة إنشاء جلسة اختبار
            test_session = Session()
            try:
                # اختبار بسيط
                result = test_session.execute("SELECT 1").fetchone()
                self.log_activity("✅ قاعدة البيانات تستجيب")
            except Exception as e:
                self.log_activity(f"❌ قاعدة البيانات لا تستجيب: {e}")
            finally:
                test_session.close()
                
        except Exception as e:
            self.log_activity(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            
    @contextmanager
    def monitor_operation(self, operation_name):
        """مراقب العمليات"""
        self.log_activity(f"🚀 بدء عملية: {operation_name}")
        start_time = time.time()
        
        try:
            yield
        except Exception as e:
            self.log_activity(f"❌ خطأ في {operation_name}: {e}")
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            self.log_activity(f"✅ انتهاء عملية: {operation_name} ({duration:.3f}s)")
            
    def save_debug_log(self):
        """حفظ سجل التشخيص"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"debug_tools/edit_mode_debug_{timestamp}.log"
        
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("سجل تشخيص وضع التعديل\n")
            f.write("=" * 60 + "\n")
            f.write(f"التاريخ: {datetime.now()}\n\n")
            
            for entry in self.debug_log:
                f.write(entry + "\n")
                
        self.log_activity(f"💾 تم حفظ السجل: {log_file}")
        return log_file

# إنشاء مثيل عام
debugger = EditModeDebugger()

def test_edit_mode_operations():
    """اختبار عمليات وضع التعديل"""
    print("🔍 بدء اختبار عمليات وضع التعديل...")
    
    debugger.start_freeze_detection()
    
    try:
        # اختبار فتح النافذة
        with debugger.monitor_operation("فتح_نافذة_التعديل"):
            debugger.log_activity("محاولة استيراد NewShipmentWindow...")
            from src.ui.shipments.new_shipment_window import NewShipmentWindow
            debugger.log_activity("✅ تم استيراد NewShipmentWindow")
            
            debugger.log_activity("محاولة إنشاء QApplication...")
            from PySide6.QtWidgets import QApplication
            
            if not QApplication.instance():
                app = QApplication(sys.argv)
                debugger.log_activity("✅ تم إنشاء QApplication")
            else:
                debugger.log_activity("✅ QApplication موجود مسبقاً")
                
        # اختبار تحميل البيانات
        with debugger.monitor_operation("تحميل_بيانات_الشحنة"):
            debugger.log_activity("محاولة الاتصال بقاعدة البيانات...")
            
            from src.database.database_manager import DatabaseManager
            from src.database.models import Shipment
            
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            
            try:
                # البحث عن شحنة للاختبار
                shipment = session.query(Shipment).first()
                if shipment:
                    debugger.log_activity(f"✅ تم العثور على شحنة: {shipment.id}")
                else:
                    debugger.log_activity("⚠️ لا توجد شحنات في قاعدة البيانات")
                    
            finally:
                session.close()
                debugger.log_activity("✅ تم إغلاق جلسة قاعدة البيانات")
                
        # اختبار إنشاء النافذة
        with debugger.monitor_operation("إنشاء_نافذة_التعديل"):
            if 'shipment' in locals() and shipment:
                debugger.log_activity(f"إنشاء نافذة تعديل للشحنة {shipment.id}...")
                
                # محاولة إنشاء النافذة
                window = NewShipmentWindow(shipment_id=shipment.id)
                debugger.log_activity("✅ تم إنشاء النافذة")
                
                # محاولة عرض النافذة
                window.show()
                debugger.log_activity("✅ تم عرض النافذة")
                
                # اختبار عملية الحفظ
                with debugger.monitor_operation("اختبار_الحفظ"):
                    debugger.log_activity("محاولة الحفظ...")
                    
                    # محاكاة الحفظ
                    time.sleep(1)
                    debugger.log_activity("✅ تم الحفظ بنجاح")
                    
                window.close()
                debugger.log_activity("✅ تم إغلاق النافذة")
                
    except Exception as e:
        debugger.log_activity(f"❌ خطأ في الاختبار: {e}")
        traceback.print_exc()
        
    finally:
        debugger.stop_freeze_detection()
        log_file = debugger.save_debug_log()
        
        print(f"\n✅ تم الانتهاء من الاختبار")
        print(f"📋 سجل التشخيص: {log_file}")

if __name__ == "__main__":
    test_edit_mode_operations()
