#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عملي لفصل التعبئة التلقائية عن وضع التعديل
Practical Test for Auto-Fill Separation from Edit Mode
"""

import sys
import os
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import Shipment, Supplier
    from src.ui.shipments.new_shipment_window import NewShipmentWindow
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد المكونات المطلوبة: {e}")

class PracticalAutoFillSeparationTester:
    """مختبر عملي لفصل التعبئة التلقائية"""
    
    def __init__(self):
        """تهيئة المختبر"""
        self.db_manager = DatabaseManager()
        self.test_results = []
        self.test_shipment_id = None
        
    def log_test(self, test_name: str, result: str, status: str):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {test_name}: {result} - {status}"
        self.test_results.append(log_entry)
        print(log_entry)
    
    def setup_test_data(self):
        """إعداد بيانات الاختبار"""
        try:
            session = self.db_manager.get_session()
            
            # البحث عن مورد موجود أو إنشاء واحد جديد
            supplier = session.query(Supplier).first()
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار فصل التعبئة التلقائية",
                    contact_person="شخص الاتصال",
                    phone="*********"
                )
                session.add(supplier)
                session.commit()
            
            # إنشاء شحنة تجريبية
            test_shipment = Shipment(
                shipment_number="AUTO-FILL-SEP-TEST-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="MAERSK LINE",
                notes="شحنة اختبار فصل التعبئة التلقائية"
            )
            session.add(test_shipment)
            session.commit()
            
            self.test_shipment_id = test_shipment.id
            session.close()
            
            self.log_test("إعداد البيانات", f"تم إنشاء شحنة تجريبية رقم {self.test_shipment_id}", "✅ نجح")
            return True
            
        except Exception as e:
            self.log_test("إعداد البيانات", f"فشل في إعداد البيانات: {str(e)}", "❌ فشل")
            return False
    
    def test_new_shipment_window_creation_mode(self):
        """اختبار نافذة الشحنة الجديدة في وضع الإنشاء"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            # إنشاء نافذة شحنة جديدة (وضع الإنشاء)
            window = NewShipmentWindow(parent=None, shipment_id=None)
            
            # التحقق من وضع التعديل
            if not window.is_edit_mode:
                self.log_test("وضع الإنشاء", "النافذة في وضع الإنشاء الجديد", "✅ نجح")
            else:
                self.log_test("وضع الإنشاء", "النافذة في وضع التعديل بدلاً من الإنشاء", "❌ فشل")
                return False
            
            # التحقق من حالة التعبئة التلقائية
            if hasattr(window, 'smart_shipping_company_widget'):
                if window.smart_shipping_company_widget.is_auto_fill_enabled():
                    self.log_test("التعبئة التلقائية في وضع الإنشاء", "التعبئة التلقائية مفعلة", "✅ نجح")
                else:
                    self.log_test("التعبئة التلقائية في وضع الإنشاء", "التعبئة التلقائية معطلة", "❌ فشل")
                    return False
            else:
                self.log_test("التعبئة التلقائية في وضع الإنشاء", "الواجهة الذكية غير موجودة", "⚠️ تحذير")
            
            window.close()
            return True
            
        except Exception as e:
            self.log_test("وضع الإنشاء", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_edit_shipment_window_edit_mode(self):
        """اختبار نافذة الشحنة في وضع التعديل"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            if not self.test_shipment_id:
                self.log_test("وضع التعديل", "لا يوجد معرف شحنة للاختبار", "❌ فشل")
                return False
            
            # إنشاء نافذة شحنة للتعديل
            window = NewShipmentWindow(parent=None, shipment_id=self.test_shipment_id)
            
            # التحقق من وضع التعديل
            if window.is_edit_mode:
                self.log_test("وضع التعديل", "النافذة في وضع التعديل", "✅ نجح")
            else:
                self.log_test("وضع التعديل", "النافذة في وضع الإنشاء بدلاً من التعديل", "❌ فشل")
                return False
            
            # انتظار تحميل البيانات
            def check_auto_fill_status():
                try:
                    # التحقق من حالة التعبئة التلقائية
                    if hasattr(window, 'smart_shipping_company_widget'):
                        if not window.smart_shipping_company_widget.is_auto_fill_enabled():
                            self.log_test("التعبئة التلقائية في وضع التعديل", "التعبئة التلقائية معطلة", "✅ نجح")
                        else:
                            self.log_test("التعبئة التلقائية في وضع التعديل", "التعبئة التلقائية لا تزال مفعلة", "❌ فشل")
                        
                        # التحقق من تحميل البيانات
                        company_name = window.smart_shipping_company_widget.get_company_name()
                        if company_name == "MAERSK LINE":
                            self.log_test("تحميل البيانات في وضع التعديل", "تم تحميل اسم الشركة بدون تحقق تلقائي", "✅ نجح")
                        else:
                            self.log_test("تحميل البيانات في وضع التعديل", f"اسم الشركة غير صحيح: {company_name}", "❌ فشل")
                    else:
                        self.log_test("التعبئة التلقائية في وضع التعديل", "الواجهة الذكية غير موجودة", "⚠️ تحذير")
                    
                    window.close()
                except Exception as e:
                    self.log_test("فحص حالة التعبئة التلقائية", f"خطأ: {str(e)}", "❌ فشل")
                    window.close()
            
            # تأخير للسماح بتحميل البيانات
            QTimer.singleShot(2000, check_auto_fill_status)
            
            # تشغيل حلقة الأحداث لفترة قصيرة
            app.processEvents()
            
            return True
            
        except Exception as e:
            self.log_test("وضع التعديل", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_mode_switching_in_window(self):
        """اختبار التبديل بين الأوضاع في النافذة"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            if not self.test_shipment_id:
                self.log_test("التبديل بين الأوضاع", "لا يوجد معرف شحنة للاختبار", "❌ فشل")
                return False
            
            # بدء بنافذة تعديل
            window = NewShipmentWindow(parent=None, shipment_id=self.test_shipment_id)
            
            # انتظار تحميل البيانات ثم اختبار التبديل
            def test_switching():
                try:
                    # التحقق من وضع التعديل
                    if window.is_edit_mode and hasattr(window, 'smart_shipping_company_widget'):
                        if not window.smart_shipping_company_widget.is_auto_fill_enabled():
                            self.log_test("حالة وضع التعديل", "التعبئة التلقائية معطلة في وضع التعديل", "✅ نجح")
                        else:
                            self.log_test("حالة وضع التعديل", "التعبئة التلقائية مفعلة في وضع التعديل", "❌ فشل")
                    
                    # محاكاة إنشاء شحنة جديدة (التبديل إلى وضع الإنشاء)
                    window.current_shipment_id = None
                    window.is_edit_mode = False
                    window.configure_auto_fill_mode()
                    
                    # التحقق من وضع الإنشاء الجديد
                    if hasattr(window, 'smart_shipping_company_widget'):
                        if window.smart_shipping_company_widget.is_auto_fill_enabled():
                            self.log_test("التبديل إلى وضع الإنشاء", "التعبئة التلقائية مفعلة في وضع الإنشاء", "✅ نجح")
                        else:
                            self.log_test("التبديل إلى وضع الإنشاء", "التعبئة التلقائية معطلة في وضع الإنشاء", "❌ فشل")
                    
                    window.close()
                except Exception as e:
                    self.log_test("اختبار التبديل", f"خطأ: {str(e)}", "❌ فشل")
                    window.close()
            
            # تأخير للسماح بتحميل البيانات
            QTimer.singleShot(2000, test_switching)
            
            # تشغيل حلقة الأحداث لفترة قصيرة
            app.processEvents()
            
            return True
            
        except Exception as e:
            self.log_test("التبديل بين الأوضاع", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def cleanup_test_data(self):
        """تنظيف بيانات الاختبار"""
        try:
            if self.test_shipment_id:
                session = self.db_manager.get_session()
                
                # حذف الشحنة التجريبية
                shipment = session.query(Shipment).filter(Shipment.id == self.test_shipment_id).first()
                if shipment:
                    session.delete(shipment)
                    session.commit()
                
                session.close()
                
                self.log_test("تنظيف البيانات", "تم حذف الشحنة التجريبية", "✅ نجح")
            
            return True
            
        except Exception as e:
            self.log_test("تنظيف البيانات", f"فشل في التنظيف: {str(e)}", "❌ فشل")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات العملية"""
        print("🧪 بدء الاختبار العملي لفصل التعبئة التلقائية عن وضع التعديل")
        print("=" * 70)
        
        # إعداد البيانات
        if not self.setup_test_data():
            print("❌ فشل في إعداد بيانات الاختبار")
            return False
        
        tests = [
            ("اختبار وضع الإنشاء الجديد", self.test_new_shipment_window_creation_mode),
            ("اختبار وضع التعديل", self.test_edit_shipment_window_edit_mode),
            ("اختبار التبديل بين الأوضاع", self.test_mode_switching_in_window)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            print(f"\n🔍 {test_name}:")
            print("-" * 50)
            
            try:
                if test_function():
                    passed_tests += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {str(e)}")
        
        # تنظيف البيانات
        self.cleanup_test_data()
        
        # النتائج النهائية
        print("\n" + "=" * 70)
        print("📊 نتائج الاختبار العملي:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("✅ جميع الاختبارات العملية نجحت! النظام يعمل بشكل صحيح")
        else:
            print("⚠️ بعض الاختبارات العملية فشلت. يرجى مراجعة النتائج أعلاه")
        
        return passed_tests, total_tests

def main():
    """الدالة الرئيسية"""
    try:
        tester = PracticalAutoFillSeparationTester()
        passed, total = tester.run_all_tests()
        
        print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات عملية نجحت")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات العملية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
