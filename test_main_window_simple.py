#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنافذة الرئيسية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_window():
    """اختبار النافذة الرئيسية"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        print("🔍 إنشاء النافذة الرئيسية...")
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار القوائم
        print("🔍 فحص القوائم...")
        menubar = main_window.menuBar()
        
        # البحث عن قائمة الأدوات
        tools_menu = None
        print("📋 القوائم المتاحة:")
        for action in menubar.actions():
            print(f"   - {action.text()}")
            if action.text() == "أدوات":  # اسم القائمة الصحيح
                tools_menu = action.menu()
                break
        
        if tools_menu:
            print("✅ تم العثور على قائمة أدوات")
            tools_actions = tools_menu.actions()
            print(f"📋 قائمة أدوات تحتوي على {len(tools_actions)} عنصر:")

            # عرض جميع عناصر قائمة الأدوات
            for i, action in enumerate(tools_actions, 1):
                action_text = action.text()
                if action_text:  # تجاهل الفواصل
                    print(f"   {i}. {action_text}")

            # التحقق من عدم وجود نظام تعبئة البيانات المفقودة
            data_filler_found = False
            for action in tools_actions:
                if "تعبئة البيانات المفقودة" in action.text():
                    data_filler_found = True
                    print(f"❌ تم العثور على: {action.text()}")
                    break

            if not data_filler_found:
                print("✅ تأكيد: لا يوجد نظام تعبئة البيانات المفقودة في قائمة أدوات")
            else:
                print("❌ خطأ: لا يزال نظام تعبئة البيانات المفقودة موجود")
                return False
        else:
            print("❌ لم يتم العثور على قائمة أدوات")
            return False
        
        print("🔍 إغلاق النافذة...")
        main_window.close()
        print("✅ تم إغلاق النافذة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 اختبار النافذة الرئيسية بعد حذف نظام تعبئة البيانات المفقودة")
    print("=" * 60)
    
    if test_main_window():
        print("\n🎉 الاختبار نجح! تم حذف النظام بنجاح.")
    else:
        print("\n❌ الاختبار فشل!")
