# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from .models import (Base, SystemSettings, Currency, FiscalYear, Company,
                     Shipment, ShipmentItem, Container, ShipmentDocument,
                     PurchaseOrder, PurchaseOrderItem)
from datetime import datetime, date

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = None):
        """تهيئة مدير قاعدة البيانات"""
        if db_path is None:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)
            db_path = data_dir / "proshipment.db"
        
        self.db_path = db_path
        self.engine = create_engine(f"sqlite:///{db_path}", echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            # إنشاء جميع الجداول
            Base.metadata.create_all(bind=self.engine)
            
            # إدراج البيانات الأساسية
            self._insert_default_data()
            
            print("تم تهيئة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات"""
        return self.SessionLocal()

    def close_all_sessions(self):
        """إغلاق جميع الجلسات النشطة"""
        try:
            # إغلاق جميع الاتصالات في pool
            self.engine.dispose()
        except Exception as e:
            print(f"خطأ في إغلاق الجلسات: {e}")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        session = self.get_session()
        try:
            # التحقق من وجود بيانات
            if session.query(SystemSettings).count() > 0:
                return
            
            # إعدادات النظام الافتراضية
            default_settings = [
                SystemSettings(
                    key="app_name",
                    value="نظام إدارة الشحنات",
                    description="اسم التطبيق",
                    category="general"
                ),
                SystemSettings(
                    key="app_version",
                    value="1.0.0",
                    description="إصدار التطبيق",
                    category="general"
                ),
                SystemSettings(
                    key="default_currency",
                    value="SAR",
                    description="العملة الافتراضية",
                    category="financial"
                ),
                SystemSettings(
                    key="decimal_places",
                    value="2",
                    description="عدد الخانات العشرية",
                    category="financial",
                    data_type="integer"
                ),
                SystemSettings(
                    key="date_format",
                    value="dd/MM/yyyy",
                    description="تنسيق التاريخ",
                    category="general"
                ),
                SystemSettings(
                    key="backup_enabled",
                    value="true",
                    description="تفعيل النسخ الاحتياطي",
                    category="system",
                    data_type="boolean"
                )
            ]
            
            # العملات الافتراضية
            default_currencies = [
                Currency(
                    code="SAR",
                    name="ريال سعودي",
                    name_en="Saudi Riyal",
                    symbol="ر.س",
                    exchange_rate=1.0,
                    is_base=True
                ),
                Currency(
                    code="USD",
                    name="دولار أمريكي",
                    name_en="US Dollar",
                    symbol="$",
                    exchange_rate=3.75
                ),
                Currency(
                    code="EUR",
                    name="يورو",
                    name_en="Euro",
                    symbol="€",
                    exchange_rate=4.10
                )
            ]
            
            # السنة المالية الحالية
            current_year = datetime.now().year
            fiscal_year = FiscalYear(
                year=current_year,
                start_date=datetime(current_year, 1, 1),
                end_date=datetime(current_year, 12, 31),
                is_current=True
            )
            
            # بيانات الشركة الافتراضية
            default_company = Company(
                name="شركة الشحنات المتقدمة",
                name_en="Advanced Shipping Company",
                address="الرياض، المملكة العربية السعودية",
                phone="+966-11-1234567",
                email="<EMAIL>"
            )
            
            # إضافة البيانات
            session.add_all(default_settings)
            session.add_all(default_currencies)
            session.add(fiscal_year)
            session.add(default_company)
            
            session.commit()
            print("تم إدراج البيانات الافتراضية بنجاح")
            
        except Exception as e:
            session.rollback()
            print(f"خطأ في إدراج البيانات الافتراضية: {e}")
        finally:
            session.close()
    
    def backup_database(self, backup_path: str = None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_path is None:
                backup_dir = Path("backups")
                backup_dir.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_dir / f"proshipment_backup_{timestamp}.db"
            
            # نسخ ملف قاعدة البيانات
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            print(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def get_setting(self, key: str, default_value: str = None):
        """الحصول على قيمة إعداد"""
        session = self.get_session()
        try:
            setting = session.query(SystemSettings).filter_by(key=key).first()
            if setting:
                return setting.value
            return default_value
        finally:
            session.close()
    
    def set_setting(self, key: str, value: str, description: str = None, category: str = "general"):
        """تعيين قيمة إعداد"""
        session = self.get_session()
        try:
            setting = session.query(SystemSettings).filter_by(key=key).first()
            if setting:
                setting.value = value
                setting.updated_at = datetime.now()
            else:
                setting = SystemSettings(
                    key=key,
                    value=value,
                    description=description,
                    category=category
                )
                session.add(setting)
            
            session.commit()
            return True
            
        except Exception as e:
            session.rollback()
            print(f"خطأ في تعيين الإعداد: {e}")
            return False
        finally:
            session.close()
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if hasattr(self, 'engine'):
            self.engine.dispose()
