#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنظام المرفقات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار الاستيرادات"""
    print("🔄 اختبار الاستيرادات...")
    
    try:
        from src.ui.shipments.attachments_manager_dialog import AttachmentsManagerDialog
        print("✅ تم استيراد AttachmentsManagerDialog بنجاح")
    except Exception as e:
        print(f"❌ فشل في استيراد AttachmentsManagerDialog: {e}")
        return False
    
    try:
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        print("✅ تم استيراد NewShipmentWindow بنجاح")
    except Exception as e:
        print(f"❌ فشل في استيراد NewShipmentWindow: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🔄 اختبار قاعدة البيانات...")
    
    try:
        import sqlite3
        db_path = "data/proshipment.db"
        
        if not os.path.exists(db_path):
            print(f"❌ ملف قاعدة البيانات غير موجود: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الحقول الجديدة
        cursor.execute("PRAGMA table_info(shipments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_fields = [
            "initial_documents_files",
            "dn_documents_files", 
            "customs_documents_files",
            "bill_of_lading_files",
            "items_images_files",
            "other_documents_files"
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in columns:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ الحقول التالية مفقودة: {missing_fields}")
            return False
        else:
            print("✅ جميع حقول المرفقات موجودة في قاعدة البيانات")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def create_test_files():
    """إنشاء ملفات تجريبية"""
    print("\n🔄 إنشاء ملفات تجريبية...")
    
    try:
        test_folder = Path("test_files")
        test_folder.mkdir(exist_ok=True)
        
        test_files = [
            "مستند_تجريبي.txt",
            "صورة_تجريبية.jpg",
            "جدول_بيانات.xlsx"
        ]
        
        for filename in test_files:
            file_path = test_folder / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"ملف تجريبي: {filename}\n")
                f.write("تم إنشاؤه لاختبار نظام المرفقات\n")
        
        print(f"✅ تم إنشاء {len(test_files)} ملف تجريبي في: {test_folder}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الملفات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار سريع لنظام المرفقات")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return
    
    # إنشاء ملفات تجريبية
    if not create_test_files():
        print("\n❌ فشل في إنشاء الملفات التجريبية")
        return
    
    print("\n" + "=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ نظام المرفقات جاهز للاستخدام")
    print("=" * 50)
    
    print("\n📋 التعليمات:")
    print("1. شغل التطبيق الرئيسي")
    print("2. افتح نافذة 'شحنة جديدة'")
    print("3. انتقل إلى تبويب 'المستندات'")
    print("4. جرب أزرار 'إضافة مرفق' الجديدة")
    print("5. استخدم الملفات التجريبية في مجلد test_files")

if __name__ == "__main__":
    main()
