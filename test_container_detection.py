#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحديد شركة الشحن من رقم الحاوية
"""

import sys
import os
import random
from datetime import datetime, <PERSON><PERSON><PERSON>

def detect_carrier_from_container(container_number):
    """تحديد شركة الشحن من رقم الحاوية"""
    if not container_number:
        return "غير محدد"

    container_upper = container_number.upper()

    # قاموس شركات الشحن وبادئات الحاويات - محدث من قاعدة بيانات PIER2PIER
    carrier_prefixes = {
        'COSCO': ['COSU', 'CXDU', 'OOCU', 'CSNU', 'VECU'],  # إضافة CSNU للحاوية المطلوبة
        'MAERSK': ['MSKU', 'TEMU', 'MRKU', 'APMU'],
        'MSC': ['MSCU', 'MEDU'],
        'CMA CGM': ['CMAU', 'CGMU', 'ANNU', 'AMCU', 'APHU', 'APLU', 'APRU', 'APZU'],
        'EVERGREEN': ['EGLV', 'EGHU', 'GESU', 'UGMU'],
        'HAPAG-LLOYD': ['HLBU', 'HLCU', 'HLXU', 'UACU', 'UAEU', 'UASU'],
        'ONE': ['ONEY', 'ONEU', 'NYKU', 'AKLU'],
        'YANG MING': ['YMLU', 'YAMU', 'SUDU', 'YMMU'],
        'HMM': ['HMMU', 'HDMU', 'HYMU'],
        'ZIM': ['ZIMU', 'ZCSU', 'ZILU', 'ZCLU', 'ZMOU'],
        'OOCL': ['OOCU'],  # Orient Overseas Container Line
        'PIL': ['PILU'],   # Pacific International Lines
        'WAN HAI': ['WHLU', 'WHSU'],
        'HYUNDAI': ['HDMU', 'HYMU'],
        'K LINE': ['KOLU'],
        'MOL': ['MOLU'],
        'NYK': ['NYKU'],
        'ARKAS': ['ARKU'],
        'BORCHARD': ['BORU'],
        'UNIFEEDER': ['UNFU', 'BLJU'],
        'SAMSKIP': ['VDMU'],
        'ATLANTIC CONTAINER LINE': ['ACLU'],
        'TEXTAINER': ['TXBU', 'TXGU', 'TXTU', 'AMFU', 'AMZU', 'AXIU', 'WCIU', 'XINU']
    }

    for carrier, prefixes in carrier_prefixes.items():
        for prefix in prefixes:
            if container_upper.startswith(prefix):
                return carrier

    return "غير محدد"

def create_demo_data(carrier, container_number):
    """إنشاء بيانات واقعية محسنة بناءً على شركة الشحن ورقم الحاوية"""

    # موانئ واقعية حسب شركة الشحن
    carrier_routes = {
        'COSCO': {
            'loading_ports': ['Shanghai', 'Ningbo', 'Qingdao', 'Tianjin', 'Dalian'],
            'discharge_ports': ['Jeddah', 'Dubai', 'Hamburg', 'Rotterdam', 'Los Angeles'],
            'vessels': ['COSCO SHIPPING UNIVERSE', 'COSCO SHIPPING GALAXY', 'COSCO SHIPPING PANAMA', 'COSCO SHIPPING ROSE']
        },
        'MAERSK': {
            'loading_ports': ['Shanghai', 'Singapore', 'Rotterdam', 'Hamburg', 'Antwerp'],
            'discharge_ports': ['Jeddah', 'Dubai', 'New York', 'Los Angeles', 'Felixstowe'],
            'vessels': ['MAERSK ESSEX', 'MAERSK EDINBURGH', 'MAERSK EMDEN', 'MAERSK ELBA']
        },
        'MSC': {
            'loading_ports': ['Shanghai', 'Singapore', 'Genoa', 'Valencia', 'Barcelona'],
            'discharge_ports': ['Jeddah', 'Dubai', 'Hamburg', 'Antwerp', 'Le Havre'],
            'vessels': ['MSC GULSUN', 'MSC MEGARA', 'MSC MAYA', 'MSC MINA']
        }
    }

    # استخدام بيانات الشركة أو بيانات افتراضية
    route_data = carrier_routes.get(carrier, {
        'loading_ports': ['Shanghai', 'Singapore', 'Hamburg'],
        'discharge_ports': ['Jeddah', 'Dubai', 'Rotterdam'],
        'vessels': [f'{carrier} VESSEL 1', f'{carrier} VESSEL 2']
    })

    # إنشاء تواريخ واقعية
    base_date = datetime.now() - timedelta(days=random.randint(5, 30))
    departure_date = base_date
    arrival_date = base_date + timedelta(days=random.randint(15, 45))

    # إنشاء أرقام تتبع واقعية
    tracking_number = f"{carrier[:3]}{container_number[-6:]}"

    demo_data = {
        'carrier': carrier,
        'container_number': container_number,
        'found_data': {
            'shipping_company': carrier,
            'vessel_name': random.choice(route_data['vessels']),
            'voyage_number': f"{random.randint(100, 999)}{'E' if random.choice([True, False]) else 'W'}",
            'port_of_loading': random.choice(route_data['loading_ports']),
            'port_of_discharge': random.choice(route_data['discharge_ports']),
            'tracking_number': tracking_number,
            'estimated_departure_date': departure_date.strftime('%Y-%m-%d'),
            'estimated_arrival_date': arrival_date.strftime('%Y-%m-%d'),
            'container_type': random.choice(['20GP', '40GP', '40HC', '45HC']),
        },
        'success': True
    }
    return demo_data

def test_container_detection():
    """اختبار تحديد شركة الشحن من أرقام الحاويات المختلفة"""

    # حاويات للاختبار
    test_containers = [
        "CSNU6166920",  # COSCO - الحاوية المطلوبة
        "OOCU7496892",  # COSCO
        "MSKU1234567",  # MAERSK
        "MSCU9876543",  # MSC
        "CMAU5555555",  # CMA CGM
        "EGLV1111111",  # EVERGREEN
        "HLBU2222222",  # HAPAG-LLOYD
        "YMLU3333333",  # YANG MING
        "ZIMU4444444",  # ZIM
        "UNKNOWN123",   # غير معروف
    ]

    print("🔍 اختبار تحديد شركة الشحن من رقم الحاوية")
    print("=" * 60)

    for container in test_containers:
        carrier = detect_carrier_from_container(container)
        print(f"📦 الحاوية: {container}")
        print(f"🚢 الشركة: {carrier}")

        # اختبار إنشاء البيانات التجريبية
        demo_data = create_demo_data(carrier, container)
        found_data = demo_data.get('found_data', {})

        print(f"🏭 شركة الشحن: {found_data.get('shipping_company', 'غير محدد')}")
        print(f"🚢 اسم السفينة: {found_data.get('vessel_name', 'غير محدد')}")
        print(f"🌊 رقم الرحلة: {found_data.get('voyage_number', 'غير محدد')}")
        print(f"🏗️ ميناء الشحن: {found_data.get('port_of_loading', 'غير محدد')}")
        print(f"🏭 ميناء الوصول: {found_data.get('port_of_discharge', 'غير محدد')}")
        print(f"📋 رقم التتبع: {found_data.get('tracking_number', 'غير محدد')}")
        print(f"📅 تاريخ المغادرة: {found_data.get('estimated_departure_date', 'غير محدد')}")
        print(f"📅 تاريخ الوصول: {found_data.get('estimated_arrival_date', 'غير محدد')}")
        print(f"📦 نوع الحاوية: {found_data.get('container_type', 'غير محدد')}")
        print("-" * 60)

if __name__ == "__main__":
    test_container_detection()
