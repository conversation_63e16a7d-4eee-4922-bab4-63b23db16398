#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق الرئيسي
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_imports():
    """اختبار استيرادات التطبيق الرئيسي"""
    print("🔍 اختبار استيرادات التطبيق الرئيسي...")
    
    try:
        print("1. اختبار استيراد PySide6...")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ تم استيراد PySide6 بنجاح")
        
        print("2. إنشاء QApplication...")
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication بنجاح")
        
        print("3. اختبار استيراد DatabaseManager...")
        from src.database.database_manager import DatabaseManager
        print("✅ تم استيراد DatabaseManager بنجاح")
        
        print("4. اختبار استيراد arabic_support...")
        from src.utils.arabic_support import setup_arabic_support
        print("✅ تم استيراد arabic_support بنجاح")
        
        print("5. اختبار استيراد MainWindow...")
        from src.ui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
        
        print("6. إنشاء MainWindow...")
        window = MainWindow()
        print("✅ تم إنشاء MainWindow بنجاح")
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_main_imports()
