#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة التطبيق الرئيسي
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_main_issue():
    """تشخيص مشكلة التطبيق الرئيسي"""
    print("🔍 تشخيص مشكلة التطبيق الرئيسي...")
    
    try:
        print("1. فحص الاستيرادات الأساسية...")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ الاستيرادات الأساسية تعمل")
        
        print("2. إنشاء QApplication...")
        app = QApplication(sys.argv)
        print("✅ QApplication تم إنشاؤه")
        
        print("3. فحص استيراد MainWindow...")
        from src.ui.main_window import MainWindow
        print("✅ MainWindow تم استيراده")
        
        print("4. فحص إنشاء MainWindow...")
        main_window = MainWindow()
        print("✅ MainWindow تم إنشاؤه")
        
        print("5. فحص عرض النافذة...")
        main_window.show()
        print("✅ النافذة تم عرضها")
        
        print("6. فحص وظيفة تعبئة البيانات...")
        if hasattr(main_window, 'open_data_filler'):
            print("✅ دالة open_data_filler موجودة")
            
            # محاولة استدعاء الدالة
            try:
                print("7. اختبار فتح نافذة تعبئة البيانات...")
                main_window.open_data_filler()
                print("✅ نافذة تعبئة البيانات تعمل")
            except Exception as e:
                print(f"❌ خطأ في فتح نافذة تعبئة البيانات: {str(e)}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ دالة open_data_filler غير موجودة")
        
        print("8. إغلاق الاختبار...")
        main_window.close()
        app.quit()
        print("✅ تم إغلاق الاختبار بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_main_issue()
