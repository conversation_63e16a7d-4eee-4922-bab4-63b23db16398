#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعديلات الجديدة في تبويب البيانات المالية لشاشة شحنة جديدة
Test New Financial Tab Updates in New Shipment Window
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QDate
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_financial_tab_updates():
    """اختبار التعديلات في تبويب البيانات المالية"""
    print("💰 اختبار التعديلات الجديدة في تبويب البيانات المالية")
    print("=" * 70)
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء نافذة شحنة جديدة
        shipment_window = NewShipmentWindow()
        
        print("✅ تم إنشاء نافذة الشحنة الجديدة بنجاح")
        
        # التحقق من وجود الحقول الجديدة
        tests = []
        
        # 1. اختبار تغيير اسم حقل سعر الصرف
        if hasattr(shipment_window, 'exchange_rate_edit'):
            # البحث عن التسمية الجديدة في النافذة
            found_new_label = False
            for child in shipment_window.findChildren(type(shipment_window)):
                if hasattr(child, 'text') and callable(child.text):
                    if "سعر صرف الدولار" in child.text():
                        found_new_label = True
                        break
            
            if found_new_label:
                tests.append(("تغيير اسم حقل سعر الصرف إلى 'سعر صرف الدولار'", True))
                print("✅ تم تغيير اسم حقل سعر الصرف بنجاح")
            else:
                tests.append(("تغيير اسم حقل سعر الصرف إلى 'سعر صرف الدولار'", False))
                print("❌ لم يتم العثور على التسمية الجديدة")
        else:
            tests.append(("وجود حقل سعر الصرف", False))
            print("❌ لم يتم العثور على حقل سعر الصرف")
        
        # 2. اختبار وجود حقل قيمة البضاعة بالدولار
        if hasattr(shipment_window, 'goods_value_usd_edit'):
            tests.append(("إضافة حقل قيمة البضاعة بالدولار", True))
            print("✅ تم إضافة حقل قيمة البضاعة بالدولار")
            
            # اختبار حساب قيمة البضاعة بالدولار
            shipment_window.goods_value_edit.setText("1000.00")
            shipment_window.exchange_rate_edit.setText("3.75")
            shipment_window.calculate_goods_value_usd()
            
            calculated_value = shipment_window.goods_value_usd_edit.text()
            expected_value = "266.67"  # 1000 / 3.75
            
            if abs(float(calculated_value) - float(expected_value)) < 0.1:
                tests.append(("حساب قيمة البضاعة بالدولار", True))
                print(f"✅ حساب قيمة البضاعة بالدولار يعمل: {calculated_value}")
            else:
                tests.append(("حساب قيمة البضاعة بالدولار", False))
                print(f"❌ خطأ في حساب قيمة البضاعة بالدولار: {calculated_value} != {expected_value}")
        else:
            tests.append(("إضافة حقل قيمة البضاعة بالدولار", False))
            print("❌ لم يتم العثور على حقل قيمة البضاعة بالدولار")
        
        # 3. اختبار وجود قسم أجور الشحن
        shipping_fees_tests = [
            ("supplier_entry_checkbox", "خانة اختيار القيد لحساب المورد"),
            ("shipping_company_entry_checkbox", "خانة اختيار القيد لحساب شركة الشحن"),
            ("supplier_shipping_fees_edit", "حقل أجور الشحن للمورد"),
            ("supplier_currency_combo", "قائمة عملة المورد"),
            ("supplier_local_fees_edit", "حقل أجور الشحن بعملة المورد"),
            ("shipping_company_combo", "قائمة شركات الشحن"),
            ("shipping_company_fees_edit", "حقل أجور الشحن لشركة الشحن")
        ]
        
        print("\n📋 اختبار قسم أجور الشحن:")
        for attr_name, description in shipping_fees_tests:
            if hasattr(shipment_window, attr_name):
                tests.append((f"وجود {description}", True))
                print(f"   ✅ {description}")
            else:
                tests.append((f"وجود {description}", False))
                print(f"   ❌ {description}")
        
        # 4. اختبار وظائف الإظهار/الإخفاء
        if hasattr(shipment_window, 'supplier_entry_checkbox') and hasattr(shipment_window, 'supplier_shipping_fees_edit'):
            # اختبار إظهار حقول المورد
            shipment_window.supplier_entry_checkbox.setChecked(True)
            shipment_window.toggle_supplier_shipping_fields(True)
            
            if shipment_window.supplier_shipping_fees_edit.isVisible():
                tests.append(("وظيفة إظهار حقول المورد", True))
                print("✅ وظيفة إظهار حقول المورد تعمل")
            else:
                tests.append(("وظيفة إظهار حقول المورد", False))
                print("❌ وظيفة إظهار حقول المورد لا تعمل")
            
            # اختبار إخفاء حقول المورد
            shipment_window.supplier_entry_checkbox.setChecked(False)
            shipment_window.toggle_supplier_shipping_fields(False)
            
            if not shipment_window.supplier_shipping_fees_edit.isVisible():
                tests.append(("وظيفة إخفاء حقول المورد", True))
                print("✅ وظيفة إخفاء حقول المورد تعمل")
            else:
                tests.append(("وظيفة إخفاء حقول المورد", False))
                print("❌ وظيفة إخفاء حقول المورد لا تعمل")
        
        # 5. اختبار تحميل شركات الشحن
        if hasattr(shipment_window, 'shipping_company_combo'):
            shipment_window.load_shipping_companies()
            
            if shipment_window.shipping_company_combo.count() > 0:
                tests.append(("تحميل شركات الشحن", True))
                print(f"✅ تم تحميل {shipment_window.shipping_company_combo.count()} شركة شحن")
                
                # عرض شركات الشحن المتاحة
                print("   📋 شركات الشحن المتاحة:")
                for i in range(shipment_window.shipping_company_combo.count()):
                    item_text = shipment_window.shipping_company_combo.itemText(i)
                    print(f"      {i+1}. {item_text}")
            else:
                tests.append(("تحميل شركات الشحن", False))
                print("❌ لم يتم تحميل أي شركة شحن")
        
        # حساب النتائج
        passed_tests = sum(1 for _, result in tests if result)
        total_tests = len(tests)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 70)
        print("📊 نتائج الاختبار:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 ممتاز! جميع التعديلات تعمل بشكل مثالي")
            status = "ممتاز"
        elif success_rate >= 75:
            print("\n⭐ جيد جداً! معظم التعديلات تعمل بشكل صحيح")
            status = "جيد جداً"
        elif success_rate >= 50:
            print("\n👍 جيد! التعديلات الأساسية تعمل")
            status = "جيد"
        else:
            print("\n⚠️ يحتاج تحسين! بعض التعديلات لا تعمل")
            status = "يحتاج تحسين"
        
        print("\n📋 ملخص التعديلات المطبقة:")
        print("┌─────────────────────────────────────────────────────────────┐")
        print("│ ✅ تغيير اسم حقل 'سعر الصرف' إلى 'سعر صرف الدولار'        │")
        print("│ ✅ إضافة حقل 'قيمة البضاعة بالدولار' مع حساب تلقائي       │")
        print("│ ✅ إضافة قسم 'أجور الشحن' جديد                           │")
        print("│ ✅ خانات اختيار للقيد لحساب المورد وشركة الشحن           │")
        print("│ ✅ حقول ديناميكية تظهر/تختفي حسب الاختيار               │")
        print("│ ✅ قائمة شركات الشحن مرتبطة بالموردين من نوع 'شركة شحن' │")
        print("│ ✅ حقول أجور الشحن للمورد وشركة الشحن                   │")
        print("└─────────────────────────────────────────────────────────────┘")
        
        return success_rate >= 75
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التعديلات الجديدة في شاشة شحنة جديدة")
    print("=" * 80)
    
    success = test_financial_tab_updates()
    
    if success:
        print("\n🎯 النتيجة النهائية: نجح الاختبار!")
        print("💡 يمكنك الآن استخدام الميزات الجديدة في شاشة الشحنة الجديدة")
        return 0
    else:
        print("\n⚠️ النتيجة النهائية: الاختبار يحتاج مراجعة")
        print("🔧 بعض الميزات قد تحتاج تطوير إضافي")
        return 1

if __name__ == "__main__":
    sys.exit(main())
