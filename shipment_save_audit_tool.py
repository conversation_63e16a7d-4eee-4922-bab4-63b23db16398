#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص ومراجعة إجراءات حفظ الشحنات
Shipment Save Audit Tool - Comprehensive review of shipment save procedures
"""

import sys
import os
from datetime import datetime, date
import traceback
import time

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment, ShipmentItem, Supplier, Item

class ShipmentSaveAuditTool:
    """أداة فحص إجراءات حفظ الشحنات"""
    
    def __init__(self):
        """تهيئة الأداة"""
        self.db_manager = DatabaseManager()
        self.audit_log = []
        self.issues_found = []
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الفحص"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status in ["ERROR", "WARNING"]:
            self.issues_found.append({
                'category': category,
                'message': message,
                'status': status,
                'timestamp': timestamp
            })
    
    def test_basic_save_operation(self):
        """اختبار عملية الحفظ الأساسية"""
        self.log_audit("عملية الحفظ الأساسية", "بدء اختبار الحفظ الأساسي")
        
        session = self.db_manager.get_session()
        try:
            # التحقق من وجود مورد
            supplier = session.query(Supplier).first()
            if not supplier:
                self.log_audit("عملية الحفظ الأساسية", "لا يوجد موردين للاختبار", "WARNING")
                return
            
            # إنشاء شحنة تجريبية
            test_shipment = Shipment(
                shipment_number="SAVE-TEST-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="شركة الحفظ التجريبية",
                container_number="SAVE123456",
                bill_of_lading="BL-SAVE-001",
                tracking_number="TRK-SAVE-001",
                vessel_name="سفينة الاختبار",
                port_of_loading="ميناء التحميل",
                port_of_discharge="ميناء التفريغ",
                port_of_arrival="ميناء الوصول"
            )
            
            # قياس وقت الحفظ
            start_time = time.time()
            session.add(test_shipment)
            session.commit()
            save_time = time.time() - start_time
            
            self.log_audit("عملية الحفظ الأساسية", f"تم حفظ الشحنة في {save_time:.3f} ثانية", "SUCCESS")
            
            # التحقق من الحفظ
            saved_shipment = session.query(Shipment).filter_by(shipment_number="SAVE-TEST-001").first()
            if saved_shipment:
                self.log_audit("عملية الحفظ الأساسية", "تم التحقق من حفظ الشحنة بنجاح", "SUCCESS")
                
                # التحقق من صحة البيانات المحفوظة
                if saved_shipment.shipping_company == "شركة الحفظ التجريبية":
                    self.log_audit("عملية الحفظ الأساسية", "البيانات محفوظة بشكل صحيح", "SUCCESS")
                else:
                    self.log_audit("عملية الحفظ الأساسية", "البيانات لم تحفظ بشكل صحيح", "ERROR")
                
                # حذف الشحنة التجريبية
                session.delete(saved_shipment)
                session.commit()
                self.log_audit("عملية الحفظ الأساسية", "تم حذف الشحنة التجريبية", "INFO")
            else:
                self.log_audit("عملية الحفظ الأساسية", "فشل في حفظ الشحنة", "ERROR")
            
        except Exception as e:
            session.rollback()
            self.log_audit("عملية الحفظ الأساسية", f"خطأ في اختبار الحفظ: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def test_transaction_rollback(self):
        """اختبار آلية التراجع في المعاملات"""
        self.log_audit("آلية التراجع", "بدء اختبار آلية التراجع")
        
        session = self.db_manager.get_session()
        try:
            supplier = session.query(Supplier).first()
            if not supplier:
                self.log_audit("آلية التراجع", "لا يوجد موردين للاختبار", "WARNING")
                return
            
            # بدء معاملة
            session.begin()
            
            # إنشاء شحنة صحيحة
            valid_shipment = Shipment(
                shipment_number="ROLLBACK-TEST-001",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب"
            )
            session.add(valid_shipment)
            session.flush()  # للحصول على ID
            
            # محاولة إنشاء شحنة بنفس الرقم (سيفشل)
            try:
                duplicate_shipment = Shipment(
                    shipment_number="ROLLBACK-TEST-001",
                    supplier_id=supplier.id,
                    shipment_status="تحت الطلب"
                )
                session.add(duplicate_shipment)
                session.commit()
                
                # إذا وصلنا هنا، فآلية التراجع لا تعمل
                self.log_audit("آلية التراجع", "آلية التراجع لا تعمل - تم حفظ شحنات مكررة", "ERROR")
                
            except Exception:
                # هذا متوقع - يجب أن تفشل المعاملة
                session.rollback()
                self.log_audit("آلية التراجع", "آلية التراجع تعمل بشكل صحيح", "SUCCESS")
                
                # التحقق من عدم حفظ أي شحنة
                check_shipment = session.query(Shipment).filter_by(shipment_number="ROLLBACK-TEST-001").first()
                if not check_shipment:
                    self.log_audit("آلية التراجع", "تم التراجع عن جميع العمليات بنجاح", "SUCCESS")
                else:
                    self.log_audit("آلية التراجع", "لم يتم التراجع بشكل كامل", "ERROR")
                    # حذف الشحنة المتبقية
                    session.delete(check_shipment)
                    session.commit()
            
        except Exception as e:
            session.rollback()
            self.log_audit("آلية التراجع", f"خطأ في اختبار التراجع: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def test_concurrent_save_operations(self):
        """اختبار العمليات المتزامنة"""
        self.log_audit("العمليات المتزامنة", "بدء اختبار العمليات المتزامنة")
        
        try:
            # إنشاء جلستين منفصلتين
            session1 = self.db_manager.get_session()
            session2 = self.db_manager.get_session()
            
            supplier = session1.query(Supplier).first()
            if not supplier:
                self.log_audit("العمليات المتزامنة", "لا يوجد موردين للاختبار", "WARNING")
                return
            
            # إنشاء شحنتين في جلستين مختلفتين
            shipment1 = Shipment(
                shipment_number="CONCURRENT-001",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب"
            )
            
            shipment2 = Shipment(
                shipment_number="CONCURRENT-002",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب"
            )
            
            # حفظ في الجلسة الأولى
            session1.add(shipment1)
            session1.commit()
            
            # حفظ في الجلسة الثانية
            session2.add(shipment2)
            session2.commit()
            
            # التحقق من حفظ كلا الشحنتين
            saved_count = session1.query(Shipment).filter(
                Shipment.shipment_number.in_(["CONCURRENT-001", "CONCURRENT-002"])
            ).count()
            
            if saved_count == 2:
                self.log_audit("العمليات المتزامنة", "العمليات المتزامنة تعمل بشكل صحيح", "SUCCESS")
            else:
                self.log_audit("العمليات المتزامنة", f"مشكلة في العمليات المتزامنة - حفظت {saved_count} من 2", "ERROR")
            
            # تنظيف البيانات التجريبية
            session1.query(Shipment).filter(
                Shipment.shipment_number.in_(["CONCURRENT-001", "CONCURRENT-002"])
            ).delete()
            session1.commit()
            
            session1.close()
            session2.close()
            
        except Exception as e:
            self.log_audit("العمليات المتزامنة", f"خطأ في اختبار العمليات المتزامنة: {str(e)}", "ERROR")
    
    def test_large_data_save(self):
        """اختبار حفظ البيانات الكبيرة"""
        self.log_audit("البيانات الكبيرة", "بدء اختبار حفظ البيانات الكبيرة")
        
        session = self.db_manager.get_session()
        try:
            supplier = session.query(Supplier).first()
            item = session.query(Item).first()
            
            if not supplier or not item:
                self.log_audit("البيانات الكبيرة", "لا توجد بيانات أساسية للاختبار", "WARNING")
                return
            
            # إنشاء شحنة مع بيانات كبيرة
            large_text = "A" * 1000  # نص كبير
            
            large_shipment = Shipment(
                shipment_number="LARGE-DATA-001",
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                notes=large_text,
                shipping_company="شركة البيانات الكبيرة " + large_text[:100]
            )
            
            start_time = time.time()
            session.add(large_shipment)
            session.flush()
            
            # إضافة عدد كبير من الأصناف
            for i in range(50):
                large_item = ShipmentItem(
                    shipment_id=large_shipment.id,
                    item_id=item.id,
                    quantity=float(i + 1),
                    unit_price=100.0 + i,
                    notes=f"صنف رقم {i+1} - " + large_text[:100]
                )
                session.add(large_item)
            
            session.commit()
            save_time = time.time() - start_time
            
            self.log_audit("البيانات الكبيرة", f"تم حفظ البيانات الكبيرة في {save_time:.3f} ثانية", "SUCCESS")
            
            # التحقق من الحفظ
            saved_shipment = session.query(Shipment).filter_by(shipment_number="LARGE-DATA-001").first()
            if saved_shipment:
                items_count = session.query(ShipmentItem).filter_by(shipment_id=saved_shipment.id).count()
                if items_count == 50:
                    self.log_audit("البيانات الكبيرة", f"تم حفظ جميع الأصناف ({items_count})", "SUCCESS")
                else:
                    self.log_audit("البيانات الكبيرة", f"مشكلة في حفظ الأصناف - حفظ {items_count} من 50", "ERROR")
                
                # حذف البيانات التجريبية
                session.query(ShipmentItem).filter_by(shipment_id=saved_shipment.id).delete()
                session.delete(saved_shipment)
                session.commit()
                self.log_audit("البيانات الكبيرة", "تم حذف البيانات التجريبية الكبيرة", "INFO")
            
        except Exception as e:
            session.rollback()
            self.log_audit("البيانات الكبيرة", f"خطأ في اختبار البيانات الكبيرة: {str(e)}", "ERROR")
        finally:
            session.close()
    
    def test_session_management(self):
        """اختبار إدارة الجلسات"""
        self.log_audit("إدارة الجلسات", "بدء اختبار إدارة الجلسات")
        
        try:
            # اختبار إنشاء وإغلاق عدة جلسات
            sessions = []
            for i in range(5):
                session = self.db_manager.get_session()
                sessions.append(session)
                
                # اختبار بسيط للجلسة
                count = session.query(Supplier).count()
                self.log_audit("إدارة الجلسات", f"الجلسة {i+1}: {count} مورد", "INFO")
            
            # إغلاق جميع الجلسات
            for i, session in enumerate(sessions):
                session.close()
                self.log_audit("إدارة الجلسات", f"تم إغلاق الجلسة {i+1}", "INFO")
            
            self.log_audit("إدارة الجلسات", "إدارة الجلسات تعمل بشكل صحيح", "SUCCESS")
            
        except Exception as e:
            self.log_audit("إدارة الجلسات", f"خطأ في إدارة الجلسات: {str(e)}", "ERROR")
    
    def run_complete_audit(self):
        """تشغيل الفحص الشامل"""
        self.log_audit("فحص شامل", "بدء الفحص الشامل لإجراءات حفظ الشحنات")
        
        # 1. اختبار عملية الحفظ الأساسية
        self.test_basic_save_operation()
        
        # 2. اختبار آلية التراجع
        self.test_transaction_rollback()
        
        # 3. اختبار العمليات المتزامنة
        self.test_concurrent_save_operations()
        
        # 4. اختبار البيانات الكبيرة
        self.test_large_data_save()
        
        # 5. اختبار إدارة الجلسات
        self.test_session_management()
        
        # تقرير نهائي
        self.log_audit("تقرير نهائي", "=" * 50)

        if not self.issues_found:
            self.log_audit("تقرير نهائي", "✅ لم يتم العثور على مشاكل في إجراءات حفظ الشحنات", "SUCCESS")
        else:
            self.log_audit("تقرير نهائي", f"⚠️ تم العثور على {len(self.issues_found)} مشكلة", "WARNING")
            for issue in self.issues_found:
                print(f"[مشكلة مكتشفة] {issue['category']}: {issue['message']} [{issue['status']}]")
        
        return {
            'success': len(self.issues_found) == 0,
            'issues_count': len(self.issues_found),
            'issues': self.issues_found,
            'audit_log': self.audit_log
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("💾 أداة فحص إجراءات حفظ الشحنات")
        print("=" * 50)

        audit_tool = ShipmentSaveAuditTool()
        result = audit_tool.run_complete_audit()

        print("\n" + "=" * 50)
        if result['success']:
            print("✅ تم اجتياز جميع فحوصات حفظ الشحنات!")
        else:
            print(f"⚠️ تم العثور على {result['issues_count']} مشكلة في إجراءات حفظ الشحنات")

        return result
    except Exception as e:
        print(f"❌ خطأ في تشغيل أداة الفحص: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
