#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحديثات الجديدة في تبويب المستندات
Test new updates in documents tab
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

def test_documents_tab_updates():
    """اختبار التحديثات الجديدة في تبويب المستندات"""
    
    try:
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # إنشاء نافذة طلبات الشراء في وضع الإدخال
        window = PurchaseOrdersWindow(maximize_on_start=False, mode="entry")
        
        print("🔍 فحص التحديثات الجديدة في تبويب المستندات...")
        
        # فحص حقول عرض الروابط (QLabel)
        print("\n📋 فحص حقول عرض الروابط...")
        
        url_displays = [
            ('contract_url_display', 'حقل عرض رابط العقد'),
            ('initial_designs_url_display', 'حقل عرض رابط التصاميم الأولية'),
            ('final_design_url_display', 'حقل عرض رابط التصميم النهائي'),
            ('other_attachments_url_display', 'حقل عرض رابط المرفقات الأخرى')
        ]
        
        for display_attr, display_name in url_displays:
            if hasattr(window, display_attr):
                display = getattr(window, display_attr)
                print(f"✅ {display_name}: موجود")
                print(f"   📝 النص الحالي: '{display.text()}'")
                print(f"   🔗 يدعم الروابط التشعبية: {display.openExternalLinks()}")
                
                # التحقق من النص الافتراضي
                if display.text() == "لا يوجد رابط":
                    print(f"   ✅ النص الافتراضي صحيح")
                else:
                    print(f"   ⚠️ النص الافتراضي مختلف: '{display.text()}'")
            else:
                print(f"❌ {display_name}: غير موجود")
        
        # فحص أزرار إضافة الروابط
        print("\n🔗 فحص أزرار إضافة الروابط...")
        
        link_buttons = [
            ('contract_add_link_btn', 'زر إضافة رابط العقد'),
            ('initial_designs_add_link_btn', 'زر إضافة رابط التصاميم الأولية'),
            ('final_design_add_link_btn', 'زر إضافة رابط التصميم النهائي'),
            ('other_attachments_add_link_btn', 'زر إضافة رابط المرفقات الأخرى')
        ]
        
        for btn_attr, btn_name in link_buttons:
            if hasattr(window, btn_attr):
                button = getattr(window, btn_attr)
                print(f"✅ {btn_name}: موجود")
                print(f"   📝 النص: '{button.text()}'")
                print(f"   🎨 اللون: أزرق (للروابط)")
                
                # التحقق من النص
                if button.text() == "إضافة رابط":
                    print(f"   ✅ النص صحيح")
                else:
                    print(f"   ❌ النص خطأ: متوقع 'إضافة رابط', موجود '{button.text()}'")
            else:
                print(f"❌ {btn_name}: غير موجود")
        
        # فحص أزرار إضافة المرفقات (يجب أن تبقى موجودة)
        print("\n📎 فحص أزرار إضافة المرفقات...")
        
        attachment_buttons = [
            ('contract_add_attachment_btn', 'زر إضافة مرفق العقد'),
            ('initial_designs_add_attachment_btn', 'زر إضافة مرفق التصاميم الأولية'),
            ('final_design_add_attachment_btn', 'زر إضافة مرفق التصميم النهائي'),
            ('other_attachments_add_attachment_btn', 'زر إضافة مرفق المرفقات الأخرى')
        ]
        
        for btn_attr, btn_name in attachment_buttons:
            if hasattr(window, btn_attr):
                button = getattr(window, btn_attr)
                print(f"✅ {btn_name}: موجود")
                print(f"   📝 النص: '{button.text()}'")
                print(f"   🎨 اللون: أحمر (للمرفقات)")
                
                # التحقق من النص الافتراضي
                if button.text() == "إضافة مرفق":
                    print(f"   ✅ النص الافتراضي صحيح")
                else:
                    print(f"   ⚠️ النص مختلف: '{button.text()}'")
            else:
                print(f"❌ {btn_name}: غير موجود")
        
        # التحقق من عدم وجود أزرار إدارة المرفقات (يجب حذفها)
        print("\n🗑️ فحص حذف أزرار إدارة المرفقات...")
        
        manage_buttons = [
            ('contract_manage_files_btn', 'زر إدارة مرفقات العقد'),
            ('initial_designs_manage_files_btn', 'زر إدارة مرفقات التصاميم الأولية'),
            ('final_design_manage_files_btn', 'زر إدارة مرفقات التصميم النهائي'),
            ('other_attachments_manage_files_btn', 'زر إدارة مرفقات المرفقات الأخرى')
        ]
        
        for btn_attr, btn_name in manage_buttons:
            if hasattr(window, btn_attr):
                print(f"❌ {btn_name}: لا يزال موجود (يجب حذفه)")
            else:
                print(f"✅ {btn_name}: محذوف بنجاح")
        
        # فحص التخطيط الجديد
        print("\n📐 فحص التخطيط الجديد...")
        print("   التخطيط المتوقع لكل صف:")
        print("   [التسمية] [عرض الرابط] [إضافة رابط] [إضافة مرفق]")
        
        # فحص الدوال الجديدة
        print("\n🔧 فحص الدوال الجديدة...")
        
        new_functions = [
            ('add_link_dialog', 'دالة حوار إضافة رابط'),
            ('get_document_url', 'دالة الحصول على رابط المستند'),
            ('update_document_display', 'دالة تحديث عرض المستند')
        ]
        
        for func_name, func_desc in new_functions:
            if hasattr(window, func_name):
                print(f"✅ {func_desc}: موجودة")
            else:
                print(f"❌ {func_desc}: غير موجودة")
        
        print("\n✅ انتهى فحص التحديثات!")
        print("📝 ملاحظات:")
        print("   • حقول الروابط أصبحت للعرض فقط (QLabel)")
        print("   • أزرار 'إضافة رابط' جديدة (لون أزرق)")
        print("   • أزرار 'إضافة مرفق' باقية (لون أحمر)")
        print("   • أزرار 'إدارة المرفقات' محذوفة")
        print("   • الروابط تظهر كروابط تشعبية قابلة للنقر")
        
        print("\n🧪 يمكنك الآن اختبار الوظائف يدوياً:")
        print("   1. اضغط على 'إضافة رابط' لإضافة رابط جديد")
        print("   2. اضغط على 'إضافة مرفق' لإضافة مرفق منفصل")
        print("   3. انقر على الرابط المعروض لفتحه")
        
        # عرض النافذة
        window.show()
        return window
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🧪 بدء اختبار التحديثات الجديدة في تبويب المستندات...")
    
    window = test_documents_tab_updates()
    
    if window:
        # تشغيل التطبيق
        sys.exit(app.exec())
    else:
        print("❌ فشل في إنشاء النافذة")
        sys.exit(1)

if __name__ == "__main__":
    main()
