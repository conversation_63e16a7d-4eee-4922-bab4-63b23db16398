#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام تعبئة البيانات المفقودة المدمج
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.database.database_manager import DatabaseManager
from src.ui.dialogs.shipment_data_filler_dialog import ShipmentDataFillerDialog

def test_data_filler_dialog():
    """اختبار نافذة تعبئة البيانات المفقودة"""
    print("🧪 بدء اختبار نظام تعبئة البيانات المفقودة المدمج...")

    try:
        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)

        # إنشاء نافذة تعبئة البيانات (ستنشئ db_manager داخلياً)
        dialog = ShipmentDataFillerDialog()
        
        print("✅ تم إنشاء نافذة تعبئة البيانات بنجاح")
        
        # التحقق من وجود التبويبات المطلوبة
        tab_widget = dialog.tab_widget
        tab_count = tab_widget.count()
        
        print(f"📊 عدد التبويبات: {tab_count}")
        
        for i in range(tab_count):
            tab_text = tab_widget.tabText(i)
            print(f"  - التبويب {i+1}: {tab_text}")
        
        # التحقق من إخفاء تبويب التحليل
        analysis_tab_found = False
        for i in range(tab_count):
            if "تحليل" in tab_widget.tabText(i):
                analysis_tab_found = True
                break
        
        if not analysis_tab_found:
            print("✅ تم إخفاء تبويب التحليل بنجاح")
        else:
            print("❌ تبويب التحليل لا يزال ظاهراً")
        
        # التحقق من وجود التبويب المدمج
        integrated_tab_found = False
        for i in range(tab_count):
            tab_text = tab_widget.tabText(i)
            if "بحث" in tab_text and "تعبئة" in tab_text:
                integrated_tab_found = True
                break
        
        if integrated_tab_found:
            print("✅ تم العثور على التبويب المدمج للبحث والتعبئة")
        else:
            print("❌ لم يتم العثور على التبويب المدمج")
        
        # عرض النافذة
        dialog.show()
        print("✅ تم عرض النافذة بنجاح")
        
        print("\n🎉 اختبار نظام تعبئة البيانات المفقودة مكتمل!")
        print("💡 يمكنك الآن اختبار الوظائف التالية:")
        print("   - البحث الذكي برقم الحاوية")
        print("   - عرض الشحنات الموجودة")
        print("   - البحث عبر الإنترنت")
        print("   - تطبيق البيانات من الإنترنت على الشحنات")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_filler_dialog()
