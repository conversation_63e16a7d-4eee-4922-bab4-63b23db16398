# ميزة استيراد البيانات من الإكسيل - شاشة الشحنة الجديدة

## 📋 نظرة عامة
تم إضافة ميزة جديدة لاستيراد البيانات من ملفات الإكسيل في شاشة الشحنة الجديدة، مما يسمح للمستخدمين بتعبئة الحقول تلقائياً من ملف إكسيل محضر مسبقاً.

## ✨ الميزات المضافة

### 🔘 زر الاستيراد
- تم إضافة زر "استيراد إكسيل" في شريط الأدوات
- أيقونة مميزة وتصميم متناسق مع باقي الأزرار
- موضع مناسب في شريط الأدوات

### 📊 الحقول المدعومة
تدعم الميزة استيراد الحقول التالية من ملف الإكسيل:

#### من تبويب البيانات الأساسية:
- **التاريخ**: تاريخ الشحنة (يتم توليد رقم الشحنة تلقائياً)
- **المورد**: اسم المورد مع البحث في قاعدة البيانات
- **بوليصة الشحن**: رقم بوليصة الشحن
- **ملاحظات**: ملاحظات الشحنة

#### من تبويب الشحن:
- **شركة الشحن**: اسم شركة الشحن
- **رقم DHL**: رقم تتبع DHL
- **ميناء الوصول**: ميناء الوصول
- **تاريخ الوصول المتوقع**: التاريخ المتوقع للوصول

#### من تبويب الحاويات:
- **رقم الحاوية**: يتم إضافة الحاوية تلقائياً مع بيانات افتراضية

## 🔧 التقنيات المستخدمة

### المكتبات المطلوبة:
- `pandas`: لقراءة ملفات الإكسيل
- `openpyxl`: لدعم تنسيق .xlsx
- `PySide6`: للواجهة الرسومية

### الدوال المضافة:
1. `import_from_excel()`: الدالة الرئيسية للاستيراد
2. `add_container_from_import()`: إضافة الحاويات من الاستيراد
3. `update_containers_count()`: تحديث عداد الحاويات

## 📄 تنسيق ملف الإكسيل

### العناوين المطلوبة (باللغة العربية):
```
التاريخ | المورد | بوليصة الشحن | ملاحظات | شركة الشحن | رقم DHL | ميناء الوصول | تاريخ الوصول المتوقع | رقم الحاوية
```

### مثال على البيانات:
```
2025-07-06 | شركة الإمارات للتجارة | BOL-2025-001 | شحنة مستوردة | شركة الخليج للشحن | DHL123456789 | ميناء الملك عبدالعزيز | 2025-08-05 | MSKU1234567
```

## 🎯 كيفية الاستخدام

### الخطوات:
1. افتح شاشة الشحنة الجديدة
2. انقر على زر "استيراد إكسيل" في شريط الأدوات
3. اختر ملف الإكسيل المطلوب (.xlsx أو .xls)
4. انتظر رسالة تأكيد نجاح الاستيراد
5. تحقق من تعبئة الحقول في التبويبات المختلفة

### ملاحظات مهمة:
- يتم قراءة البيانات من الصف الأول فقط
- رقم الشحنة يتم توليده تلقائياً (لا يُستورد)
- البحث عن المورد يتم في قاعدة البيانات
- الحاوية تُضاف بقيم افتراضية قابلة للتعديل

## 🛡️ معالجة الأخطاء

### الأخطاء المحتملة:
- **ملف غير موجود**: رسالة خطأ واضحة
- **تنسيق غير صحيح**: تحقق من تنسيق الملف
- **مكتبات مفقودة**: إرشادات التثبيت
- **بيانات فارغة**: تحذير من الملف الفارغ
- **أخطاء التحويل**: معالجة أخطاء التواريخ والبيانات

### رسائل التأكيد:
- رسالة نجاح مفصلة تظهر الحقول المستوردة
- قائمة بالبيانات التي تم استيرادها بنجاح
- إشارات بصرية (✓/✗) لكل حقل

## 📁 الملفات المضافة/المعدلة

### الملفات المعدلة:
- `src/ui/shipments/new_shipment_window.py`: إضافة الميزة الجديدة

### الملفات الجديدة:
- `sample_shipment_data.xlsx`: ملف إكسيل نموذجي للاختبار
- `create_shipment_sample_excel.py`: سكريبت إنشاء الملف النموذجي
- `test_excel_import.py`: اختبار شامل للميزة
- `test_excel_import_feature.py`: اختبار بسيط للتحقق من الميزة

## 🧪 الاختبارات

### اختبارات تم إجراؤها:
- ✅ إضافة الزر بنجاح
- ✅ ربط الزر بالدالة
- ✅ إنشاء ملف إكسيل نموذجي
- ✅ اختبار استيراد الدوال
- ✅ التحقق من وجود جميع الدوال المطلوبة

### اختبارات مطلوبة:
- اختبار الاستيراد الفعلي مع ملف إكسيل
- اختبار معالجة الأخطاء
- اختبار التواريخ المختلفة
- اختبار البحث عن الموردين

## 🚀 الاستخدام المستقبلي

### تحسينات محتملة:
- دعم استيراد عدة صفوف (شحنات متعددة)
- دعم استيراد الأصناف من ملف منفصل
- حفظ قوالب الاستيراد المخصصة
- دعم تنسيقات أخرى (CSV, JSON)
- معاينة البيانات قبل الاستيراد

### التكامل:
- يمكن تطبيق نفس المفهوم على شاشات أخرى
- إمكانية ربط الاستيراد بأنظمة خارجية
- دعم الاستيراد المجدول (Scheduled Import)

## 📞 الدعم الفني

في حالة وجود مشاكل:
1. تأكد من تثبيت المكتبات المطلوبة
2. تحقق من تنسيق ملف الإكسيل
3. راجع رسائل الخطأ في وحدة التحكم
4. استخدم الملف النموذجي للاختبار

---
**تاريخ الإنشاء**: 2025-07-06  
**الإصدار**: 1.0  
**الحالة**: مكتمل ✅
