# أدوات تشخيص مشكلة التعليق
# Freeze Issue Debugging Tools

## نظرة عامة
هذه مجموعة شاملة من أدوات التشخيص المتخصصة لحل مشكلة التعليق وعدم الاستجابة في شاشة شحنة جديدة عند الفتح للتعديل.

## الأدوات المتاحة

### 1. محلل الأداء الشامل (Performance Analyzer)
**الملف:** `performance_analyzer.py`

**الوظائف:**
- مراقبة استخدام المعالج والذاكرة في الوقت الفعلي
- تحليل الأداء باستخدام cProfile
- كشف التعليق المحتمل
- تحليل اتصالات قاعدة البيانات
- إنشاء تقارير مفصلة

**الاستخدام:**
```bash
python debug_tools/performance_analyzer.py
```

### 2. مشخص وضع التعديل (Edit Mode Debugger)
**الملف:** `edit_mode_debugger.py`

**الوظائف:**
- مراقبة متخصصة لعمليات وضع التعديل
- كشف التعليق التلقائي
- تتبع العمليات خطوة بخطوة
- تحليل الخيوط والأقفال
- سجل مفصل للأنشطة

**الاستخدام:**
```bash
python debug_tools/edit_mode_debugger.py
```

### 3. مراقب قاعدة البيانات (Database Monitor)
**الملف:** `database_monitor.py`

**الوظائف:**
- مراقبة اتصالات قاعدة البيانات
- كشف الأقفال والاستعلامات المعلقة
- تحليل أداء قاعدة البيانات
- اختبار العمليات الأساسية
- إحصائيات مفصلة

**الاستخدام:**
```bash
python debug_tools/database_monitor.py
```

### 4. المشخص الشامل (Comprehensive Debugger)
**الملف:** `comprehensive_debugger.py`

**الوظائف:**
- تشغيل جميع أدوات التشخيص معاً
- اختبار السيناريو الكامل
- تقرير شامل موحد
- توصيات للحل

**الاستخدام:**
```bash
# تشخيص شامل
python debug_tools/comprehensive_debugger.py --mode full

# تشخيص الأداء فقط
python debug_tools/comprehensive_debugger.py --mode performance

# تشخيص وضع التعديل فقط
python debug_tools/comprehensive_debugger.py --mode edit

# تشخيص قاعدة البيانات فقط
python debug_tools/comprehensive_debugger.py --mode database
```

## التثبيت

### 1. تثبيت المتطلبات
```bash
pip install -r debug_tools/requirements.txt
```

### 2. التحقق من التثبيت
```bash
python -c "import psutil, memory_profiler, objgraph; print('✅ جميع الأدوات مثبتة بنجاح')"
```

## سير العمل المقترح

### المرحلة 1: التشخيص الأولي
```bash
python debug_tools/comprehensive_debugger.py --mode full
```

### المرحلة 2: التحليل المتخصص
حسب نتائج المرحلة الأولى، قم بتشغيل الأدوات المتخصصة:

```bash
# إذا كانت المشكلة في الأداء
python debug_tools/performance_analyzer.py

# إذا كانت المشكلة في وضع التعديل
python debug_tools/edit_mode_debugger.py

# إذا كانت المشكلة في قاعدة البيانات
python debug_tools/database_monitor.py
```

### المرحلة 3: تحليل النتائج
1. راجع التقارير المُنشأة في مجلد `debug_tools/`
2. ابحث عن الأنماط في السجلات
3. حدد نقاط الاختناق
4. طبق الحلول المقترحة

## ملفات الإخراج

### التقارير
- `comprehensive_report_YYYYMMDD_HHMMSS.txt` - التقرير الشامل
- `profile_*_YYYYMMDD_HHMMSS.txt` - تقارير تحليل الأداء

### السجلات
- `edit_mode_debug_YYYYMMDD_HHMMSS.log` - سجل وضع التعديل
- `database_monitor_YYYYMMDD_HHMMSS.log` - سجل مراقبة قاعدة البيانات

## نصائح للاستخدام

### 1. التشغيل أثناء المشكلة
قم بتشغيل الأدوات أثناء حدوث مشكلة التعليق للحصول على أفضل النتائج.

### 2. مراقبة متعددة
يمكن تشغيل عدة أدوات في نفس الوقت لمراقبة شاملة.

### 3. حفظ السجلات
احتفظ بجميع السجلات والتقارير لمقارنة النتائج.

### 4. البيئة النظيفة
قم بإغلاق التطبيقات الأخرى أثناء التشخيص لتجنب التداخل.

## الحلول الشائعة

### 1. مشكلة أقفال قاعدة البيانات
```python
# تحسين إدارة الجلسات
session = db_manager.get_session()
try:
    # العمليات
    session.commit()
except:
    session.rollback()
finally:
    session.close()
```

### 2. مشكلة تسرب الذاكرة
```python
# تنظيف الموارد
import gc
gc.collect()
```

### 3. مشكلة الخيوط المعلقة
```python
# استخدام timeout
import threading
lock = threading.Lock()
if lock.acquire(timeout=5):
    try:
        # العمليات
        pass
    finally:
        lock.release()
```

## الدعم والمساعدة

إذا واجهت مشاكل في استخدام الأدوات:
1. تحقق من تثبيت جميع المتطلبات
2. راجع سجلات الأخطاء
3. تأكد من صحة مسارات الملفات
4. جرب تشغيل الأدوات بشكل منفصل

## معلومات إضافية

### متطلبات النظام
- Python 3.8+
- Windows/Linux/macOS
- 4GB RAM (مُوصى به)
- مساحة قرص كافية للسجلات

### الأمان
- الأدوات آمنة ولا تعدل البيانات
- تعمل في وضع القراءة فقط
- لا تؤثر على التطبيق الأساسي
