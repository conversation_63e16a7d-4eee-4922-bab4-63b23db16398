#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# إنشاء بيانات نموذجية للموردين
suppliers_data = {
    'اسم المورد': [
        'شركة الأهرام للتجارة',
        'مؤسسة النيل للاستيراد',
        'شركة الخليج للتوريدات',
        'مكتب الشرق الأوسط التجاري',
        'شركة الوادي للمواد الغذائية',
        'مؤسسة البحر الأحمر للتجارة',
        'شركة الصحراء للاستيراد والتصدير',
        'مكتب الفرات للتوريدات',
        'شركة الجزيرة للمواد الصناعية',
        'مؤسسة الأطلس للتجارة العامة'
    ],
    'الاسم الإنجليزي': [
        'Al-Ahram Trading Company',
        'Al-Nile Import Foundation',
        'Gulf Supplies Company',
        'Middle East Commercial Office',
        'Al-Wadi Food Materials Company',
        'Red Sea Trading Foundation',
        'Desert Import & Export Company',
        'Al-Furat Supplies Office',
        'Al-Jazeera Industrial Materials Company',
        'Atlas General Trading Foundation'
    ],
    'نوع المورد': [
        'شركة',
        'مؤسسة',
        'شركة',
        'مكتب',
        'شركة',
        'مؤسسة',
        'شركة',
        'مكتب',
        'شركة',
        'مؤسسة'
    ],
    'الرقم الضريبي': [
        '*********',
        '*********',
        '*********',
        '*********',
        '*********',
        '*********',
        '*********',
        '*********',
        '*********',
        '*********'
    ],
    'السجل التجاري': [
        'CR-2023-001',
        'CR-2023-002',
        'CR-2023-003',
        'CR-2023-004',
        'CR-2023-005',
        'CR-2023-006',
        'CR-2023-007',
        'CR-2023-008',
        'CR-2023-009',
        'CR-2023-010'
    ],
    'الهاتف': [
        '011-2345678',
        '012-3456789',
        '013-4567890',
        '014-5678901',
        '015-6789012',
        '016-7890123',
        '017-8901234',
        '018-9012345',
        '019-0123456',
        '020-1234567'
    ],
    'الجوال': [
        '0501234567',
        '0502345678',
        '0503456789',
        '0504567890',
        '0505678901',
        '0506789012',
        '0507890123',
        '0508901234',
        '0509012345',
        '0500123456'
    ],
    'البريد الإلكتروني': [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ],
    'الموقع الإلكتروني': [
        'www.ahram-trading.com',
        'www.nile-import.com',
        'www.gulf-supplies.com',
        'www.me-commercial.com',
        'www.wadi-food.com',
        'www.redsea-trading.com',
        'www.desert-ie.com',
        'www.furat-supplies.com',
        'www.jazeera-industrial.com',
        'www.atlas-trading.com'
    ],
    'الدولة': [
        'السعودية',
        'الإمارات',
        'الكويت',
        'قطر',
        'البحرين',
        'عمان',
        'الأردن',
        'لبنان',
        'مصر',
        'المغرب'
    ],
    'المدينة': [
        'الرياض',
        'دبي',
        'الكويت',
        'الدوحة',
        'المنامة',
        'مسقط',
        'عمان',
        'بيروت',
        'القاهرة',
        'الدار البيضاء'
    ],
    'العنوان': [
        'شارع الملك فهد، حي العليا',
        'شارع الشيخ زايد، منطقة التجارة',
        'شارع الخليج العربي، الصالحية',
        'كورنيش الدوحة، الخليج الغربي',
        'شارع الملك فيصل، المنامة',
        'شارع السلطان قابوس، روي',
        'شارع الملكة رانيا، عبدون',
        'شارع الحمرا، بيروت',
        'شارع التحرير، وسط البلد',
        'شارع محمد الخامس، المعاريف'
    ],
    'الرمز البريدي': [
        '11564',
        '12345',
        '13000',
        '23456',
        '34567',
        '45678',
        '56789',
        '67890',
        '78901',
        '89012'
    ],
    'حد الائتمان': [
        50000.00,
        75000.00,
        100000.00,
        25000.00,
        60000.00,
        80000.00,
        45000.00,
        90000.00,
        35000.00,
        120000.00
    ],
    'مدة السداد': [
        30,
        45,
        60,
        15,
        30,
        45,
        30,
        60,
        15,
        90
    ],
    'شخص الاتصال': [
        'أحمد محمد',
        'فاطمة علي',
        'محمد حسن',
        'عائشة أحمد',
        'علي محمود',
        'زينب حسين',
        'حسن عبدالله',
        'مريم سالم',
        'يوسف إبراهيم',
        'خديجة عمر'
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(suppliers_data)

# حفظ الملف
filename = 'نموذج_استيراد_الموردين.xlsx'
df.to_excel(filename, index=False, engine='openpyxl')

print(f"✅ تم إنشاء ملف الموردين النموذجي: {filename}")
print("\n📋 الأعمدة المتاحة:")
for i, col in enumerate(df.columns, 1):
    print(f"{i:2d}. {col}")

print(f"\n📊 عدد الموردين: {len(df)} مورد")
print("\n🔍 معاينة البيانات:")
print("=" * 60)
for i in range(min(3, len(df))):
    print(f"المورد {i+1}: {df.iloc[i]['اسم المورد']} - {df.iloc[i]['المدينة']}")

print("\n💡 ملاحظات:")
print("- العمود الوحيد المطلوب هو 'اسم المورد'")
print("- باقي الأعمدة اختيارية")
print("- سيتم توليد كود المورد تلقائياً")
print("- يمكن اختيار تحديث أو تجاهل الموردين الموجودين")
