#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات شاشة طلبات الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_purchase_orders_improvements():
    """اختبار التحسينات المطبقة على شاشة طلبات الشراء"""
    print("🔄 اختبار تحسينات شاشة طلبات الشراء...")
    
    try:
        from PySide6.QtWidgets import QApplication, QTableWidget
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود نافذة قائمة الطلبات (يجب أن تفتح تلقائياً)
        if hasattr(window, 'orders_list_window') and window.orders_list_window:
            print("✅ تم فتح قائمة طلبات الشراء تلقائياً عند الدخول")
            
            # التحقق من إعدادات الجدول
            table = window.orders_list_window.orders_table
            if table:
                # فحص إعدادات القراءة فقط
                edit_triggers = table.editTriggers()
                if edit_triggers == QTableWidget.NoEditTriggers:
                    print("✅ الجدول مضبوط للقراءة فقط (غير قابل للتعديل)")
                else:
                    print("❌ الجدول لا يزال قابلاً للتعديل")
                
                # فحص عناوين الأعمدة
                column_count = table.columnCount()
                print(f"✅ عدد الأعمدة: {column_count}")
                
                # فحص تسميات الأعمدة
                headers = []
                for i in range(column_count):
                    item = table.horizontalHeaderItem(i)
                    if item:
                        headers.append(item.text())
                
                if headers:
                    print(f"✅ تسميات الأعمدة: {', '.join(headers)}")
                else:
                    print("❌ لا توجد تسميات للأعمدة")
            else:
                print("❌ لم يتم العثور على الجدول")
        else:
            print("❌ لم يتم فتح قائمة طلبات الشراء تلقائياً")
        
        # فحص ترتيب الأزرار في شريط الأدوات
        toolbar = window.findChild(object, "الأدوات الرئيسية")
        if toolbar:
            actions = toolbar.actions()
            if len(actions) >= 2:
                first_action = actions[0].text()
                second_action = actions[1].text()
                
                if "قائمة الطلبات" in first_action and "طلب جديد" in second_action:
                    print("✅ تم إعادة ترتيب الأزرار: قائمة الطلبات قبل طلب جديد")
                else:
                    print(f"❌ ترتيب الأزرار غير صحيح: {first_action}, {second_action}")
            else:
                print("❌ لم يتم العثور على الأزرار في شريط الأدوات")
        else:
            print("❌ لم يتم العثور على شريط الأدوات")
        
        print("🎉 اكتمل اختبار التحسينات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_purchase_orders_improvements()
    sys.exit(0 if success else 1)
