#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PySide6.QtWidgets import QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTabWidget, QWidget, <PERSON><PERSON>abel
from PySide6.QtCore import Qt

class SimpleButtonsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("اختبار الأزرار البسيط")
        self.setModal(True)
        self.resize(800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان
        title_label = QLabel("اختبار أزرار التحكم")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        main_layout.addWidget(title_label)
        
        # التبويبات (محاكاة)
        tab_widget = QTabWidget()
        
        # تبويب تجريبي
        test_tab = QWidget()
        test_label = QLabel("هذا تبويب تجريبي")
        test_layout = QVBoxLayout(test_tab)
        test_layout.addWidget(test_label)
        tab_widget.addTab(test_tab, "تبويب تجريبي")
        
        main_layout.addWidget(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر إضافة
        new_button = QPushButton("🆕 إضافة")
        new_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # زر حفظ
        save_button = QPushButton("💾 حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # زر تعديل
        edit_button = QPushButton("✏️ تعديل")
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)

        # زر خروج
        exit_button = QPushButton("🚪 خروج")
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(new_button)
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(edit_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(exit_button)
        
        # إضافة الأزرار للتخطيط الرئيسي
        main_layout.addLayout(buttons_layout)
        
        # ربط الأزرار
        new_button.clicked.connect(lambda: print("تم الضغط على زر إضافة"))
        save_button.clicked.connect(lambda: print("تم الضغط على زر حفظ"))
        edit_button.clicked.connect(lambda: print("تم الضغط على زر تعديل"))
        exit_button.clicked.connect(self.accept)

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    dialog = SimpleButtonsDialog()
    dialog.show()
    
    print("النافذة البسيطة مفتوحة - تحقق من الأزرار")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
