#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PySide6.QtWidgets import QApplication
    from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
    
    print("🔄 اختبار الإصلاح الجديد لرأس الجدول...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = PurchaseOrdersWindow()
    window.show_orders_list()
    
    if hasattr(window, 'orders_list_window') and window.orders_list_window:
        table = window.orders_list_window.orders_table
        print(f"✅ الجدول موجود - أعمدة: {table.columnCount()}")
        
        # فحص التسميات
        print("📋 تسميات الأعمدة:")
        for i in range(table.columnCount()):
            item = table.horizontalHeaderItem(i)
            if item:
                print(f"  ✅ العمود {i+1}: '{item.text()}'")
            else:
                print(f"  ❌ العمود {i+1}: لا توجد تسمية")
        
        # فحص التنسيق
        style = table.styleSheet()
        if "QHeaderView::section" in style and "background-color: #ecf0f1" in style:
            print("✅ تم تطبيق التنسيق الجديد (خلفية فاتحة بدلاً من السوداء)!")
        else:
            print("❌ لم يتم تطبيق التنسيق")
        
        print("🎉 الإصلاح مكتمل - يجب أن تظهر عناوين الأعمدة بوضوح الآن!")
    else:
        print("❌ مشكلة في الجدول")
        
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
