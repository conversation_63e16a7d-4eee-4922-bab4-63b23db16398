#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة حقلي ميناء الوصول ورقم DHL لجدول الشحنات
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from sqlalchemy import text

def add_shipping_fields():
    """إضافة حقلي ميناء الوصول ورقم DHL"""
    print("🔧 إضافة حقلي ميناء الوصول ورقم DHL لجدول الشحنات...")
    
    db_manager = DatabaseManager()
    engine = db_manager.engine
    
    try:
        with engine.connect() as connection:
            # التحقق من الحقول الموجودة
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            columns = [row[1] for row in result.fetchall()]
            print(f"   📊 عدد الأعمدة الحالية: {len(columns)}")
            
            fields_to_add = []
            
            # التحقق من حقل ميناء الوصول
            if 'port_of_arrival' not in columns:
                fields_to_add.append(('port_of_arrival', 'VARCHAR(200)', 'ميناء الوصول'))
                print("   ➕ سيتم إضافة حقل ميناء الوصول")
            else:
                print("   ✅ حقل ميناء الوصول موجود مسبقاً")
            
            # التحقق من حقل رقم DHL
            if 'dhl_number' not in columns:
                fields_to_add.append(('dhl_number', 'VARCHAR(100)', 'رقم DHL'))
                print("   ➕ سيتم إضافة حقل رقم DHL")
            else:
                print("   ✅ حقل رقم DHL موجود مسبقاً")
            
            if not fields_to_add:
                print("   ✅ جميع الحقول موجودة مسبقاً")
                return True
            
            # إضافة الحقول الجديدة
            for field_name, field_type, description in fields_to_add:
                alter_sql = f"""
                ALTER TABLE shipments 
                ADD COLUMN {field_name} {field_type}
                """
                
                print(f"   🔧 إضافة حقل {description} ({field_name})...")
                connection.execute(text(alter_sql))
                connection.commit()
                print(f"   ✅ تم إضافة حقل {description} بنجاح")
            
            # التحقق من النتيجة النهائية
            result = connection.execute(text("PRAGMA table_info(shipments)"))
            new_columns = [row[1] for row in result.fetchall()]
            print(f"   📊 عدد الأعمدة بعد الإضافة: {len(new_columns)}")
            
            # اختبار الوصول للحقول الجديدة
            print("\n🧪 اختبار الوصول للحقول الجديدة...")
            test_sql = "SELECT port_of_arrival, dhl_number FROM shipments LIMIT 1"
            try:
                connection.execute(text(test_sql))
                print("   ✅ يمكن الوصول للحقول الجديدة بنجاح")
            except Exception as e:
                print(f"   ❌ خطأ في الوصول للحقول: {str(e)}")
                return False
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في إضافة الحقول: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 إضافة حقلي ميناء الوصول ورقم DHL")
    print("=" * 60)
    
    if add_shipping_fields():
        print("\n🎉 تم إضافة الحقول بنجاح!")
        print("✅ حقل ميناء الوصول (port_of_arrival)")
        print("✅ حقل رقم DHL (dhl_number)")
        print("✅ قاعدة البيانات جاهزة للاستخدام")
    else:
        print("\n❌ فشل في إضافة الحقول!")
        sys.exit(1)
