#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لرأس الجدول
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def test_simple_header():
    """اختبار بسيط لرأس الجدول"""
    print("🔄 بدء اختبار بسيط لرأس الجدول...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # فتح نافذة قائمة الطلبات بدون عرض
        window.show_orders_list()
        orders_window = window.orders_list_window
        
        if orders_window and hasattr(orders_window, 'orders_table'):
            table = orders_window.orders_table
            header = table.horizontalHeader()
            
            print(f"✅ عدد الأعمدة: {table.columnCount()}")
            print(f"✅ ارتفاع الرأس: {header.height()}")
            print(f"✅ الحد الأدنى لارتفاع الرأس: {header.minimumHeight()}")
            print(f"✅ الحد الأقصى لارتفاع الرأس: {header.maximumHeight()}")
            
            # محاولة إجبار ظهور الرأس
            header.setVisible(True)
            header.show()
            
            print(f"✅ رأس الجدول مرئي بعد الإجبار: {header.isVisible()}")
            
            # فحص تسميات الأعمدة
            for i in range(table.columnCount()):
                item = table.horizontalHeaderItem(i)
                if item:
                    print(f"✅ العمود {i}: {item.text()}")
                else:
                    print(f"❌ العمود {i}: لا يوجد تسمية")
            
            print("🎉 اكتمل الاختبار البسيط")
            return True
        else:
            print("❌ لم يتم العثور على الجدول")
            return False
            
    except Exception as e:
        print(f"❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_header()
    sys.exit(0 if success else 1)
