#!/usr/bin/env python3
"""
اختبار بسيط لإنشاء النافذة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_simple():
    """اختبار بسيط"""
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow
        
        print("✅ تم استيراد PurchaseOrdersWindow بنجاح")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # محاولة إنشاء النافذة
        window = PurchaseOrdersWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # التحقق من وجود العناصر الأساسية
        assert hasattr(window, 'orders_table'), "جدول الطلبات غير موجود"
        assert hasattr(window, 'search_edit'), "حقل البحث غير موجود"
        print("✅ العناصر الأساسية موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 اختبار بسيط...")
    
    if test_simple():
        print("\n🎉 النافذة تعمل بنجاح!")
    else:
        print("\n❌ لا تزال هناك مشاكل")
        sys.exit(1)
