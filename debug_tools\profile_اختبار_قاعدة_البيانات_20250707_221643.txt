تحليل الأداء: اختبار_قاعدة_البيانات
وقت التنفيذ: 0.411 ثانية
تغيير الذاكرة: +0.24 MB
==================================================
         10111 function calls (9874 primitive calls) in 0.408 seconds

   Ordered by: cumulative time
   List reduced from 493 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.404    0.404 E:\project\backup\‏‏ProShipment1\src\database\database_manager.py:20(__init__)
        1    0.000    0.000    0.397    0.397 <string>:1(create_engine)
      3/1    0.000    0.000    0.397    0.397 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py:249(warned)
        1    0.001    0.001    0.397    0.397 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\create.py:92(create_engine)
        1    0.000    0.000    0.317    0.317 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\url.py:761(_get_entrypoint)
        1    0.000    0.000    0.317    0.317 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\langhelpers.py:438(load)
        1    0.000    0.000    0.317    0.317 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\__init__.py:23(_auto_fn)
        1    0.000    0.000    0.317    0.317 {__feature_import__}
      8/1    0.001    0.000    0.314    0.314 <frozen importlib._bootstrap>:1349(_find_and_load)
      8/1    0.001    0.000    0.313    0.313 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
      8/1    0.001    0.000    0.308    0.308 <frozen importlib._bootstrap>:911(_load_unlocked)
      8/1    0.000    0.000    0.307    0.307 <frozen importlib._bootstrap_external>:1020(exec_module)
     18/2    0.000    0.000    0.304    0.152 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
     27/1    0.008    0.000    0.303    0.303 {built-in method builtins.exec}
        1    0.001    0.001    0.303    0.303 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\sqlite\__init__.py:1(<module>)
    34/10    0.001    0.000    0.255    0.025 <frozen importlib._bootstrap>:1390(_handle_fromlist)
        2    0.000    0.000    0.254    0.127 {built-in method builtins.__import__}
        1    0.002    0.002    0.196    0.196 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\sqlite\aiosqlite.py:1(<module>)
        1    0.001    0.001    0.146    0.146 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\sqlite\base.py:1(<module>)
       32    0.003    0.000    0.119    0.004 {built-in method builtins.__build_class__}


