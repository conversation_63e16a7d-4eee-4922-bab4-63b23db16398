#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حاوية محددة: OOCU7496892
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def test_container_oocu7496892():
    """اختبار الحاوية OOCU7496892"""
    container_number = "OOCU7496892"
    
    print(f"🔍 اختبار الحاوية: {container_number}")
    print("=" * 50)
    
    try:
        # استيراد النافذة
        from ui.dialogs.auto_fill_dialog import AutoFillWorker
        
        # إنشاء عامل البحث
        worker = AutoFillWorker(container_number, 1)
        
        # 1. اختبار تحديد شركة الشحن
        print("1️⃣ تحديد شركة الشحن...")
        carrier = worker.detect_carrier_from_container(container_number)
        print(f"   📋 شركة الشحن المحددة: {carrier}")
        
        # 2. اختبار البحث في المواقع
        print("\n2️⃣ البحث في مواقع شركات الشحن...")
        search_results = worker.search_shipping_websites(carrier, container_number)
        
        print(f"   🎯 نجح البحث: {search_results['success']}")
        print(f"   🏢 شركة الشحن: {search_results['carrier']}")
        print(f"   📦 رقم الحاوية: {search_results['container_number']}")
        
        if search_results['found_data']:
            print("\n   📊 البيانات المكتشفة:")
            for key, value in search_results['found_data'].items():
                if value:
                    print(f"      • {key}: {value}")
        
        # 3. اختبار معالجة النتائج
        print("\n3️⃣ معالجة النتائج...")
        processed = worker.process_search_results(search_results)
        
        print(f"   ✅ معالجة ناجحة: {processed['success']}")
        
        if processed['shipping_data']:
            print("\n   🚢 بيانات الشحن:")
            for key, value in processed['shipping_data'].items():
                if value:
                    print(f"      • {key}: {value}")
        
        if processed['container_data']:
            print("\n   📋 بيانات الحاوية:")
            for key, value in processed['container_data'].items():
                if value:
                    print(f"      • {key}: {value}")
        
        print("\n" + "=" * 50)
        
        if processed['success']:
            print("🎉 تم العثور على البيانات بنجاح!")
            print("\n💡 ملاحظة: هذه بيانات تجريبية لأغراض الاختبار")
            print("   في البيئة الحقيقية، سيتم البحث في مواقع شركات الشحن")
        else:
            print("❌ لم يتم العثور على بيانات")
        
        return True
        
    except Exception as e:
        print(f"\n💥 خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_containers():
    """اختبار عدة حاويات"""
    containers = [
        "OOCU7496892",  # COSCO
        "MSCU1234567",  # MSC
        "MSKU9876543",  # MAERSK
        "CMAU5555555",  # CMA CGM
        "EGLV3333333"   # EVERGREEN
    ]
    
    print("\n🔄 اختبار عدة حاويات")
    print("=" * 50)
    
    try:
        from ui.dialogs.auto_fill_dialog import AutoFillWorker
        
        for container in containers:
            print(f"\n📦 اختبار: {container}")
            worker = AutoFillWorker(container, 1)
            
            # تحديد شركة الشحن
            carrier = worker.detect_carrier_from_container(container)
            print(f"   🏢 الشركة: {carrier}")
            
            # البحث
            results = worker.search_shipping_websites(carrier, container)
            status = "✅" if results['success'] else "❌"
            print(f"   {status} البحث: {'نجح' if results['success'] else 'فشل'}")
            
            if results['success'] and results['found_data']:
                vessel = results['found_data'].get('vessel_name', 'غير محدد')
                route = f"{results['found_data'].get('port_of_loading', '')} → {results['found_data'].get('port_of_discharge', '')}"
                print(f"   🚢 السفينة: {vessel}")
                print(f"   🗺️ المسار: {route}")
        
        print("\n" + "=" * 50)
        print("🎯 اختبار الحاويات المتعددة مكتمل!")
        
    except Exception as e:
        print(f"💥 خطأ: {str(e)}")

if __name__ == "__main__":
    print("🤖 اختبار نظام التعبئة التلقائية")
    print("🎯 التركيز على الحاوية: OOCU7496892")
    print()
    
    # اختبار الحاوية المحددة
    success = test_container_oocu7496892()
    
    if success:
        # اختبار حاويات إضافية
        test_multiple_containers()
        
        print("\n✨ الخلاصة:")
        print("   • النظام يعمل بشكل صحيح")
        print("   • يتم تحديد شركة الشحن تلقائياً")
        print("   • يتم إنشاء بيانات تجريبية عند عدم توفر البيانات الحقيقية")
        print("   • جميع الحقول المطلوبة متوفرة")
        
        print("\n🚀 للاستخدام في التطبيق:")
        print("   1. افتح نافذة إدارة الشحنات")
        print("   2. انقر بالزر الأيمن على شحنة تحتوي على رقم حاوية")
        print("   3. اختر 'تعبئة تلقائية'")
        print("   4. ستحصل على بيانات مفيدة حتى لو لم تكن متوفرة عبر الإنترنت")
    else:
        print("\n💥 هناك مشاكل تحتاج إلى إصلاح")
