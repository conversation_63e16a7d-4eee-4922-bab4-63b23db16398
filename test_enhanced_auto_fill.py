#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعبئة التلقائية المحسنة
Enhanced Auto-Fill Test
"""

import sys
import os
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.database_manager import DatabaseManager
    from src.database.models import Shipment, Supplier, Container
    from src.services.web_scraping_service import WebScrapingService, ShipmentData
    from src.ui.dialogs.auto_fill_dialog import AutoFillDialog
    from PySide6.QtWidgets import QApplication
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد المكونات المطلوبة: {e}")

class EnhancedAutoFillTester:
    """مختبر التعبئة التلقائية المحسنة"""
    
    def __init__(self):
        """تهيئة المختبر"""
        self.db_manager = DatabaseManager()
        self.test_results = []
        self.test_shipment_id = None
        
    def log_test(self, test_name: str, result: str, status: str):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {test_name}: {result} - {status}"
        self.test_results.append(log_entry)
        print(log_entry)
    
    def setup_test_data(self):
        """إعداد بيانات الاختبار"""
        try:
            session = self.db_manager.get_session()
            
            # البحث عن مورد موجود أو إنشاء واحد جديد
            supplier = session.query(Supplier).first()
            if not supplier:
                supplier = Supplier(
                    name="مورد اختبار التعبئة المحسنة",
                    contact_person="شخص الاتصال",
                    phone="*********"
                )
                session.add(supplier)
                session.commit()
            
            # إنشاء شحنة تجريبية مع بيانات موجودة
            test_shipment = Shipment(
                shipment_number="ENHANCED-AUTO-FILL-TEST-001",
                shipment_date=date.today(),
                supplier_id=supplier.id,
                shipment_status="تحت الطلب",
                shipping_company="OLD COMPANY",  # بيانات موجودة للاستبدال
                port_of_loading="OLD PORT",      # بيانات موجودة للاستبدال
                notes="شحنة اختبار التعبئة التلقائية المحسنة"
            )
            session.add(test_shipment)
            session.commit()
            
            # إضافة حاوية للشحنة
            test_container = Container(
                shipment_id=test_shipment.id,
                container_number="COSU1234567",
                container_type="20GP",
                status="فارغة"
            )
            session.add(test_container)
            session.commit()
            
            self.test_shipment_id = test_shipment.id
            session.close()
            
            self.log_test("إعداد البيانات", f"تم إنشاء شحنة تجريبية رقم {self.test_shipment_id}", "✅ نجح")
            return True
            
        except Exception as e:
            self.log_test("إعداد البيانات", f"فشل في إعداد البيانات: {str(e)}", "❌ فشل")
            return False
    
    def test_web_scraping_service_enhancements(self):
        """اختبار تحسينات خدمة البحث عبر الإنترنت"""
        try:
            web_service = WebScrapingService()
            
            # اختبار إنشاء بيانات تجريبية محسنة
            test_data = web_service._create_fallback_data_searates("COSU1234567", "TEST-BL-001")
            
            # التحقق من وجود الحقول الجديدة
            required_fields = [
                'shipping_method', 'shipping_type', 'final_destination',
                'port_of_loading', 'port_of_discharge'
            ]
            
            missing_fields = []
            for field in required_fields:
                if not hasattr(test_data, field) or getattr(test_data, field) is None:
                    missing_fields.append(field)
            
            if not missing_fields:
                self.log_test("حقول البيانات الجديدة", "جميع الحقول المطلوبة موجودة", "✅ نجح")
            else:
                self.log_test("حقول البيانات الجديدة", f"حقول مفقودة: {missing_fields}", "❌ فشل")
                return False
            
            # التحقق من قيم الحقول
            if test_data.shipping_method and test_data.shipping_type and test_data.final_destination:
                self.log_test("قيم الحقول الجديدة", "جميع الحقول تحتوي على قيم", "✅ نجح")
            else:
                self.log_test("قيم الحقول الجديدة", "بعض الحقول فارغة", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("خدمة البحث المحسنة", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_auto_fill_dialog_enhancements(self):
        """اختبار تحسينات نافذة التعبئة التلقائية"""
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            if not self.test_shipment_id:
                self.log_test("نافذة التعبئة المحسنة", "لا يوجد معرف شحنة للاختبار", "❌ فشل")
                return False
            
            # إنشاء نافذة التعبئة التلقائية
            dialog = AutoFillDialog(parent=None, shipment_id=self.test_shipment_id, container_number="COSU1234567")
            
            # التحقق من وجود خانة الاختيار الجديدة
            if hasattr(dialog, 'replace_existing_checkbox'):
                self.log_test("خانة استبدال البيانات", "خانة الاختيار موجودة", "✅ نجح")
            else:
                self.log_test("خانة استبدال البيانات", "خانة الاختيار غير موجودة", "❌ فشل")
                return False
            
            # التحقق من الحالة الافتراضية
            if dialog.replace_existing_checkbox.isChecked():
                self.log_test("حالة خانة الاستبدال", "مفعلة افتراضياً", "✅ نجح")
            else:
                self.log_test("حالة خانة الاستبدال", "غير مفعلة افتراضياً", "❌ فشل")
                return False
            
            dialog.close()
            return True
            
        except Exception as e:
            self.log_test("نافذة التعبئة المحسنة", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_field_mapping_completeness(self):
        """اختبار اكتمال تعيين الحقول"""
        try:
            # قائمة الحقول المطلوبة
            required_fields = [
                'shipping_company', 'shipping_method', 'shipping_type',
                'port_of_loading', 'port_of_discharge', 'final_destination',
                'vessel_name', 'voyage_number', 'tracking_number'
            ]
            
            # محاكاة field_mapping من AutoFillDialog
            field_mapping = {
                # بيانات الشحن الأساسية
                'shipping_company': 'shipping_company',
                'shipping_method': 'shipping_method',
                'shipping_type': 'shipping_type',
                
                # الموانئ والوجهات
                'port_of_loading': 'port_of_loading',
                'port_of_discharge': 'port_of_discharge',
                'final_destination': 'final_destination',
                
                # بيانات السفينة والرحلة
                'vessel_name': 'vessel_name',
                'voyage_number': 'voyage_number',
                'tracking_number': 'tracking_number'
            }
            
            # التحقق من وجود جميع الحقول المطلوبة
            missing_mappings = []
            for field in required_fields:
                if field not in field_mapping:
                    missing_mappings.append(field)
            
            if not missing_mappings:
                self.log_test("تعيين الحقول", "جميع الحقول المطلوبة معينة", "✅ نجح")
            else:
                self.log_test("تعيين الحقول", f"حقول غير معينة: {missing_mappings}", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("تعيين الحقول", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def test_replacement_logic(self):
        """اختبار منطق الاستبدال"""
        try:
            # محاكاة البيانات الموجودة والجديدة
            existing_data = {
                'shipping_company': 'OLD COMPANY',
                'port_of_loading': 'OLD PORT',
                'final_destination': None  # حقل فارغ
            }
            
            new_data = {
                'shipping_company': 'NEW COMPANY',
                'port_of_loading': 'NEW PORT',
                'final_destination': 'NEW DESTINATION'
            }
            
            # اختبار منطق الاستبدال (replace_existing = True)
            replace_existing = True
            updated_fields_replace = []
            
            for field, new_value in new_data.items():
                if new_value:
                    current_value = existing_data.get(field)
                    should_update = False
                    
                    if replace_existing:
                        should_update = True
                    else:
                        should_update = not current_value or (isinstance(current_value, str) and not current_value.strip())
                    
                    if should_update:
                        updated_fields_replace.append(field)
            
            # يجب أن يتم تحديث جميع الحقول
            if len(updated_fields_replace) == 3:
                self.log_test("منطق الاستبدال", "يتم استبدال جميع الحقول", "✅ نجح")
            else:
                self.log_test("منطق الاستبدال", f"تم تحديث {len(updated_fields_replace)} من 3 حقول", "❌ فشل")
                return False
            
            # اختبار منطق عدم الاستبدال (replace_existing = False)
            replace_existing = False
            updated_fields_no_replace = []
            
            for field, new_value in new_data.items():
                if new_value:
                    current_value = existing_data.get(field)
                    should_update = False
                    
                    if replace_existing:
                        should_update = True
                    else:
                        should_update = not current_value or (isinstance(current_value, str) and not current_value.strip())
                    
                    if should_update:
                        updated_fields_no_replace.append(field)
            
            # يجب أن يتم تحديث الحقل الفارغ فقط
            if len(updated_fields_no_replace) == 1 and 'final_destination' in updated_fields_no_replace:
                self.log_test("منطق عدم الاستبدال", "يتم تحديث الحقول الفارغة فقط", "✅ نجح")
            else:
                self.log_test("منطق عدم الاستبدال", f"تم تحديث {updated_fields_no_replace} بدلاً من ['final_destination']", "❌ فشل")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("منطق الاستبدال", f"خطأ في الاختبار: {str(e)}", "❌ فشل")
            return False
    
    def cleanup_test_data(self):
        """تنظيف بيانات الاختبار"""
        try:
            if self.test_shipment_id:
                session = self.db_manager.get_session()
                
                # حذف الحاويات المرتبطة
                containers = session.query(Container).filter(Container.shipment_id == self.test_shipment_id).all()
                for container in containers:
                    session.delete(container)
                
                # حذف الشحنة التجريبية
                shipment = session.query(Shipment).filter(Shipment.id == self.test_shipment_id).first()
                if shipment:
                    session.delete(shipment)
                
                session.commit()
                session.close()
                
                self.log_test("تنظيف البيانات", "تم حذف البيانات التجريبية", "✅ نجح")
            
            return True
            
        except Exception as e:
            self.log_test("تنظيف البيانات", f"فشل في التنظيف: {str(e)}", "❌ فشل")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار التعبئة التلقائية المحسنة")
        print("=" * 60)
        
        # إعداد البيانات
        if not self.setup_test_data():
            print("❌ فشل في إعداد بيانات الاختبار")
            return False
        
        tests = [
            ("اختبار تحسينات خدمة البحث", self.test_web_scraping_service_enhancements),
            ("اختبار تحسينات نافذة التعبئة", self.test_auto_fill_dialog_enhancements),
            ("اختبار اكتمال تعيين الحقول", self.test_field_mapping_completeness),
            ("اختبار منطق الاستبدال", self.test_replacement_logic)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            print(f"\n🔍 {test_name}:")
            print("-" * 50)
            
            try:
                if test_function():
                    passed_tests += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {str(e)}")
        
        # تنظيف البيانات
        self.cleanup_test_data()
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج اختبار التعبئة التلقائية المحسنة:")
        print(f"• الاختبارات الناجحة: {passed_tests}/{total_tests}")
        print(f"• معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("✅ جميع التحسينات تعمل بشكل صحيح!")
            print("🎯 التعبئة التلقائية المحسنة جاهزة للاستخدام")
        else:
            print("⚠️ بعض التحسينات تحتاج مراجعة")
        
        return passed_tests, total_tests

def main():
    """الدالة الرئيسية"""
    try:
        tester = EnhancedAutoFillTester()
        passed, total = tester.run_all_tests()
        
        print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
        
        if passed == total:
            print("\n🎉 تم تطبيق التحسينات بنجاح!")
            print("📋 الميزات الجديدة:")
            print("• تعبئة جميع الحقول المطلوبة (شركة الشحن، طريقة الشحن، الموانئ، الوجهة النهائية)")
            print("• خيار استبدال البيانات الموجودة")
            print("• تحسين منطق التطبيق")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
