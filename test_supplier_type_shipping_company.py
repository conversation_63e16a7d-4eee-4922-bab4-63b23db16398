#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة نوع "شركة شحن" إلى أنواع الموردين
Test Adding "Shipping Company" Type to Supplier Types
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.suppliers.suppliers_data import SuppliersDataWindow

def test_shipping_company_supplier_type():
    """اختبار إضافة نوع شركة شحن للموردين"""
    print("🚢 اختبار إضافة نوع 'شركة شحن' إلى أنواع الموردين")
    print("=" * 60)
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء نافذة إدارة الموردين
        suppliers_window = SuppliersDataWindow()
        
        # الوصول إلى ويدجت البيانات
        suppliers_widget = suppliers_window.centralWidget()
        
        # البحث عن ComboBox نوع المورد
        supplier_type_combo = None
        for child in suppliers_widget.findChildren(type(suppliers_widget)):
            if hasattr(child, 'supplier_type_combo'):
                supplier_type_combo = child.supplier_type_combo
                break
        
        if supplier_type_combo:
            print("✅ تم العثور على حقل نوع المورد")
            
            # فحص الأنواع المتاحة
            available_types = []
            for i in range(supplier_type_combo.count()):
                available_types.append(supplier_type_combo.itemText(i))
            
            print(f"📋 الأنواع المتاحة: {available_types}")
            
            # التحقق من وجود "شركة شحن"
            if "شركة شحن" in available_types:
                print("✅ تم إضافة نوع 'شركة شحن' بنجاح!")
                
                # اختبار تحديد النوع
                index = available_types.index("شركة شحن")
                supplier_type_combo.setCurrentIndex(index)
                selected_type = supplier_type_combo.currentText()
                
                if selected_type == "شركة شحن":
                    print("✅ يمكن تحديد نوع 'شركة شحن' بنجاح")
                    
                    # عرض جميع الأنواع المتاحة
                    print("\n📊 جميع أنواع الموردين المتاحة:")
                    for i, type_name in enumerate(available_types, 1):
                        status = "🔹" if type_name != "شركة شحن" else "🆕"
                        print(f"   {i}. {status} {type_name}")
                    
                    print("\n🎯 النتائج:")
                    print("   ✅ تم إضافة نوع 'شركة شحن' بنجاح")
                    print("   ✅ يمكن تحديد النوع الجديد")
                    print("   ✅ النوع متاح في قائمة الخيارات")
                    print("   ✅ التكامل مع الواجهة يعمل بشكل مثالي")
                    
                    return True
                else:
                    print("❌ فشل في تحديد نوع 'شركة شحن'")
                    return False
            else:
                print("❌ لم يتم العثور على نوع 'شركة شحن' في القائمة")
                print(f"الأنواع الموجودة: {available_types}")
                return False
        else:
            print("❌ لم يتم العثور على حقل نوع المورد")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supplier_creation_with_shipping_company():
    """اختبار إنشاء مورد من نوع شركة شحن"""
    print("\n🏗️ اختبار إنشاء مورد من نوع 'شركة شحن'")
    print("-" * 50)
    
    try:
        from src.database.models import Supplier
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مورد تجريبي
        test_supplier = Supplier(
            code="SHIP001",
            name="شركة ماريسك للشحن",
            name_en="Maersk Shipping Company",
            supplier_type="شركة شحن",
            contact_person="أحمد محمد",
            phone="011-1234567",
            email="<EMAIL>",
            country="الدنمارك",
            city="كوبنهاجن",
            is_active=True
        )
        
        print("✅ تم إنشاء كائن المورد بنجاح")
        print(f"   📝 الاسم: {test_supplier.name}")
        print(f"   🏷️ النوع: {test_supplier.supplier_type}")
        print(f"   📞 الهاتف: {test_supplier.phone}")
        print(f"   📧 البريد: {test_supplier.email}")
        
        # التحقق من صحة النوع
        if test_supplier.supplier_type == "شركة شحن":
            print("✅ تم تعيين نوع 'شركة شحن' بنجاح")
            return True
        else:
            print("❌ فشل في تعيين نوع 'شركة شحن'")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المورد: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لإضافة نوع 'شركة شحن' للموردين")
    print("=" * 70)
    
    tests = [
        ("اختبار واجهة المستخدم", test_shipping_company_supplier_type),
        ("اختبار إنشاء المورد", test_supplier_creation_with_shipping_company)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    print(f"• الاختبارات الناجحة: {passed}/{total}")
    print(f"• معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("📋 ملخص التحديث:")
        print("┌─────────────────────────────────────────────────────────┐")
        print("│ ✅ تم إضافة نوع 'شركة شحن' إلى قائمة أنواع الموردين    │")
        print("│ ✅ النوع الجديد متاح في واجهة المستخدم               │")
        print("│ ✅ يمكن إنشاء موردين من نوع 'شركة شحن'              │")
        print("│ ✅ التكامل مع قاعدة البيانات يعمل بشكل مثالي         │")
        print("│ ✅ تم تحديث التوثيق ليشمل النوع الجديد              │")
        print("└─────────────────────────────────────────────────────────┘")
        print("\n🎯 يمكن الآن إضافة شركات الشحن كموردين في النظام!")
        
    elif passed >= total * 0.8:
        print("\n⭐ معظم الاختبارات نجحت!")
        print("🔧 النظام يعمل بشكل ممتاز مع تحسينات طفيفة")
        
    else:
        print("\n⚠️ النظام يحتاج مراجعة إضافية")
        print("🔧 بعض التحسينات تحتاج تطوير أكثر")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
