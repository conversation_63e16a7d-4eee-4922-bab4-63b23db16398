#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص ومراجعة واجهة المستخدم
UI Audit Tool - Comprehensive review of user interface components
"""

import sys
import os
from datetime import datetime
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class UIAuditTool:
    """أداة فحص واجهة المستخدم"""
    
    def __init__(self):
        """تهيئة الأداة"""
        self.audit_log = []
        self.issues_found = []
        self.ui_files_checked = 0
        self.components_found = 0
        
    def log_audit(self, category: str, message: str, status: str = "INFO"):
        """تسجيل نتائج الفحص"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{status}] {category}: {message}"
        self.audit_log.append(log_entry)
        print(log_entry)
        
        if status in ["ERROR", "WARNING"]:
            self.issues_found.append({
                'category': category,
                'message': message,
                'status': status,
                'timestamp': timestamp
            })
    
    def check_main_window_structure(self):
        """فحص بنية النافذة الرئيسية"""
        self.log_audit("النافذة الرئيسية", "بدء فحص بنية النافذة الرئيسية")
        
        main_window_path = "src/ui/main_window.py"
        if os.path.exists(main_window_path):
            self.ui_files_checked += 1
            self.log_audit("النافذة الرئيسية", "ملف النافذة الرئيسية موجود", "SUCCESS")
            
            # فحص محتوى الملف
            try:
                with open(main_window_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # فحص المكونات الأساسية
                required_components = [
                    "class MainWindow",
                    "QMainWindow",
                    "setupUi",
                    "menuBar",
                    "statusBar"
                ]
                
                for component in required_components:
                    if component in content:
                        self.components_found += 1
                        self.log_audit("النافذة الرئيسية", f"المكون {component} موجود", "SUCCESS")
                    else:
                        self.log_audit("النافذة الرئيسية", f"المكون {component} مفقود", "WARNING")
                
                # فحص القوائم
                menu_items = ["الملف", "التحرير", "العرض", "أدوات", "مساعدة"]
                for menu in menu_items:
                    if menu in content:
                        self.log_audit("النافذة الرئيسية", f"قائمة {menu} موجودة", "SUCCESS")
                    else:
                        self.log_audit("النافذة الرئيسية", f"قائمة {menu} قد تكون مفقودة", "INFO")
                        
            except Exception as e:
                self.log_audit("النافذة الرئيسية", f"خطأ في قراءة الملف: {str(e)}", "ERROR")
        else:
            self.log_audit("النافذة الرئيسية", "ملف النافذة الرئيسية غير موجود", "ERROR")
    
    def check_shipment_windows(self):
        """فحص نوافذ الشحنات"""
        self.log_audit("نوافذ الشحنات", "بدء فحص نوافذ الشحنات")
        
        shipment_files = [
            ("src/ui/shipments/shipments_window.py", "نافذة إدارة الشحنات"),
            ("src/ui/shipments/new_shipment_window.py", "نافذة الشحنة الجديدة"),
            ("src/ui/shipments/advanced_tracking_window.py", "نافذة التتبع المتقدم")
        ]
        
        for file_path, description in shipment_files:
            if os.path.exists(file_path):
                self.ui_files_checked += 1
                self.log_audit("نوافذ الشحنات", f"{description} موجودة", "SUCCESS")
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # فحص المكونات الأساسية
                    if "class" in content and "QDialog" in content or "QMainWindow" in content:
                        self.components_found += 1
                        self.log_audit("نوافذ الشحنات", f"{description} - البنية الأساسية صحيحة", "SUCCESS")
                    else:
                        self.log_audit("نوافذ الشحنات", f"{description} - مشكلة في البنية الأساسية", "WARNING")
                        
                    # فحص الأزرار الأساسية
                    buttons = ["حفظ", "إلغاء", "موافق", "إغلاق"]
                    button_count = 0
                    for button in buttons:
                        if button in content:
                            button_count += 1
                    
                    if button_count > 0:
                        self.log_audit("نوافذ الشحنات", f"{description} - يحتوي على {button_count} أزرار", "SUCCESS")
                    else:
                        self.log_audit("نوافذ الشحنات", f"{description} - لا يحتوي على أزرار أساسية", "WARNING")
                        
                except Exception as e:
                    self.log_audit("نوافذ الشحنات", f"{description} - خطأ في القراءة: {str(e)}", "ERROR")
            else:
                self.log_audit("نوافذ الشحنات", f"{description} غير موجودة", "ERROR")
    
    def check_dialog_windows(self):
        """فحص النوافذ الحوارية"""
        self.log_audit("النوافذ الحوارية", "بدء فحص النوافذ الحوارية")
        
        dialog_files = [
            ("src/ui/dialogs/auto_fill_dialog.py", "نافذة التعبئة التلقائية"),
            ("src/ui/dialogs/supplier_dialog.py", "نافذة الموردين"),
            ("src/ui/dialogs/item_dialog.py", "نافذة الأصناف")
        ]
        
        for file_path, description in dialog_files:
            if os.path.exists(file_path):
                self.ui_files_checked += 1
                self.log_audit("النوافذ الحوارية", f"{description} موجودة", "SUCCESS")
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # فحص البنية الأساسية للحوار
                    if "QDialog" in content:
                        self.components_found += 1
                        self.log_audit("النوافذ الحوارية", f"{description} - ترث من QDialog", "SUCCESS")
                    else:
                        self.log_audit("النوافذ الحوارية", f"{description} - لا ترث من QDialog", "WARNING")
                        
                except Exception as e:
                    self.log_audit("النوافذ الحوارية", f"{description} - خطأ في القراءة: {str(e)}", "ERROR")
            else:
                self.log_audit("النوافذ الحوارية", f"{description} غير موجودة", "INFO")
    
    def check_ui_resources(self):
        """فحص موارد واجهة المستخدم"""
        self.log_audit("موارد الواجهة", "بدء فحص موارد واجهة المستخدم")
        
        # فحص مجلدات الموارد
        resource_dirs = [
            ("src/ui", "مجلد واجهة المستخدم الرئيسي"),
            ("src/ui/shipments", "مجلد نوافذ الشحنات"),
            ("src/ui/dialogs", "مجلد النوافذ الحوارية"),
            ("src/ui/widgets", "مجلد الأدوات المخصصة")
        ]
        
        for dir_path, description in resource_dirs:
            if os.path.exists(dir_path):
                self.log_audit("موارد الواجهة", f"{description} موجود", "SUCCESS")
                
                # عد الملفات في المجلد
                try:
                    files = [f for f in os.listdir(dir_path) if f.endswith('.py')]
                    self.log_audit("موارد الواجهة", f"{description} يحتوي على {len(files)} ملف Python", "INFO")
                except Exception as e:
                    self.log_audit("موارد الواجهة", f"خطأ في قراءة {description}: {str(e)}", "ERROR")
            else:
                self.log_audit("موارد الواجهة", f"{description} غير موجود", "WARNING")
        
        # فحص ملفات الأيقونات والصور
        icon_dirs = ["icons", "images", "resources"]
        for icon_dir in icon_dirs:
            if os.path.exists(icon_dir):
                self.log_audit("موارد الواجهة", f"مجلد {icon_dir} موجود", "SUCCESS")
            else:
                self.log_audit("موارد الواجهة", f"مجلد {icon_dir} غير موجود", "INFO")
    
    def check_ui_imports(self):
        """فحص استيراد مكتبات واجهة المستخدم"""
        self.log_audit("استيراد المكتبات", "بدء فحص استيراد مكتبات واجهة المستخدم")
        
        # فحص الملف الرئيسي
        main_file = "main.py"
        if os.path.exists(main_file):
            try:
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص استيراد PySide6/PyQt
                ui_libraries = ["PySide6", "PyQt5", "PyQt6"]
                library_found = False
                
                for lib in ui_libraries:
                    if lib in content:
                        self.log_audit("استيراد المكتبات", f"مكتبة {lib} مستخدمة", "SUCCESS")
                        library_found = True
                        break
                
                if not library_found:
                    self.log_audit("استيراد المكتبات", "لم يتم العثور على مكتبة واجهة مستخدم", "ERROR")
                
                # فحص استيراد QApplication
                if "QApplication" in content:
                    self.log_audit("استيراد المكتبات", "QApplication مستورد", "SUCCESS")
                else:
                    self.log_audit("استيراد المكتبات", "QApplication غير مستورد", "WARNING")
                    
            except Exception as e:
                self.log_audit("استيراد المكتبات", f"خطأ في قراءة main.py: {str(e)}", "ERROR")
        else:
            self.log_audit("استيراد المكتبات", "ملف main.py غير موجود", "ERROR")
    
    def run_complete_audit(self):
        """تشغيل الفحص الشامل"""
        self.log_audit("فحص شامل", "بدء الفحص الشامل لواجهة المستخدم")
        
        # 1. فحص النافذة الرئيسية
        self.check_main_window_structure()
        
        # 2. فحص نوافذ الشحنات
        self.check_shipment_windows()
        
        # 3. فحص النوافذ الحوارية
        self.check_dialog_windows()
        
        # 4. فحص موارد الواجهة
        self.check_ui_resources()
        
        # 5. فحص استيراد المكتبات
        self.check_ui_imports()
        
        # تقرير نهائي
        self.log_audit("تقرير نهائي", "=" * 50)
        self.log_audit("تقرير نهائي", f"تم فحص {self.ui_files_checked} ملف واجهة مستخدم", "INFO")
        self.log_audit("تقرير نهائي", f"تم العثور على {self.components_found} مكون", "INFO")
        
        if not self.issues_found:
            self.log_audit("تقرير نهائي", "✅ لم يتم العثور على مشاكل في واجهة المستخدم", "SUCCESS")
        else:
            self.log_audit("تقرير نهائي", f"⚠️ تم العثور على {len(self.issues_found)} مشكلة", "WARNING")
            for issue in self.issues_found:
                print(f"[مشكلة مكتشفة] {issue['category']}: {issue['message']} [{issue['status']}]")
        
        return {
            'success': len(self.issues_found) == 0,
            'issues_count': len(self.issues_found),
            'issues': self.issues_found,
            'audit_log': self.audit_log,
            'files_checked': self.ui_files_checked,
            'components_found': self.components_found
        }

def main():
    """الدالة الرئيسية"""
    try:
        print("🔧 أداة فحص واجهة المستخدم")
        print("=" * 50)
        
        audit_tool = UIAuditTool()
        result = audit_tool.run_complete_audit()
        
        print("\n" + "=" * 50)
        if result['success']:
            print("✅ تم اجتياز جميع فحوصات واجهة المستخدم!")
        else:
            print(f"⚠️ تم العثور على {result['issues_count']} مشكلة في واجهة المستخدم")
        
        print(f"📊 إحصائيات: {result['files_checked']} ملف، {result['components_found']} مكون")
        
        return result
    except Exception as e:
        print(f"❌ خطأ في تشغيل أداة الفحص: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
