#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استيراد حقلي حالة الشحنة وحالة الإفراج من الإكسيل
"""

import pandas as pd
import os
from datetime import datetime

def create_status_test_file():
    """إنشاء ملف إكسيل لاختبار حقلي الحالة"""
    try:
        print("🔄 إنشاء ملف اختبار حقول الحالة...")
        
        # بيانات الاختبار
        data = {
            'التاريخ': [
                '2025-07-06',
                '2025-07-07', 
                '2025-07-08',
                '2025-07-09'
            ],
            'المورد': [
                'شركة الإمارات للتجارة',
                'شركة الكويت التجارية',
                'مؤسسة البحرين للاستيراد',
                'شركة قطر للتصدير'
            ],
            'بوليصة الشحن': [
                'BOL-2025-001',
                'BOL-2025-002',
                'BOL-2025-003',
                'BOL-2025-004'
            ],
            'ملاحظات': [
                'شحنة مستوردة من دبي',
                'شحنة عاجلة من الكويت',
                'شحنة منتجات إلكترونية',
                'شحنة مواد غذائية'
            ],
            'حالة الشحنة': [
                'تم الشحن',
                'في الطريق',
                'وصلت الميناء',
                'تم التسليم'
            ],
            'حالة الإفراج': [
                'مع الافراج',
                'بدون الافراج',
                'مع الافراج',
                'مع الافراج'
            ],
            'شركة الشحن': [
                'شركة الإمارات للشحن',
                'الخطوط الكويتية',
                'شحن البحرين',
                'قطر للنقل البحري'
            ],
            'رقم DHL': [
                'DHL123456',
                'DHL789012',
                'DHL345678',
                'DHL901234'
            ],
            'ميناء الوصول': [
                'ميناء جبل علي',
                'ميناء الشويخ',
                'ميناء المنامة',
                'ميناء الدوحة'
            ],
            'تاريخ الوصول المتوقع': [
                '2025-08-06',
                '2025-08-07',
                '2025-08-08',
                '2025-08-09'
            ],
            'رقم الحاوية': [
                'MSKU1234567, TCLU9876543',
                'ABCD1111111; EFGH2222222',
                'MNOP4444444/QRST5555555',
                'ZYXW7777777'
            ]
        }
        
        # إنشاء DataFrame
        df = pd.DataFrame(data)
        
        # حفظ الملف
        filename = 'test_status_fields.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء الملف: {filename}")
        print(f"📍 المسار الكامل: {os.path.abspath(filename)}")
        
        # عرض محتويات الملف
        print("\n📋 محتويات الملف:")
        print("=" * 80)
        
        for i, row in df.iterrows():
            print(f"الصف {i+1}:")
            print(f"  التاريخ: {row['التاريخ']}")
            print(f"  المورد: {row['المورد']}")
            print(f"  حالة الشحنة: {row['حالة الشحنة']}")
            print(f"  حالة الإفراج: {row['حالة الإفراج']}")
            print(f"  الحاويات: {row['رقم الحاوية']}")
            print("-" * 40)
        
        print("\n🎯 الحقول الجديدة المضافة:")
        print("• حالة الشحنة: تم الشحن، في الطريق، وصلت الميناء، تم التسليم")
        print("• حالة الإفراج: مع الافراج، بدون الافراج")
        
        print("\n📝 تعليمات الاختبار:")
        print("1. افتح شاشة الشحنة الجديدة")
        print("2. انقر على زر 'استيراد إكسيل'")
        print("3. اختر الملف المُنشأ")
        print("4. تحقق من استيراد حالة الشحنة وحالة الإفراج")
        print("5. تحقق من استيراد الحاويات المتعددة")
        
        return filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return None

def test_status_values():
    """اختبار قيم الحالة المختلفة"""
    print("\n🧪 اختبار قيم الحالة:")
    print("=" * 50)
    
    # قيم حالة الشحنة المتوقعة
    shipment_statuses = [
        "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق",
        "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
    ]
    
    # قيم حالة الإفراج المتوقعة
    clearance_statuses = ["بدون الافراج", "مع الافراج"]
    
    print("📋 حالات الشحنة المدعومة:")
    for i, status in enumerate(shipment_statuses, 1):
        print(f"  {i}. {status}")
    
    print("\n📋 حالات الإفراج المدعومة:")
    for i, status in enumerate(clearance_statuses, 1):
        print(f"  {i}. {status}")
    
    print("\n✨ ميزات إضافية:")
    print("• إذا لم توجد الحالة في القائمة، سيتم إضافتها تلقائياً")
    print("• يمكن استخدام أي قيم مخصصة للحالات")
    print("• النظام يدعم البحث الذكي عن الحالات")

if __name__ == "__main__":
    print("🚀 اختبار استيراد حقول الحالة من الإكسيل")
    print("=" * 60)
    
    # إنشاء ملف الاختبار
    filename = create_status_test_file()
    
    if filename:
        print(f"\n🎉 تم إنشاء ملف الاختبار بنجاح!")
        print(f"📁 اسم الملف: {filename}")
        
        # اختبار قيم الحالة
        test_status_values()
        
        print(f"\n✅ الملف جاهز للاختبار!")
    else:
        print("\n❌ فشل في إنشاء ملف الاختبار!")
