#!/usr/bin/env python3
"""
عرض توضيحي لنافذة تتبع طلبات الشراء المتقدمة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print("🚀 تشغيل نافذة تتبع طلبات الشراء المتقدمة...")
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # استيراد وإنشاء النافذة
        from src.ui.suppliers.purchase_orders_tracking_window import PurchaseOrdersTrackingWindow
        
        print("✅ تم استيراد النافذة بنجاح")
        
        # إنشاء النافذة
        tracking_window = PurchaseOrdersTrackingWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📊 النافذة تحتوي على:")
        print("   - قسم الإحصائيات مع 6 بطاقات")
        print("   - قسم البحث والفلترة المتقدمة")
        print("   - جدول تفصيلي بـ 15 عمود")
        print("   - شريط أدوات متكامل")
        print("   - شريط حالة مع مؤشرات")
        
        # عرض النافذة
        tracking_window.show()
        
        print("🎉 النافذة جاهزة للاستخدام!")
        print("💡 الميزات المتاحة:")
        print("   ✓ البحث الفوري في جميع الحقول")
        print("   ✓ فلترة متقدمة حسب الحالة والمورد والتاريخ والقيمة")
        print("   ✓ إحصائيات شاملة ومؤشرات بصرية")
        print("   ✓ تتبع حالة التأخير ونسب الإنجاز")
        print("   ✓ تصدير التقارير")
        print("   ✓ واجهة عربية متكاملة")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
