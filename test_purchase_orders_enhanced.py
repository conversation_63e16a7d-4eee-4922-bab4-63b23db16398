#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شاشة طلبات الشراء المحسنة
Test Enhanced Purchase Orders Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# استيراد النافذة
from src.ui.suppliers.purchase_orders_window import PurchaseOrdersWindow

def main():
    """تشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه النص من اليمين لليسار للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = PurchaseOrdersWindow()
    window.show()
    
    print("🚀 تم تشغيل شاشة طلبات الشراء المحسنة")
    print("✅ التحسينات المطبقة:")
    print("   - إعادة ترتيب الحقول في صفوف متعددة")
    print("   - زيادة ارتفاع الحقول (35 بكسل)")
    print("   - تحويل الحقول الرقمية إلى نصية")
    print("   - إصلاح مشكلة اختيار المورد")
    print("   - حساب الإجمالي التلقائي")
    print("   - تصميم محسن ومتجاوب")
    print("")
    print("🆕 التحسينات الجديدة في تبويب الأصناف:")
    print("   - إضافة عمود تاريخ الإنتاج")
    print("   - إضافة عمود تاريخ الانتهاء")
    print("   - إصلاح حساب الإجمالي التلقائي")
    print("   - إصلاح حساب الكمية المتبقية")
    print("   - تحديث فوري للحسابات عند تغيير الكمية أو السعر")
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
