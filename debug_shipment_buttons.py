#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def debug_buttons():
    app = QApplication(sys.argv)
    
    try:
        window = NewShipmentWindow()
        
        print("=== فحص أزرار شاشة الشحنة الجديدة ===")
        
        # التحقق من وجود الأزرار
        buttons_to_check = [
            ('new_button', '🆕 إضافة'),
            ('save_button', '💾 حفظ'),
            ('edit_button', '✏️ تعديل'),
            ('exit_button', '🚪 خروج')
        ]
        
        for button_attr, expected_text in buttons_to_check:
            if hasattr(window, button_attr):
                button = getattr(window, button_attr)
                print(f"✅ {button_attr}: موجود")
                print(f"   النص: {button.text()}")
                print(f"   مرئي: {button.isVisible()}")
                print(f"   مفعل: {button.isEnabled()}")
                print(f"   الحجم: {button.size().width()}x{button.size().height()}")
            else:
                print(f"❌ {button_attr}: غير موجود")
        
        # التحقق من التخطيط
        print("\n=== فحص التخطيط ===")
        main_layout = window.layout()
        if main_layout:
            print(f"التخطيط الرئيسي: {type(main_layout).__name__}")
            print(f"عدد العناصر: {main_layout.count()}")
            
            # البحث عن تخطيط الأزرار
            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and item.layout():
                    layout = item.layout()
                    print(f"  تخطيط فرعي {i}: {type(layout).__name__} - عدد العناصر: {layout.count()}")
                    
                    # فحص عناصر التخطيط الفرعي
                    for j in range(layout.count()):
                        sub_item = layout.itemAt(j)
                        if sub_item and sub_item.widget():
                            widget = sub_item.widget()
                            if hasattr(widget, 'text'):
                                print(f"    عنصر {j}: {type(widget).__name__} - '{widget.text()}'")
        
        window.show()
        print("\n✅ تم فتح النافذة - تحقق من الأزرار بصرياً")
        
        # عدم تشغيل الحلقة الرئيسية للتطبيق
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_buttons()
