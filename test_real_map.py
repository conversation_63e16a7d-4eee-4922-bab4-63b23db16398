#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة الخرائط الحقيقية
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(__file__))

def test_real_map_window():
    """اختبار نافذة الخرائط الحقيقية"""
    try:
        print("بدء اختبار نافذة الخرائط الحقيقية...")
        
        # استيراد النافذة
        from src.ui.shipments.real_map_window import RealMapWindow
        print("✅ تم استيراد RealMapWindow بنجاح")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✅ تم إنشاء التطبيق")
        
        # إنشاء النافذة
        window = RealMapWindow()
        print("✅ تم إنشاء النافذة")
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة")
        
        # تشغيل التطبيق
        print("🚀 تشغيل التطبيق...")
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_real_map_window())
