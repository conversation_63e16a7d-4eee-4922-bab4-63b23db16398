#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حل مشكلة تعديل الشحنات بعد الإصلاح
"""

import sys
import os
sys.path.append('.')

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow
from src.database.database_manager import DatabaseManager
from src.database.models import Shipment

def test_edit_functionality_fixed():
    """اختبار وظيفة التعديل بعد الإصلاح"""
    try:
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("=== اختبار حل مشكلة تعديل الشحنات (محدث) ===")
        
        # التحقق من وجود شحنات في قاعدة البيانات
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        shipments = session.query(Shipment).all()
        print(f"📊 عدد الشحنات الموجودة: {len(shipments)}")
        
        if len(shipments) == 0:
            print("❌ لا توجد شحنات للاختبار")
            session.close()
            return False
        
        # اختبار 1: فتح نافذة التعديل مباشرة
        print("\n1️⃣ اختبار فتح نافذة التعديل:")
        first_shipment = shipments[0]
        print(f"   - الشحنة المختارة: {first_shipment.shipment_number}")
        print(f"   - معرف الشحنة: {first_shipment.id}")
        
        # اختبار إنشاء النافذة
        try:
            edit_window = NewShipmentWindow(shipment_id=first_shipment.id)
            print(f"   - عنوان النافذة: {edit_window.windowTitle()}")
            print(f"   - وضع التعديل: {'✅ مفعل' if edit_window.is_edit_mode else '❌ غير مفعل'}")
            print(f"   - معرف الشحنة المحمل: {edit_window.current_shipment_id}")
            
            # اختبار 2: التحقق من تحميل البيانات
            print("\n2️⃣ اختبار تحميل البيانات:")
            shipment_number = edit_window.shipment_number_edit.text()
            supplier_name = edit_window.supplier_edit.text()
            print(f"   - رقم الشحنة المحمل: {shipment_number}")
            print(f"   - اسم المورد المحمل: {supplier_name}")
            
            # اختبار 3: التحقق من الحقول الإضافية
            print("\n3️⃣ اختبار الحقول الإضافية:")
            if hasattr(edit_window, 'shipping_policy_edit'):
                shipping_policy = edit_window.shipping_policy_edit.text()
                print(f"   - بوليصة الشحن: {shipping_policy}")
            else:
                print("   - حقل بوليصة الشحن غير موجود")
                
            if hasattr(edit_window, 'container_number_edit'):
                container_number = edit_window.container_number_edit.text()
                print(f"   - رقم الحاوية: {container_number}")
            else:
                print("   - حقل رقم الحاوية غير موجود")
            
            # اختبار 4: التحقق من تحميل الأصناف
            print("\n4️⃣ اختبار تحميل الأصناف:")
            items_count = edit_window.items_table.rowCount()
            print(f"   - عدد الأصناف المحملة: {items_count}")
            
            # اختبار 5: التحقق من تحميل الحاويات
            print("\n5️⃣ اختبار تحميل الحاويات:")
            containers_count = edit_window.containers_table.rowCount()
            print(f"   - عدد الحاويات المحملة: {containers_count}")
            
            print("\n✅ تم إنشاء نافذة التعديل بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة التعديل: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        session.close()
        
        print(f"\n{'='*50}")
        print("✅ تم حل مشاكل التعديل بنجاح:")
        print("   1. ✅ إصلاح مشكلة shipment_date")
        print("   2. ✅ إصلاح مشكلة أسماء الحقول")
        print("   3. ✅ إضافة الحقول الجديدة لقاعدة البيانات")
        print("   4. ✅ تحميل البيانات يعمل بدون أخطاء")
        print("   5. ✅ دعم كامل لوضع التعديل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_edit_functionality_fixed()
    if success:
        print("\n🎉 تم حل جميع مشاكل التعديل بنجاح!")
        print("✅ يمكن الآن:")
        print("   • فتح الشحنات للتعديل بدون أخطاء")
        print("   • تحميل جميع البيانات بشكل صحيح")
        print("   • استخدام الحقول الجديدة")
        print("   • حفظ التعديلات بدون مشاكل")
    else:
        print("⚠️ لا تزال هناك مشاكل تحتاج إلى حل")
