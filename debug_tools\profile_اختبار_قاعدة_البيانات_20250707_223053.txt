تحليل الأداء: اختبار_قاعدة_البيانات
وقت التنفيذ: 0.259 ثانية
تغيير الذاكرة: +0.02 MB
==================================================
         10111 function calls (9874 primitive calls) in 0.258 seconds

   Ordered by: cumulative time
   List reduced from 493 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.250    0.250 E:\project\backup\‏‏ProShipment1\src\database\database_manager.py:20(__init__)
        1    0.000    0.000    0.248    0.248 <string>:1(create_engine)
      3/1    0.000    0.000    0.248    0.248 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py:249(warned)
        1    0.001    0.001    0.248    0.248 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\create.py:92(create_engine)
        1    0.000    0.000    0.157    0.157 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\url.py:761(_get_entrypoint)
        1    0.000    0.000    0.157    0.157 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\langhelpers.py:438(load)
        1    0.000    0.000    0.157    0.157 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\__init__.py:23(_auto_fn)
        1    0.000    0.000    0.157    0.157 {__feature_import__}
      8/1    0.001    0.000    0.155    0.155 <frozen importlib._bootstrap>:1349(_find_and_load)
      8/1    0.001    0.000    0.154    0.154 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
      8/1    0.001    0.000    0.150    0.150 <frozen importlib._bootstrap>:911(_load_unlocked)
      8/1    0.000    0.000    0.149    0.149 <frozen importlib._bootstrap_external>:1020(exec_module)
     18/2    0.000    0.000    0.143    0.072 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
     27/1    0.005    0.000    0.143    0.143 {built-in method builtins.exec}
        1    0.000    0.000    0.143    0.143 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\sqlite\__init__.py:1(<module>)
    34/10    0.001    0.000    0.101    0.010 <frozen importlib._bootstrap>:1390(_handle_fromlist)
        2    0.000    0.000    0.100    0.050 {built-in method builtins.__import__}
        1    0.001    0.001    0.083    0.083 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\sqlite\aiosqlite.py:1(<module>)
       16    0.000    0.000    0.081    0.005 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\re\__init__.py:330(_compile)
        3    0.000    0.000    0.081    0.027 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\re\__init__.py:287(compile)


