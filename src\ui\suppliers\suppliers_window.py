# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين الرئيسية
Main Suppliers Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QAction

from .suppliers_data import SuppliersDataWidget, SuppliersDataWindow
from .suppliers_operations import SuppliersOperationsWidget, SuppliersOperationsWindow
from .suppliers_reports import SuppliersReportsWidget, SuppliersReportsWindow
from .purchase_orders_window import PurchaseOrdersWindow
from .advanced_remittances_window import AdvancedRemittancesWindow

class SuppliersWindow(QMainWindow):
    """نافذة إدارة الموردين الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الموردين - نظام إدارة الشحنات")

        # تعيين النافذة لتفتح في وضع ملء الشاشة
        self.setWindowState(Qt.WindowMaximized)

        # تعيين الحد الأدنى لحجم النافذة
        self.setMinimumSize(1200, 800)

        # تحسين عرض النافذة في وضع ملء الشاشة
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.setFocusPolicy(Qt.StrongFocus)

        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي محسن لوضع ملء الشاشة
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الموردين والشركاء التجاريين والحوالات المالية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # قسم الأنظمة المنفصلة
        systems_frame = QFrame()
        systems_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        systems_layout = QGridLayout(systems_frame)
        systems_layout.setSpacing(20)

        # إنشاء أزرار الأنظمة المنفصلة
        self.create_system_buttons(systems_layout)

        main_layout.addWidget(systems_frame)
        
        # أزرار التحكم محسنة لوضع ملء الشاشة
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 15, 20, 15)
        buttons_layout.addStretch()

        refresh_button = QPushButton("🔄 تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_all_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        export_button = QPushButton("📤 تصدير البيانات")
        export_button.clicked.connect(self.export_data)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)

        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addWidget(close_button)

        main_layout.addWidget(buttons_frame)

        # متغيرات النوافذ المنفصلة
        self.data_window = None
        self.operations_window = None
        self.reports_window = None
        self.purchase_orders_window = None
        self.supplier_remittances_window = None

    def create_system_buttons(self, layout):
        """إنشاء أزرار الأنظمة المنفصلة"""

        # زر إدارة بيانات الموردين
        data_button = QPushButton("📋 إدارة بيانات الموردين")
        data_button.clicked.connect(self.open_suppliers_data)
        data_button.setStyleSheet(self.get_system_button_style("#3498db"))
        layout.addWidget(data_button, 0, 0)

        # زر عمليات الموردين
        operations_button = QPushButton("💼 عمليات الموردين")
        operations_button.clicked.connect(self.open_suppliers_operations)
        operations_button.setStyleSheet(self.get_system_button_style("#e67e22"))
        layout.addWidget(operations_button, 0, 1)

        # زر التقارير
        reports_button = QPushButton("📊 تقارير الموردين")
        reports_button.clicked.connect(self.open_suppliers_reports)
        reports_button.setStyleSheet(self.get_system_button_style("#9b59b6"))
        layout.addWidget(reports_button, 1, 0)

        # زر طلبات الشراء
        purchase_orders_button = QPushButton("🛒 طلبات الشراء")
        purchase_orders_button.clicked.connect(self.open_purchase_orders)
        purchase_orders_button.setStyleSheet(self.get_system_button_style("#27ae60"))
        layout.addWidget(purchase_orders_button, 1, 1)

        # زر حوالات الموردين
        remittances_button = QPushButton("💸 حوالات الموردين")
        remittances_button.clicked.connect(self.open_supplier_remittances)
        remittances_button.setStyleSheet(self.get_system_button_style("#f39c12"))
        layout.addWidget(remittances_button, 1, 2)

    def get_system_button_style(self, color):
        """الحصول على نمط أزرار الأنظمة"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 30px 20px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 16px;
                min-height: 80px;
                min-width: 200px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {color}aa;
            }}
        """
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        new_supplier_action = QAction("مورد جديد", self)
        new_supplier_action.setShortcut("Ctrl+N")
        new_supplier_action.triggered.connect(self.new_supplier)
        file_menu.addAction(new_supplier_action)
        
        file_menu.addSeparator()
        
        export_action = QAction("تصدير البيانات", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        import_action = QAction("استيراد البيانات", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة البيانات
        data_menu = menubar.addMenu("البيانات")
        
        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        data_menu.addAction(refresh_action)
        
        data_menu.addSeparator()
        
        suppliers_action = QAction("إدارة بيانات الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers_data)
        data_menu.addAction(suppliers_action)

        operations_action = QAction("عمليات الموردين", self)
        operations_action.triggered.connect(self.open_suppliers_operations)
        data_menu.addAction(operations_action)

        reports_action = QAction("التقارير", self)
        reports_action.triggered.connect(self.open_suppliers_reports)
        data_menu.addAction(reports_action)

        purchase_orders_action = QAction("طلبات الشراء", self)
        purchase_orders_action.triggered.connect(self.open_purchase_orders)
        data_menu.addAction(purchase_orders_action)

        remittances_action = QAction("حوالات الموردين", self)
        remittances_action.triggered.connect(self.open_supplier_remittances)
        data_menu.addAction(remittances_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        suppliers_report_action = QAction("تقرير الموردين", self)
        suppliers_report_action.triggered.connect(self.generate_suppliers_report)
        reports_menu.addAction(suppliers_report_action)
        
        transactions_report_action = QAction("تقرير المعاملات", self)
        transactions_report_action.triggered.connect(self.generate_transactions_report)
        reports_menu.addAction(transactions_report_action)
        
        performance_report_action = QAction("تقرير الأداء", self)
        performance_report_action.triggered.connect(self.generate_performance_report)
        reports_menu.addAction(performance_report_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات محسن لوضع ملء الشاشة"""
        toolbar = self.addToolBar("أدوات الموردين")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #34495e;
                border: none;
                spacing: 5px;
                padding: 8px;
            }
            QToolBar QToolButton {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
                margin: 2px;
            }
            QToolBar QToolButton:hover {
                background-color: #3498db;
            }
            QToolBar QToolButton:pressed {
                background-color: #2980b9;
            }
        """)

        new_action = QAction("➕ مورد جديد", self)
        new_action.triggered.connect(self.new_supplier)
        new_action.setToolTip("إضافة مورد جديد")
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.refresh_all_data)
        refresh_action.setToolTip("تحديث جميع البيانات")
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        export_action = QAction("📤 تصدير", self)
        export_action.triggered.connect(self.export_data)
        export_action.setToolTip("تصدير البيانات")
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # إضافة زر لفتح طلبات الشراء
        purchase_orders_action = QAction("🛒 طلبات الشراء", self)
        purchase_orders_action.triggered.connect(self.open_purchase_orders)
        purchase_orders_action.setToolTip("فتح نافذة طلبات الشراء")
        toolbar.addAction(purchase_orders_action)

        # إضافة زر لفتح حوالات الموردين
        remittances_action = QAction("💸 حوالات الموردين", self)
        remittances_action.triggered.connect(self.open_supplier_remittances)
        remittances_action.setToolTip("فتح نافذة حوالات الموردين")
        toolbar.addAction(remittances_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة محسن لوضع ملء الشاشة"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #2c3e50;
                color: white;
                border-top: 1px solid #34495e;
                font-size: 12px;
                padding: 5px;
            }
            QStatusBar::item {
                border: none;
            }
        """)

        # رسالة الترحيب
        status_bar.showMessage("مرحباً بك في نظام إدارة الموردين - وضع ملء الشاشة", 3000)

        # إضافة معلومات دائمة
        from PySide6.QtWidgets import QLabel
        permanent_label = QLabel("جاهز للاستخدام")
        permanent_label.setStyleSheet("color: #1abc9c; font-weight: bold;")
        status_bar.addPermanentWidget(permanent_label)
    
    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            QMessageBox.information(
                self,
                "تم التحديث",
                "تم تحديث جميع البيانات بنجاح"
            )

        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}"
            )

    def new_supplier(self):
        """إضافة مورد جديد"""
        # فتح نافذة بيانات الموردين
        self.open_suppliers_data()

    def open_suppliers_data(self):
        """فتح نافذة إدارة بيانات الموردين"""
        if self.data_window is None or not self.data_window.isVisible():
            self.data_window = SuppliersDataWindow()
            self.data_window.show()
        else:
            self.data_window.raise_()
            self.data_window.activateWindow()

    def open_suppliers_operations(self):
        """فتح نافذة عمليات الموردين"""
        if self.operations_window is None or not self.operations_window.isVisible():
            self.operations_window = SuppliersOperationsWindow()
            self.operations_window.show()
        else:
            self.operations_window.raise_()
            self.operations_window.activateWindow()

    def open_suppliers_reports(self):
        """فتح نافذة تقارير الموردين"""
        if self.reports_window is None or not self.reports_window.isVisible():
            self.reports_window = SuppliersReportsWindow()
            self.reports_window.show()
        else:
            self.reports_window.raise_()
            self.reports_window.activateWindow()

    def open_purchase_orders(self):
        """فتح نافذة طلبات الشراء"""
        try:
            # التحقق من أن النافذة موجودة وصالحة
            if self.purchase_orders_window is None or not self.purchase_orders_window.isVisible():
                print("إنشاء نافذة طلبات الشراء جديدة...")
                self.purchase_orders_window = PurchaseOrdersWindow()
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.purchase_orders_window.destroyed.connect(lambda: setattr(self, 'purchase_orders_window', None))
                print("تم إنشاء النافذة بنجاح، جاري عرضها...")
                self.purchase_orders_window.show()
                print("تم عرض النافذة")
            else:
                print("النافذة موجودة، جاري تفعيلها...")
                self.purchase_orders_window.raise_()
                self.purchase_orders_window.activateWindow()

        except Exception as e:
            print(f"خطأ في فتح نافذة طلبات الشراء: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة طلبات الشراء:\n{str(e)}")
            # محاولة إعادة إنشاء النافذة
            try:
                self.purchase_orders_window = None
                self.purchase_orders_window = PurchaseOrdersWindow()
                self.purchase_orders_window.destroyed.connect(lambda: setattr(self, 'purchase_orders_window', None))
                self.purchase_orders_window.show()
            except Exception as e2:
                print(f"فشل في إعادة إنشاء النافذة: {e2}")
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء نافذة طلبات الشراء:\n{str(e2)}")

    def open_supplier_remittances(self):
        """فتح نظام حوالات الموردين المتقدم"""
        try:
            # التحقق من أن النافذة موجودة وصالحة
            if self.supplier_remittances_window is None or not self.supplier_remittances_window.isVisible():
                print("🚀 إنشاء نظام حوالات الموردين المتقدم...")
                self.supplier_remittances_window = AdvancedRemittancesWindow()
                # ربط إشارة الإغلاق لتنظيف المرجع
                self.supplier_remittances_window.destroyed.connect(lambda: setattr(self, 'supplier_remittances_window', None))
                print("✅ تم إنشاء النظام بنجاح، جاري عرضه...")
                self.supplier_remittances_window.show()
                print("🎉 تم عرض النظام المتقدم بنجاح")
            else:
                print("النظام موجود، جاري تفعيله...")
                self.supplier_remittances_window.raise_()
                self.supplier_remittances_window.activateWindow()

        except Exception as e:
            print(f"❌ خطأ في فتح نظام حوالات الموردين: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نظام حوالات الموردين المتقدم:\n{str(e)}")
            # محاولة إعادة إنشاء النظام
            try:
                self.supplier_remittances_window = None
                self.supplier_remittances_window = AdvancedRemittancesWindow()
                self.supplier_remittances_window.destroyed.connect(lambda: setattr(self, 'supplier_remittances_window', None))
                self.supplier_remittances_window.show()
            except Exception as e2:
                print(f"فشل في إعادة إنشاء النظام: {e2}")
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء نظام حوالات الموردين المتقدم:\n{str(e2)}")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تصدير البيانات قيد التطوير"
        )
    
    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة استيراد البيانات قيد التطوير"
        )
    
    def generate_suppliers_report(self):
        """إنتاج تقرير الموردين"""
        # التبديل إلى تبويب التقارير
        self.tab_widget.setCurrentIndex(2)
        self.reports_widget.generate_suppliers_report()
    
    def generate_transactions_report(self):
        """إنتاج تقرير المعاملات"""
        # التبديل إلى تبويب التقارير
        self.tab_widget.setCurrentIndex(2)
        self.reports_widget.generate_transactions_report()
    
    def generate_performance_report(self):
        """إنتاج تقرير الأداء"""
        # التبديل إلى تبويب التقارير
        self.tab_widget.setCurrentIndex(2)
        self.reports_widget.generate_performance_report()
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # يمكن إضافة تحقق من التغييرات غير المحفوظة هنا
        event.accept()
