# ميزات ربط الموردين بالعملات المطورة

## نظرة عامة

تم تطوير تبويب جديد في نافذة إدارة بيانات الموردين لربط الموردين بالعملات مع دعم العملات المتعددة والبحث المتقدم.

## الميزات الجديدة

### 1. تبويب ربط الموردين بالعملات

- **الموقع**: نافذة إدارة بيانات الموردين → تبويب "💰 ربط الموردين بالعملات"
- **الوظيفة**: ربط كل مورد بعملة أو أكثر مع إمكانية تحديد العملة المفضلة وأسعار الصرف المخصصة

### 2. البحث المتقدم للموردين

#### كيفية الوصول:
- **اختصار لوحة المفاتيح**: اضغط `F9` في تبويب ربط الموردين بالعملات
- **الزر**: اضغط على زر 🔍 بجانب حقل البحث

#### معايير البحث المتاحة:
- **كود المورد**: البحث بكود المورد
- **اسم المورد**: البحث باسم المورد (عربي أو إنجليزي)
- **نوع المورد**: فلترة حسب النوع (شركة، فرد، مؤسسة، شركة شحن)
- **المدينة**: البحث بالمدينة
- **الهاتف**: البحث بالهاتف أو الجوال
- **البريد الإلكتروني**: البحث بالبريد الإلكتروني

#### مميزات نافذة البحث:
- **بحث فوري**: النتائج تظهر أثناء الكتابة
- **ترتيب النتائج**: النتائج مرتبة أبجدياً
- **اختيار سريع**: نقر مزدوج لاختيار المورد
- **معلومات شاملة**: عرض جميع بيانات المورد في جدول منظم

### 3. اختيار العملات المتعددة

#### كيفية الوصول:
- اضغط على زر "اختيار العملات" في نموذج الإدخال

#### الميزات:
- **اختيار متعدد**: يمكن اختيار عدة عملات للمورد الواحد
- **العملة المفضلة**: تحديد عملة واحدة كمفضلة للمورد
- **أسعار الصرف المخصصة**: تحديد سعر صرف مخصص لكل عملة (اختياري)
- **معاينة فورية**: عرض العملات المحددة في الحقل الرئيسي

#### أزرار مساعدة:
- **☑️ تحديد الكل**: تحديد جميع العملات
- **☐ إلغاء تحديد الكل**: إلغاء تحديد جميع العملات

### 4. إدارة الروابط

#### إضافة روابط جديدة:
1. اختر المورد (باستخدام البحث العادي أو المتقدم)
2. اختر العملات المطلوبة
3. أضف ملاحظات (اختياري)
4. اضغط "إضافة ربط العملات"

#### التعامل مع الروابط الموجودة:
- **تحديث تلقائي**: إذا كان الربط موجود، سيتم تحديثه
- **تأكيد المستخدم**: رسالة تأكيد قبل تحديث الروابط الموجودة
- **حفظ البيانات**: الحفاظ على الروابط الأخرى للمورد

#### حذف الروابط:
- **حذف ناعم**: تعطيل الربط بدلاً من الحذف النهائي
- **تأكيد الحذف**: رسالة تأكيد قبل الحذف

## قاعدة البيانات

### جدول جديد: `supplier_currencies`

```sql
CREATE TABLE supplier_currencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_id INTEGER NOT NULL,
    currency_id INTEGER NOT NULL,
    is_preferred BOOLEAN DEFAULT 0,
    exchange_rate_override REAL,
    notes TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
    FOREIGN KEY (currency_id) REFERENCES currencies (id)
);
```

### الفهارس:
- **فهرس فريد**: منع تكرار ربط نفس المورد بنفس العملة
- **فهارس الأداء**: تسريع البحث والاستعلامات

### العلاقات:
- **Supplier → SupplierCurrency**: علاقة واحد إلى متعدد
- **Currency → SupplierCurrency**: علاقة واحد إلى متعدد
- **SupplierCurrency**: جدول الربط مع بيانات إضافية

## الملفات المضافة

### 1. نماذج قاعدة البيانات
- `src/database/models.py`: إضافة نموذج `SupplierCurrency`
- `src/database/migrations/add_supplier_currencies.py`: ملف الترحيل

### 2. واجهات المستخدم
- `src/ui/suppliers/supplier_currencies_tab.py`: التبويب الرئيسي
- `src/ui/suppliers/advanced_supplier_search_dialog.py`: نافذة البحث المتقدم
- `src/ui/suppliers/multi_currency_selection_dialog.py`: نافذة اختيار العملات

### 3. التحديثات
- `src/ui/suppliers/suppliers_data.py`: إضافة التبويب الجديد

## كيفية الاستخدام

### سيناريو 1: ربط مورد بعملة واحدة
1. افتح نافذة إدارة بيانات الموردين
2. انتقل إلى تبويب "ربط الموردين بالعملات"
3. ابحث عن المورد واختره
4. اضغط "اختيار العملات"
5. حدد العملة المطلوبة وضع علامة "مفضلة"
6. اضغط "حفظ الاختيار"
7. اضغط "إضافة ربط العملات"

### سيناريو 2: ربط مورد بعملات متعددة
1. اتبع نفس الخطوات السابقة
2. في نافذة اختيار العملات، حدد عدة عملات
3. اختر واحدة كمفضلة
4. حدد أسعار صرف مخصصة إذا لزم الأمر
5. احفظ وأضف الربط

### سيناريو 3: البحث المتقدم
1. في حقل البحث، اضغط F9 أو زر 🔍
2. أدخل معايير البحث المطلوبة
3. اضغط "بحث" أو اتركه للبحث التلقائي
4. اختر المورد من النتائج
5. اضغط "اختيار" أو انقر نقراً مزدوجاً

## المزايا

### للمستخدمين:
- **سهولة الاستخدام**: واجهة بديهية ومنظمة
- **بحث سريع**: العثور على الموردين بسرعة
- **مرونة في العملات**: دعم عملات متعددة لكل مورد
- **تخصيص الأسعار**: أسعار صرف مخصصة لكل مورد

### للنظام:
- **أداء محسن**: فهارس محسنة للبحث السريع
- **سلامة البيانات**: منع التكرار والتضارب
- **قابلية التوسع**: إمكانية إضافة ميزات جديدة
- **تتبع التغييرات**: تسجيل تواريخ الإنشاء والتحديث

## الاختبار

### بيانات تجريبية:
- **العملات**: ريال سعودي، دولار أمريكي، يورو، جنيه إسترليني، درهم إماراتي
- **الموردين**: أكثر من 180 مورد متنوع

### سيناريوهات الاختبار:
1. **إضافة ربط جديد**: مورد + عملة واحدة
2. **إضافة ربط متعدد**: مورد + عدة عملات
3. **تحديث ربط موجود**: تغيير العملة المفضلة
4. **البحث المتقدم**: جميع معايير البحث
5. **حذف ربط**: التأكد من الحذف الناعم

## الدعم والصيانة

### ملفات السجل:
- أخطاء قاعدة البيانات تظهر في رسائل منبثقة
- تحذيرات SQLAlchemy في وحدة التحكم

### النسخ الاحتياطي:
- يُنصح بعمل نسخة احتياطية قبل إضافة بيانات كثيرة
- الجدول الجديد مشمول في النسخ الاحتياطية التلقائية

### التحديثات المستقبلية:
- إمكانية تصدير تقارير العملات
- إحصائيات استخدام العملات
- تنبيهات تغيير أسعار الصرف
- ربط مع أنظمة أسعار الصرف الخارجية
