# -*- coding: utf-8 -*-
"""
واجهة إدارة حوالات الموردين
Supplier Remittances Management Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QCheckBox,
                               QTextEdit, QAbstractItemView, QSplitter, QDateEdit, QDoubleSpinBox,
                               QTabWidget, QFrame, QScrollArea, QGridLayout, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, QDate, QTimer, Signal
from PySide6.QtGui import QFont, QKeySequence, QShortcut, QPixmap, QIcon
from datetime import datetime

from ...database.database_manager import DatabaseManager
from ...database.models import SupplierRemittance, SupplierRemittanceDetail, Supplier, Currency


class SupplierRemittancesTab(QWidget):
    """تبويب إدارة حوالات الموردين"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_remittance_id = None
        self.supplier_details = []  # قائمة تفاصيل الموردين
        
        # إعداد مؤقت البحث
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # العنوان
        title_label = QLabel("إدارة حوالات الموردين")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء Splitter للتقسيم
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # الجانب الأيسر - النموذج
        form_widget = self.create_form_section()
        splitter.addWidget(form_widget)

        # الجانب الأيمن - الجدول
        table_widget = self.create_table_section()
        splitter.addWidget(table_widget)

        # تحديد نسب التقسيم
        splitter.setSizes([400, 600])

    def create_form_section(self):
        """إنشاء قسم النموذج"""
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(15)

        # معلومات الحوالة الأساسية
        basic_group = QGroupBox("معلومات الحوالة الأساسية")
        basic_layout = QFormLayout(basic_group)

        # رقم الحوالة
        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.remittance_number_edit.setReadOnly(True)
        basic_layout.addRow("رقم الحوالة:", self.remittance_number_edit)

        # تاريخ الحوالة
        self.remittance_date_edit = QDateEdit()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.remittance_date_edit.setCalendarPopup(True)
        basic_layout.addRow("تاريخ الحوالة:", self.remittance_date_edit)

        # العملة
        self.currency_combo = QComboBox()
        basic_layout.addRow("العملة:", self.currency_combo)

        # سعر الصرف
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.001, 999999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0)
        basic_layout.addRow("سعر الصرف:", self.exchange_rate_spin)

        form_layout.addWidget(basic_group)

        # معلومات البنوك
        banks_group = QGroupBox("معلومات البنوك")
        banks_layout = QFormLayout(banks_group)

        # البنك المرسل
        self.sender_bank_edit = QLineEdit()
        self.sender_bank_edit.setPlaceholderText("اسم البنك المرسل")
        banks_layout.addRow("البنك المرسل:", self.sender_bank_edit)

        self.sender_account_edit = QLineEdit()
        self.sender_account_edit.setPlaceholderText("رقم الحساب المرسل")
        banks_layout.addRow("رقم الحساب المرسل:", self.sender_account_edit)

        # البنك المستقبل
        self.receiver_bank_edit = QLineEdit()
        self.receiver_bank_edit.setPlaceholderText("اسم البنك المستقبل")
        banks_layout.addRow("البنك المستقبل:", self.receiver_bank_edit)

        self.receiver_country_edit = QLineEdit()
        self.receiver_country_edit.setPlaceholderText("دولة البنك المستقبل")
        banks_layout.addRow("دولة البنك:", self.receiver_country_edit)

        self.swift_code_edit = QLineEdit()
        self.swift_code_edit.setPlaceholderText("كود SWIFT")
        banks_layout.addRow("كود SWIFT:", self.swift_code_edit)

        form_layout.addWidget(banks_group)

        # الرسوم والمعلومات الإضافية
        fees_group = QGroupBox("الرسوم والمعلومات الإضافية")
        fees_layout = QFormLayout(fees_group)

        # رسوم التحويل
        self.transfer_fees_spin = QDoubleSpinBox()
        self.transfer_fees_spin.setRange(0, 999999)
        self.transfer_fees_spin.setDecimals(2)
        self.transfer_fees_spin.setSuffix(" ريال")
        fees_layout.addRow("رسوم التحويل:", self.transfer_fees_spin)

        # رسوم البنك
        self.bank_charges_spin = QDoubleSpinBox()
        self.bank_charges_spin.setRange(0, 999999)
        self.bank_charges_spin.setDecimals(2)
        self.bank_charges_spin.setSuffix(" ريال")
        fees_layout.addRow("رسوم البنك:", self.bank_charges_spin)

        # الغرض من التحويل
        self.purpose_edit = QTextEdit()
        self.purpose_edit.setMaximumHeight(60)
        self.purpose_edit.setPlaceholderText("الغرض من التحويل...")
        fees_layout.addRow("الغرض:", self.purpose_edit)

        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        fees_layout.addRow("الملاحظات:", self.notes_edit)

        form_layout.addWidget(fees_group)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة حوالة")
        self.add_button.clicked.connect(self.add_remittance)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(self.add_button)

        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_remittance)
        self.update_button.setEnabled(False)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(self.update_button)

        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        buttons_layout.addWidget(self.clear_button)

        form_layout.addLayout(buttons_layout)
        form_layout.addStretch()

        return form_widget

    def create_table_section(self):
        """إنشاء قسم الجدول"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setSpacing(10)

        # شريط البحث والفلترة
        search_layout = QHBoxLayout()

        search_label = QLabel("البحث:")
        search_layout.addWidget(search_label)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في رقم الحوالة، البنك، أو المورد...")
        self.search_edit.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_edit)

        # فلتر الحالة
        status_label = QLabel("الحالة:")
        search_layout.addWidget(status_label)

        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["الكل", "في الانتظار", "مرسلة", "مؤكدة", "مرحلة", "ملغاة"])
        self.status_filter_combo.currentTextChanged.connect(self.perform_search)
        search_layout.addWidget(self.status_filter_combo)

        table_layout.addLayout(search_layout)

        # الجدول
        self.remittances_table = QTableWidget()
        self.remittances_table.setColumnCount(8)
        self.remittances_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "التاريخ", "المبلغ الإجمالي", "العملة", "عدد الموردين", "الحالة", "البنك المستقبل", "الملاحظات"
        ])

        # ضبط خصائص الجدول
        self.remittances_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSortingEnabled(True)
        self.remittances_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # ضبط عرض الأعمدة
        header = self.remittances_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحوالة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # عدد الموردين
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.Stretch)           # البنك المستقبل
        header.setSectionResizeMode(7, QHeaderView.Stretch)           # الملاحظات

        # ربط الأحداث
        self.remittances_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.remittances_table.itemDoubleClicked.connect(self.open_remittance_details)

        table_layout.addWidget(self.remittances_table)

        # أزرار العمليات
        operations_layout = QHBoxLayout()

        self.details_button = QPushButton("تفاصيل الموردين")
        self.details_button.clicked.connect(self.open_remittance_details)
        self.details_button.setEnabled(False)
        self.details_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        operations_layout.addWidget(self.details_button)

        self.send_button = QPushButton("إرسال الحوالة")
        self.send_button.clicked.connect(self.send_remittance)
        self.send_button.setEnabled(False)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        operations_layout.addWidget(self.send_button)

        self.confirm_button = QPushButton("تأكيد الاستلام")
        self.confirm_button.clicked.connect(self.confirm_remittance)
        self.confirm_button.setEnabled(False)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        operations_layout.addWidget(self.confirm_button)

        self.post_button = QPushButton("ترحيل للحسابات")
        self.post_button.clicked.connect(self.post_remittance)
        self.post_button.setEnabled(False)
        self.post_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        operations_layout.addWidget(self.post_button)

        self.delete_button = QPushButton("حذف")
        self.delete_button.clicked.connect(self.delete_remittance)
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        operations_layout.addWidget(self.delete_button)

        operations_layout.addStretch()
        table_layout.addLayout(operations_layout)

        return table_widget

    def load_currencies(self):
        """تحميل العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter_by(is_active=True).order_by(Currency.name).all()

            self.currency_combo.clear()
            for currency in currencies:
                display_text = f"{currency.name} ({currency.code})"
                self.currency_combo.addItem(display_text, currency.id)

            # تحديد العملة الافتراضية (ريال سعودي)
            for i in range(self.currency_combo.count()):
                if "ريال" in self.currency_combo.itemText(i):
                    self.currency_combo.setCurrentIndex(i)
                    break

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل العملات: {str(e)}")
        finally:
            session.close()

    def load_data(self):
        """تحميل البيانات"""
        self.load_currencies()
        self.load_remittances()

    def load_remittances(self):
        """تحميل الحوالات"""
        session = self.db_manager.get_session()
        try:
            # استعلام الحوالات مع تفاصيلها
            query = session.query(SupplierRemittance).filter_by(is_active=True)

            # تطبيق فلتر الحالة
            status_filter = self.status_filter_combo.currentText()
            if status_filter != "الكل":
                status_map = {
                    "في الانتظار": "pending",
                    "مرسلة": "sent",
                    "مؤكدة": "confirmed",
                    "مرحلة": "posted",
                    "ملغاة": "cancelled"
                }
                if status_filter in status_map:
                    query = query.filter(SupplierRemittance.status == status_map[status_filter])

            # تطبيق البحث
            search_text = self.search_edit.text().strip()
            if search_text:
                query = query.filter(
                    SupplierRemittance.remittance_number.contains(search_text) |
                    SupplierRemittance.receiver_bank_name.contains(search_text) |
                    SupplierRemittance.sender_bank_name.contains(search_text)
                )

            remittances = query.order_by(SupplierRemittance.remittance_date.desc()).all()

            # تحديث الجدول
            self.remittances_table.setRowCount(len(remittances))

            for row, remittance in enumerate(remittances):
                # رقم الحوالة
                number_item = QTableWidgetItem(remittance.remittance_number)
                number_item.setData(Qt.UserRole, remittance.id)
                self.remittances_table.setItem(row, 0, number_item)

                # التاريخ
                date_item = QTableWidgetItem(str(remittance.remittance_date))
                self.remittances_table.setItem(row, 1, date_item)

                # المبلغ الإجمالي
                amount_item = QTableWidgetItem(f"{remittance.total_amount:,.2f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.remittances_table.setItem(row, 2, amount_item)

                # العملة
                currency_item = QTableWidgetItem(remittance.currency.code if remittance.currency else "")
                self.remittances_table.setItem(row, 3, currency_item)

                # عدد الموردين
                suppliers_count = len(remittance.remittance_details)
                count_item = QTableWidgetItem(str(suppliers_count))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.remittances_table.setItem(row, 4, count_item)

                # الحالة
                status_map = {
                    "pending": "في الانتظار",
                    "sent": "مرسلة",
                    "confirmed": "مؤكدة",
                    "posted": "مرحلة",
                    "cancelled": "ملغاة"
                }
                status_text = status_map.get(remittance.status, remittance.status)
                status_item = QTableWidgetItem(status_text)

                # تلوين الحالة
                if remittance.status == "pending":
                    status_item.setBackground(Qt.yellow)
                elif remittance.status == "sent":
                    status_item.setBackground(Qt.cyan)
                elif remittance.status == "confirmed":
                    status_item.setBackground(Qt.green)
                elif remittance.status == "posted":
                    status_item.setBackground(Qt.darkGreen)
                elif remittance.status == "cancelled":
                    status_item.setBackground(Qt.red)

                self.remittances_table.setItem(row, 5, status_item)

                # البنك المستقبل
                bank_item = QTableWidgetItem(remittance.receiver_bank_name or "")
                self.remittances_table.setItem(row, 6, bank_item)

                # الملاحظات
                notes_item = QTableWidgetItem(remittance.notes or "")
                self.remittances_table.setItem(row, 7, notes_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الحوالات: {str(e)}")
        finally:
            session.close()

    def on_search_changed(self):
        """عند تغيير نص البحث"""
        self.search_timer.stop()
        self.search_timer.start(300)

    def perform_search(self):
        """تنفيذ البحث"""
        self.load_remittances()

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.remittances_table.currentRow()
        if current_row >= 0:
            self.load_selected_remittance()
            self.update_buttons_state()
        else:
            self.clear_form()
            self.update_buttons_state()

    def update_buttons_state(self):
        """تحديث حالة الأزرار"""
        current_row = self.remittances_table.currentRow()
        has_selection = current_row >= 0

        self.details_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

        if has_selection:
            remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(remittance_id)
                if remittance:
                    # تحديث الأزرار حسب الحالة
                    self.send_button.setEnabled(remittance.status == "pending")
                    self.confirm_button.setEnabled(remittance.status == "sent")
                    self.post_button.setEnabled(remittance.status == "confirmed")
                    self.update_button.setEnabled(remittance.status in ["pending", "sent"])
                else:
                    self.send_button.setEnabled(False)
                    self.confirm_button.setEnabled(False)
                    self.post_button.setEnabled(False)
                    self.update_button.setEnabled(False)
            finally:
                session.close()
        else:
            self.send_button.setEnabled(False)
            self.confirm_button.setEnabled(False)
            self.post_button.setEnabled(False)
            self.update_button.setEnabled(False)

    def generate_remittance_number(self):
        """إنشاء رقم حوالة جديد"""
        session = self.db_manager.get_session()
        try:
            # الحصول على آخر رقم حوالة
            last_remittance = session.query(SupplierRemittance).order_by(
                SupplierRemittance.id.desc()
            ).first()

            if last_remittance and last_remittance.remittance_number:
                # استخراج الرقم من آخر حوالة
                try:
                    last_number = int(last_remittance.remittance_number.split('-')[-1])
                    new_number = last_number + 1
                except:
                    new_number = 1
            else:
                new_number = 1

            # تنسيق رقم الحوالة الجديد
            current_year = QDate.currentDate().year()
            return f"REM-{current_year}-{new_number:03d}"

        except Exception as e:
            print(f"خطأ في إنشاء رقم الحوالة: {str(e)}")
            return f"REM-{QDate.currentDate().year()}-001"
        finally:
            session.close()

    def add_remittance(self):
        """إضافة حوالة جديدة"""
        if not self.validate_form():
            return

        # فتح نافذة تفاصيل الموردين
        from .supplier_remittance_details_dialog import SupplierRemittanceDetailsDialog

        dialog = SupplierRemittanceDetailsDialog(self)
        if dialog.exec() == dialog.Accepted:
            supplier_details = dialog.get_supplier_details()

            if not supplier_details:
                QMessageBox.warning(self, "تحذير", "يجب إضافة مورد واحد على الأقل")
                return

            # حساب المبلغ الإجمالي
            total_amount = sum(detail['amount'] for detail in supplier_details)

            session = self.db_manager.get_session()
            try:
                # إنشاء الحوالة
                remittance = SupplierRemittance(
                    remittance_number=self.generate_remittance_number(),
                    remittance_date=self.remittance_date_edit.date().toPython(),
                    sender_bank_name=self.sender_bank_edit.text().strip(),
                    sender_account_number=self.sender_account_edit.text().strip(),
                    receiver_bank_name=self.receiver_bank_edit.text().strip(),
                    receiver_bank_country=self.receiver_country_edit.text().strip(),
                    total_amount=total_amount,
                    currency_id=self.currency_combo.currentData(),
                    exchange_rate=self.exchange_rate_spin.value(),
                    amount_in_base_currency=total_amount * self.exchange_rate_spin.value(),
                    transfer_fees=self.transfer_fees_spin.value(),
                    bank_charges=self.bank_charges_spin.value(),
                    swift_code=self.swift_code_edit.text().strip(),
                    purpose=self.purpose_edit.toPlainText().strip(),
                    notes=self.notes_edit.toPlainText().strip(),
                    created_by="النظام",  # يمكن تحديثه لاحقاً بنظام المستخدمين
                    status="pending"
                )

                session.add(remittance)
                session.flush()  # للحصول على معرف الحوالة

                # إضافة تفاصيل الموردين
                for detail in supplier_details:
                    remittance_detail = SupplierRemittanceDetail(
                        remittance_id=remittance.id,
                        supplier_id=detail['supplier_id'],
                        amount=detail['amount'],
                        amount_in_base_currency=detail['amount'] * self.exchange_rate_spin.value(),
                        payment_purpose=detail['purpose'],
                        invoice_numbers=detail['invoices'],
                        notes=detail['notes']
                    )
                    session.add(remittance_detail)

                session.commit()

                QMessageBox.information(self, "نجح", "تم إضافة الحوالة بنجاح!")
                self.clear_form()
                self.load_remittances()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الحوالة: {str(e)}")
            finally:
                session.close()

    def update_remittance(self):
        """تحديث حوالة موجودة"""
        if not self.current_remittance_id:
            return

        if not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(self.current_remittance_id)
            if not remittance:
                QMessageBox.warning(self, "خطأ", "الحوالة غير موجودة")
                return

            if remittance.status not in ["pending", "sent"]:
                QMessageBox.warning(self, "تحذير", "لا يمكن تعديل حوالة مؤكدة أو مرحلة")
                return

            # تحديث البيانات
            remittance.remittance_date = self.remittance_date_edit.date().toPython()
            remittance.sender_bank_name = self.sender_bank_edit.text().strip()
            remittance.sender_account_number = self.sender_account_edit.text().strip()
            remittance.receiver_bank_name = self.receiver_bank_edit.text().strip()
            remittance.receiver_bank_country = self.receiver_country_edit.text().strip()
            remittance.currency_id = self.currency_combo.currentData()
            remittance.exchange_rate = self.exchange_rate_spin.value()
            remittance.transfer_fees = self.transfer_fees_spin.value()
            remittance.bank_charges = self.bank_charges_spin.value()
            remittance.swift_code = self.swift_code_edit.text().strip()
            remittance.purpose = self.purpose_edit.toPlainText().strip()
            remittance.notes = self.notes_edit.toPlainText().strip()

            # إعادة حساب المبلغ بالعملة الأساسية
            remittance.amount_in_base_currency = remittance.total_amount * remittance.exchange_rate

            session.commit()

            QMessageBox.information(self, "نجح", "تم تحديث الحوالة بنجاح!")
            self.load_remittances()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الحوالة: {str(e)}")
        finally:
            session.close()

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.sender_bank_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البنك المرسل")
            self.sender_bank_edit.setFocus()
            return False

        if not self.receiver_bank_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البنك المستقبل")
            self.receiver_bank_edit.setFocus()
            return False

        if not self.currency_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار العملة")
            self.currency_combo.setFocus()
            return False

        if self.exchange_rate_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صرف صحيح")
            self.exchange_rate_spin.setFocus()
            return False

        return True

    def clear_form(self):
        """مسح النموذج"""
        self.current_remittance_id = None
        self.remittance_number_edit.clear()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.sender_bank_edit.clear()
        self.sender_account_edit.clear()
        self.receiver_bank_edit.clear()
        self.receiver_country_edit.clear()
        self.swift_code_edit.clear()
        self.exchange_rate_spin.setValue(1.0)
        self.transfer_fees_spin.setValue(0)
        self.bank_charges_spin.setValue(0)
        self.purpose_edit.clear()
        self.notes_edit.clear()

        # إعادة تفعيل زر الإضافة
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def load_selected_remittance(self):
        """تحميل بيانات الحوالة المحددة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            return

        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
        if not remittance_id:
            return

        session = self.db_manager.get_session()
        try:
            remittance = session.query(SupplierRemittance).get(remittance_id)
            if not remittance:
                return

            self.current_remittance_id = remittance.id

            # تحميل البيانات في النموذج
            self.remittance_number_edit.setText(remittance.remittance_number)
            self.remittance_date_edit.setDate(QDate.fromString(str(remittance.remittance_date), "yyyy-MM-dd"))
            self.sender_bank_edit.setText(remittance.sender_bank_name or "")
            self.sender_account_edit.setText(remittance.sender_account_number or "")
            self.receiver_bank_edit.setText(remittance.receiver_bank_name or "")
            self.receiver_country_edit.setText(remittance.receiver_bank_country or "")
            self.swift_code_edit.setText(remittance.swift_code or "")
            self.exchange_rate_spin.setValue(remittance.exchange_rate or 1.0)
            self.transfer_fees_spin.setValue(remittance.transfer_fees or 0)
            self.bank_charges_spin.setValue(remittance.bank_charges or 0)
            self.purpose_edit.setPlainText(remittance.purpose or "")
            self.notes_edit.setPlainText(remittance.notes or "")

            # تحديد العملة
            if remittance.currency_id:
                for i in range(self.currency_combo.count()):
                    if self.currency_combo.itemData(i) == remittance.currency_id:
                        self.currency_combo.setCurrentIndex(i)
                        break

            # تفعيل زر التحديث
            self.add_button.setEnabled(False)
            self.update_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل بيانات الحوالة: {str(e)}")
        finally:
            session.close()

    def open_remittance_details(self):
        """فتح نافذة تفاصيل الموردين"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حوالة")
            return

        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)

        from .supplier_remittance_details_dialog import SupplierRemittanceDetailsDialog

        dialog = SupplierRemittanceDetailsDialog(self, remittance_id)
        dialog.exec()

    def send_remittance(self):
        """إرسال الحوالة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            return

        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الإرسال",
            "هل أنت متأكد من إرسال هذه الحوالة؟\nلن يمكن التراجع عن هذا الإجراء.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(remittance_id)
                if remittance and remittance.status == "pending":
                    remittance.status = "sent"
                    remittance.sent_date = datetime.now()
                    session.commit()

                    QMessageBox.information(self, "نجح", "تم إرسال الحوالة بنجاح!")
                    self.load_remittances()
                    self.update_buttons_state()
                else:
                    QMessageBox.warning(self, "خطأ", "لا يمكن إرسال هذه الحوالة")

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"خطأ في إرسال الحوالة: {str(e)}")
            finally:
                session.close()

    def confirm_remittance(self):
        """تأكيد استلام الحوالة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            return

        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الاستلام",
            "هل تم تأكيد استلام الحوالة من البنك الخارجي؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(remittance_id)
                if remittance and remittance.status == "sent":
                    remittance.status = "confirmed"
                    remittance.confirmation_date = datetime.now()
                    remittance.confirmed_by = "النظام"  # يمكن تحديثه لاحقاً
                    session.commit()

                    QMessageBox.information(self, "نجح", "تم تأكيد استلام الحوالة بنجاح!")
                    self.load_remittances()
                    self.update_buttons_state()
                else:
                    QMessageBox.warning(self, "خطأ", "لا يمكن تأكيد هذه الحوالة")

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"خطأ في تأكيد الحوالة: {str(e)}")
            finally:
                session.close()

    def post_remittance(self):
        """ترحيل الحوالة للحسابات"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            return

        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الترحيل",
            "هل أنت متأكد من ترحيل هذه الحوالة لحسابات الموردين؟\nسيتم خصم المبالغ من حسابات الموردين المعنيين.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(remittance_id)
                if remittance and remittance.status == "confirmed":
                    # ترحيل الحوالة الرئيسية
                    remittance.status = "posted"
                    remittance.posting_date = datetime.now()
                    remittance.posted_by = "النظام"  # يمكن تحديثه لاحقاً

                    # ترحيل تفاصيل الموردين
                    for detail in remittance.remittance_details:
                        detail.is_posted = True
                        detail.posting_date = datetime.now()

                        # هنا يمكن إضافة منطق ترحيل المبالغ لحسابات الموردين
                        # مثل إنشاء قيود محاسبية أو تحديث أرصدة الموردين

                    session.commit()

                    QMessageBox.information(self, "نجح", "تم ترحيل الحوالة للحسابات بنجاح!")
                    self.load_remittances()
                    self.update_buttons_state()
                else:
                    QMessageBox.warning(self, "خطأ", "لا يمكن ترحيل هذه الحوالة")

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"خطأ في ترحيل الحوالة: {str(e)}")
            finally:
                session.close()

    def delete_remittance(self):
        """حذف حوالة"""
        current_row = self.remittances_table.currentRow()
        if current_row < 0:
            return

        remittance_id = self.remittances_table.item(current_row, 0).data(Qt.UserRole)
        remittance_number = self.remittances_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الحوالة {remittance_number}؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                remittance = session.query(SupplierRemittance).get(remittance_id)
                if remittance:
                    if remittance.status in ["confirmed", "posted"]:
                        QMessageBox.warning(self, "تحذير", "لا يمكن حذف حوالة مؤكدة أو مرحلة")
                        return

                    # حذف ناعم
                    remittance.is_active = False
                    session.commit()

                    QMessageBox.information(self, "نجح", "تم حذف الحوالة بنجاح!")
                    self.clear_form()
                    self.load_remittances()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الحوالة: {str(e)}")
            finally:
                session.close()
