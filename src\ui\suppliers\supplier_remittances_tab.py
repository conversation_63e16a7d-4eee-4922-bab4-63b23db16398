# -*- coding: utf-8 -*-
"""
تبويب حوالات الموردين الجديد - تصميم محسن وخالي من التداخل
New Supplier Remittances Tab - Enhanced Design Without Overlapping
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QComboBox, QDateEdit,
                               QTextEdit, QAbstractItemView, QDoubleSpinBox, QFrame,
                               QSplitter, QScrollArea, QGridLayout, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, QDate, QTimer, Signal
from PySide6.QtGui import QFont, QIcon

from ...database.database_manager import DatabaseManager
# from ...models.supplier_remittance import SupplierRemittance  # سيتم إنشاؤه لاحقاً


class SupplierRemittancesTab(QWidget):
    """تبويب حوالات الموردين الجديد - تصميم محسن"""
    
    # إشارات
    remittance_added = Signal(dict)
    remittance_updated = Signal(dict)
    remittance_deleted = Signal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        
        # متغيرات النظام
        self.current_remittance_id = None
        self.is_editing = False
        
        # إعداد الواجهة الجديدة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        print("تم إنشاء تبويب حوالات الموردين الجديد بنجاح")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # عنوان القسم
        title_label = QLabel("📋 إدارة حوالات الموردين")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 12px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء Splitter للتقسيم المحسن
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)  # منع طي الأقسام
        main_layout.addWidget(splitter)
        
        # الجانب الأيسر - النموذج المحسن
        form_widget = self.create_enhanced_form_section()
        splitter.addWidget(form_widget)
        
        # الجانب الأيمن - الجدول المحسن
        table_widget = self.create_enhanced_table_section()
        splitter.addWidget(table_widget)
        
        # تحديد نسب التقسيم المحسنة - مساحة أكبر للنموذج
        splitter.setSizes([900, 700])  # النموذج أكبر من الجدول
        
        # شريط الأزرار السفلي
        buttons_layout = self.create_buttons_section()
        main_layout.addLayout(buttons_layout)
        
    def create_enhanced_form_section(self):
        """إنشاء قسم النموذج المحسن"""
        # إطار النموذج الرئيسي
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 5px;
            }
        """)
        
        # تخطيط النموذج مع scroll area لتجنب التداخل
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # الويدجت الداخلي للنموذج
        form_content = QWidget()
        form_content.setMinimumWidth(800)  # عرض أدنى كبير
        
        form_layout = QVBoxLayout(form_content)
        form_layout.setSpacing(20)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # قسم المعلومات الأساسية
        basic_section = self.create_basic_info_section()
        form_layout.addWidget(basic_section)
        
        # قسم معلومات البنوك
        banks_section = self.create_banks_info_section()
        form_layout.addWidget(banks_section)
        
        # قسم الرسوم والمعلومات الإضافية
        fees_section = self.create_fees_info_section()
        form_layout.addWidget(fees_section)
        
        # مساحة مرنة في النهاية
        form_layout.addStretch()
        
        # إضافة المحتوى إلى scroll area
        scroll_area.setWidget(form_content)
        
        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(form_frame)
        frame_layout.setContentsMargins(5, 5, 5, 5)
        frame_layout.addWidget(scroll_area)
        
        return form_frame
        
    def create_basic_info_section(self):
        """إنشاء قسم المعلومات الأساسية"""
        group = QGroupBox("📝 معلومات الحوالة الأساسية")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #3498db;
            }
        """)
        
        # استخدام Grid Layout للتنظيم الأفضل
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)
        
        # الصف الأول: رقم الحوالة وتاريخ الحوالة
        # رقم الحوالة
        layout.addWidget(QLabel("رقم الحوالة:"), 0, 0)
        self.remittance_number_edit = QLineEdit()
        self.remittance_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.remittance_number_edit.setReadOnly(True)
        self.remittance_number_edit.setMinimumHeight(45)
        self.remittance_number_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.remittance_number_edit, 0, 1)
        
        # تاريخ الحوالة
        layout.addWidget(QLabel("تاريخ الحوالة:"), 0, 2)
        self.remittance_date_edit = QDateEdit()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.remittance_date_edit.setCalendarPopup(True)
        self.remittance_date_edit.setMinimumHeight(45)
        self.remittance_date_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.remittance_date_edit, 0, 3)
        
        # الصف الثاني: العملة وسعر الصرف
        # العملة
        layout.addWidget(QLabel("العملة:"), 1, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.setMinimumHeight(45)
        self.currency_combo.setStyleSheet(self.get_combo_style())
        layout.addWidget(self.currency_combo, 1, 1)
        
        # سعر الصرف
        layout.addWidget(QLabel("سعر الصرف:"), 1, 2)
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.001, 999999)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0)
        self.exchange_rate_spin.setMinimumHeight(45)
        self.exchange_rate_spin.setStyleSheet(self.get_input_style())
        layout.addWidget(self.exchange_rate_spin, 1, 3)
        
        return group

    def create_banks_info_section(self):
        """إنشاء قسم معلومات البنوك"""
        group = QGroupBox("🏦 معلومات البنوك")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #e74c3c;
            }
        """)

        # استخدام Grid Layout للتنظيم الأفضل
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)

        # الصف الأول: البنك المرسل ورقم الحساب
        layout.addWidget(QLabel("البنك المرسل:"), 0, 0)
        self.sender_bank_edit = QLineEdit()
        self.sender_bank_edit.setPlaceholderText("اسم البنك المرسل")
        self.sender_bank_edit.setMinimumHeight(45)
        self.sender_bank_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.sender_bank_edit, 0, 1)

        layout.addWidget(QLabel("رقم الحساب المرسل:"), 0, 2)
        self.sender_account_edit = QLineEdit()
        self.sender_account_edit.setPlaceholderText("رقم الحساب المرسل")
        self.sender_account_edit.setMinimumHeight(45)
        self.sender_account_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.sender_account_edit, 0, 3)

        # الصف الثاني: البنك المستقبل ودولة البنك
        layout.addWidget(QLabel("البنك المستقبل:"), 1, 0)
        self.receiver_bank_edit = QLineEdit()
        self.receiver_bank_edit.setPlaceholderText("اسم البنك المستقبل")
        self.receiver_bank_edit.setMinimumHeight(45)
        self.receiver_bank_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.receiver_bank_edit, 1, 1)

        layout.addWidget(QLabel("دولة البنك:"), 1, 2)
        self.receiver_country_edit = QLineEdit()
        self.receiver_country_edit.setPlaceholderText("دولة البنك المستقبل")
        self.receiver_country_edit.setMinimumHeight(45)
        self.receiver_country_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.receiver_country_edit, 1, 3)

        # الصف الثالث: كود SWIFT (يمتد عبر عمودين)
        layout.addWidget(QLabel("كود SWIFT:"), 2, 0)
        self.swift_code_edit = QLineEdit()
        self.swift_code_edit.setPlaceholderText("كود SWIFT للتحويلات الدولية")
        self.swift_code_edit.setMinimumHeight(45)
        self.swift_code_edit.setStyleSheet(self.get_input_style())
        layout.addWidget(self.swift_code_edit, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        return group

    def create_fees_info_section(self):
        """إنشاء قسم الرسوم والمعلومات الإضافية"""
        group = QGroupBox("💰 الرسوم والمعلومات الإضافية")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: #f8f9fa;
                color: #f39c12;
            }
        """)

        # استخدام Grid Layout للتنظيم الأفضل
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 30, 25, 25)

        # الصف الأول: رسوم التحويل ورسوم البنك
        layout.addWidget(QLabel("رسوم التحويل:"), 0, 0)
        self.transfer_fees_spin = QDoubleSpinBox()
        self.transfer_fees_spin.setRange(0, 999999)
        self.transfer_fees_spin.setDecimals(2)
        self.transfer_fees_spin.setSuffix(" ريال")
        self.transfer_fees_spin.setMinimumHeight(45)
        self.transfer_fees_spin.setStyleSheet(self.get_input_style())
        layout.addWidget(self.transfer_fees_spin, 0, 1)

        layout.addWidget(QLabel("رسوم البنك:"), 0, 2)
        self.bank_charges_spin = QDoubleSpinBox()
        self.bank_charges_spin.setRange(0, 999999)
        self.bank_charges_spin.setDecimals(2)
        self.bank_charges_spin.setSuffix(" ريال")
        self.bank_charges_spin.setMinimumHeight(45)
        self.bank_charges_spin.setStyleSheet(self.get_input_style())
        layout.addWidget(self.bank_charges_spin, 0, 3)

        # الصف الثاني: الغرض من التحويل (يمتد عبر جميع الأعمدة)
        layout.addWidget(QLabel("الغرض من التحويل:"), 1, 0)
        self.purpose_edit = QTextEdit()
        self.purpose_edit.setMaximumHeight(80)
        self.purpose_edit.setMinimumHeight(80)
        self.purpose_edit.setPlaceholderText("الغرض من التحويل...")
        self.purpose_edit.setStyleSheet(self.get_text_style())
        layout.addWidget(self.purpose_edit, 1, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # الصف الثالث: الملاحظات (يمتد عبر جميع الأعمدة)
        layout.addWidget(QLabel("الملاحظات:"), 2, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setMinimumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        self.notes_edit.setStyleSheet(self.get_text_style())
        layout.addWidget(self.notes_edit, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        return group

    def create_enhanced_table_section(self):
        """إنشاء قسم الجدول المحسن"""
        table_frame = QFrame()
        table_frame.setFrameStyle(QFrame.StyledPanel)
        table_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)
        table_layout.setSpacing(10)

        # عنوان الجدول
        table_title = QLabel("📊 قائمة الحوالات")
        table_title.setFont(QFont("Arial", 14, QFont.Bold))
        table_title.setAlignment(Qt.AlignCenter)
        table_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)
        table_layout.addWidget(table_title)

        # الجدول
        self.remittances_table = QTableWidget()
        self.remittances_table.setColumnCount(8)
        self.remittances_table.setHorizontalHeaderLabels([
            "رقم الحوالة", "التاريخ", "المبلغ", "العملة",
            "عدد الموردين", "الحالة", "البنك المستقبل", "الملاحظات"
        ])

        # تحسين مظهر الجدول
        self.remittances_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # إعدادات الجدول
        self.remittances_table.setAlternatingRowColors(True)
        self.remittances_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.remittances_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # ضبط عرض الأعمدة
        header = self.remittances_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحوالة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # العملة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # عدد الموردين
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.Stretch)           # البنك المستقبل
        header.setSectionResizeMode(7, QHeaderView.Stretch)           # الملاحظات

        table_layout.addWidget(self.remittances_table)

        return table_frame

    def create_buttons_section(self):
        """إنشاء قسم الأزرار"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر حوالة جديدة
        self.new_btn = QPushButton("🆕 حوالة جديدة")
        self.new_btn.setMinimumHeight(45)
        self.new_btn.setStyleSheet(self.get_button_style("#27ae60"))
        buttons_layout.addWidget(self.new_btn)

        # زر حفظ
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.setMinimumHeight(45)
        self.save_btn.setStyleSheet(self.get_button_style("#3498db"))
        buttons_layout.addWidget(self.save_btn)

        # زر تعديل
        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setMinimumHeight(45)
        self.edit_btn.setStyleSheet(self.get_button_style("#f39c12"))
        buttons_layout.addWidget(self.edit_btn)

        # زر حذف
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setMinimumHeight(45)
        self.delete_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        buttons_layout.addWidget(self.delete_btn)

        # مساحة مرنة
        buttons_layout.addStretch()

        # زر تحديث
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setMinimumHeight(45)
        self.refresh_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        buttons_layout.addWidget(self.refresh_btn)

        return buttons_layout

    def get_input_style(self):
        """الحصول على نمط الحقول"""
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QLineEdit:read-only {
                background-color: #ecf0f1;
                color: #7f8c8d;
            }
        """

    def get_combo_style(self):
        """الحصول على نمط القوائم المنسدلة"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
            }
        """

    def get_text_style(self):
        """الحصول على نمط النصوص المتعددة الأسطر"""
        return """
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: white;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_button_style(self, color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
                transform: translateY(0px);
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل بسيط للألوان الشائعة
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#e74c3c": "#c0392b",
            "#9b59b6": "#8e44ad"
        }
        return color_map.get(color, color)

    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        # اتصالات الأزرار
        self.new_btn.clicked.connect(self.add_new_remittance)
        self.save_btn.clicked.connect(self.save_current_remittance)
        self.edit_btn.clicked.connect(self.edit_selected_remittance)
        self.delete_btn.clicked.connect(self.delete_selected_remittance)
        self.refresh_btn.clicked.connect(self.refresh_data)

        # اتصالات الجدول
        self.remittances_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.remittances_table.itemDoubleClicked.connect(self.edit_selected_remittance)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل العملات
            self.load_currencies()

            # تحميل الحوالات
            self.refresh_data()

            # تعيين رقم حوالة جديد
            self.generate_new_remittance_number()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def load_currencies(self):
        """تحميل العملات"""
        try:
            self.currency_combo.clear()
            self.currency_combo.addItems([
                "ريال سعودي (SAR)",
                "دولار أمريكي (USD)",
                "يورو (EUR)",
                "جنيه إسترليني (GBP)",
                "درهم إماراتي (AED)",
                "دينار كويتي (KWD)"
            ])
            self.currency_combo.setCurrentIndex(0)  # ريال سعودي افتراضياً
        except Exception as e:
            print(f"خطأ في تحميل العملات: {e}")

    def generate_new_remittance_number(self):
        """إنشاء رقم حوالة جديد"""
        try:
            # تنسيق: REM-YYYY-NNNN
            from datetime import datetime
            year = datetime.now().year

            # إنشاء رقم حوالة مؤقت
            new_remittance_number = f"REM-{year}-0001"
            self.remittance_number_edit.setText(new_remittance_number)

        except Exception as e:
            print(f"خطأ في إنشاء رقم الحوالة: {e}")
            self.remittance_number_edit.setText("REM-2024-0001")

    def add_new_remittance(self):
        """إضافة حوالة جديدة"""
        try:
            self.clear_form()
            self.generate_new_remittance_number()
            self.current_remittance_id = None
            self.is_editing = False

            # تفعيل النموذج
            self.set_form_enabled(True)

            QMessageBox.information(self, "حوالة جديدة", "تم إعداد نموذج حوالة جديدة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء حوالة جديدة: {str(e)}")

    def save_current_remittance(self):
        """حفظ الحوالة الحالية"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            # جمع البيانات من النموذج
            remittance_data = self.get_form_data()

            # حفظ مؤقت - سيتم ربطه بقاعدة البيانات لاحقاً
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ الحوالة بنجاح (مؤقت)")
            self.remittance_added.emit(remittance_data)

            # تحديث الجدول
            self.refresh_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ الحوالة: {str(e)}")

    def edit_selected_remittance(self):
        """تعديل الحوالة المحددة"""
        try:
            current_row = self.remittances_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد حوالة للتعديل")
                return

            # الحصول على معرف الحوالة
            remittance_id_item = self.remittances_table.item(current_row, 0)
            if not remittance_id_item:
                return

            remittance_number = remittance_id_item.text()

            # تحميل مؤقت - سيتم ربطه بقاعدة البيانات لاحقاً
            QMessageBox.information(self, "تعديل", f"تم تحديد الحوالة: {remittance_number}")
            self.is_editing = True
            self.set_form_enabled(True)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الحوالة: {str(e)}")

    def delete_selected_remittance(self):
        """حذف الحوالة المحددة"""
        try:
            current_row = self.remittances_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد حوالة للحذف")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(self, 'تأكيد الحذف',
                                       'هل أنت متأكد من حذف هذه الحوالة؟\nلا يمكن التراجع عن هذا الإجراء.',
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)

            if reply != QMessageBox.Yes:
                return

            # الحصول على معرف الحوالة
            remittance_id_item = self.remittances_table.item(current_row, 0)
            if not remittance_id_item:
                return

            remittance_number = remittance_id_item.text()

            # حذف مؤقت - سيتم ربطه بقاعدة البيانات لاحقاً
            QMessageBox.information(self, "نجح الحذف", f"تم حذف الحوالة: {remittance_number}")

            # حذف الصف من الجدول
            self.remittances_table.removeRow(current_row)

            # مسح النموذج
            self.clear_form()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الحوالة: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # إضافة بيانات تجريبية للاختبار
            self.remittances_table.setRowCount(3)

            # حوالة تجريبية 1
            self.remittances_table.setItem(0, 0, QTableWidgetItem("REM-2024-0001"))
            self.remittances_table.setItem(0, 1, QTableWidgetItem("2024-01-15"))
            self.remittances_table.setItem(0, 2, QTableWidgetItem("5000.00"))
            self.remittances_table.setItem(0, 3, QTableWidgetItem("ريال سعودي"))
            self.remittances_table.setItem(0, 4, QTableWidgetItem("2"))
            self.remittances_table.setItem(0, 5, QTableWidgetItem("مسودة"))
            self.remittances_table.setItem(0, 6, QTableWidgetItem("HSBC Bank"))
            self.remittances_table.setItem(0, 7, QTableWidgetItem("دفع مستحقات الموردين"))

            # حوالة تجريبية 2
            self.remittances_table.setItem(1, 0, QTableWidgetItem("REM-2024-0002"))
            self.remittances_table.setItem(1, 1, QTableWidgetItem("2024-01-16"))
            self.remittances_table.setItem(1, 2, QTableWidgetItem("3000.00"))
            self.remittances_table.setItem(1, 3, QTableWidgetItem("دولار أمريكي"))
            self.remittances_table.setItem(1, 4, QTableWidgetItem("1"))
            self.remittances_table.setItem(1, 5, QTableWidgetItem("مرسل"))
            self.remittances_table.setItem(1, 6, QTableWidgetItem("ANB Bank"))
            self.remittances_table.setItem(1, 7, QTableWidgetItem("تحويل عاجل"))

            # حوالة تجريبية 3
            self.remittances_table.setItem(2, 0, QTableWidgetItem("REM-2024-0003"))
            self.remittances_table.setItem(2, 1, QTableWidgetItem("2024-01-17"))
            self.remittances_table.setItem(2, 2, QTableWidgetItem("7500.00"))
            self.remittances_table.setItem(2, 3, QTableWidgetItem("يورو"))
            self.remittances_table.setItem(2, 4, QTableWidgetItem("3"))
            self.remittances_table.setItem(2, 5, QTableWidgetItem("مؤكد"))
            self.remittances_table.setItem(2, 6, QTableWidgetItem("Deutsche Bank"))
            self.remittances_table.setItem(2, 7, QTableWidgetItem("دفعة شهرية"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def get_total_remittances_count(self):
        """الحصول على إجمالي عدد الحوالات"""
        try:
            return self.remittances_table.rowCount()
        except:
            return 0

    def validate_form(self):
        """التحقق من صحة بيانات النموذج"""
        if not self.remittance_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "رقم الحوالة مطلوب")
            return False

        if not self.sender_bank_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "البنك المرسل مطلوب")
            return False

        if not self.receiver_bank_edit.text().strip():
            QMessageBox.warning(self, "خطأ في البيانات", "البنك المستقبل مطلوب")
            return False

        return True

    def get_form_data(self):
        """الحصول على بيانات النموذج"""
        return {
            'remittance_number': self.remittance_number_edit.text().strip(),
            'remittance_date': self.remittance_date_edit.date().toPython(),
            'currency': self.currency_combo.currentText(),
            'exchange_rate': self.exchange_rate_spin.value(),
            'sender_bank': self.sender_bank_edit.text().strip(),
            'sender_account': self.sender_account_edit.text().strip(),
            'receiver_bank': self.receiver_bank_edit.text().strip(),
            'receiver_country': self.receiver_country_edit.text().strip(),
            'swift_code': self.swift_code_edit.text().strip(),
            'transfer_fees': self.transfer_fees_spin.value(),
            'bank_charges': self.bank_charges_spin.value(),
            'purpose': self.purpose_edit.toPlainText().strip(),
            'notes': self.notes_edit.toPlainText().strip(),
            'status': 'draft'
        }

    def update_remittance_from_data(self, remittance, data):
        """تحديث كائن الحوالة من البيانات"""
        remittance.remittance_number = data['remittance_number']
        remittance.remittance_date = data['remittance_date']
        remittance.currency = data['currency']
        remittance.exchange_rate = data['exchange_rate']
        remittance.sender_bank = data['sender_bank']
        remittance.sender_account = data['sender_account']
        remittance.receiver_bank = data['receiver_bank']
        remittance.receiver_country = data['receiver_country']
        remittance.swift_code = data['swift_code']
        remittance.transfer_fees = data['transfer_fees']
        remittance.bank_charges = data['bank_charges']
        remittance.purpose = data['purpose']
        remittance.notes = data['notes']
        remittance.status = data['status']
        remittance.total_amount = data['transfer_fees'] + data['bank_charges']

    def load_remittance_to_form(self, remittance):
        """تحميل بيانات الحوالة إلى النموذج"""
        self.remittance_number_edit.setText(remittance.remittance_number or "")

        if remittance.remittance_date:
            self.remittance_date_edit.setDate(QDate.fromString(remittance.remittance_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        # تحديد العملة
        currency_text = remittance.currency or ""
        index = self.currency_combo.findText(currency_text, Qt.MatchContains)
        if index >= 0:
            self.currency_combo.setCurrentIndex(index)

        self.exchange_rate_spin.setValue(remittance.exchange_rate or 1.0)
        self.sender_bank_edit.setText(remittance.sender_bank or "")
        self.sender_account_edit.setText(remittance.sender_account or "")
        self.receiver_bank_edit.setText(remittance.receiver_bank or "")
        self.receiver_country_edit.setText(remittance.receiver_country or "")
        self.swift_code_edit.setText(remittance.swift_code or "")
        self.transfer_fees_spin.setValue(remittance.transfer_fees or 0.0)
        self.bank_charges_spin.setValue(remittance.bank_charges or 0.0)
        self.purpose_edit.setPlainText(remittance.purpose or "")
        self.notes_edit.setPlainText(remittance.notes or "")

    def clear_form(self):
        """مسح النموذج"""
        self.remittance_number_edit.clear()
        self.remittance_date_edit.setDate(QDate.currentDate())
        self.currency_combo.setCurrentIndex(0)
        self.exchange_rate_spin.setValue(1.0)
        self.sender_bank_edit.clear()
        self.sender_account_edit.clear()
        self.receiver_bank_edit.clear()
        self.receiver_country_edit.clear()
        self.swift_code_edit.clear()
        self.transfer_fees_spin.setValue(0.0)
        self.bank_charges_spin.setValue(0.0)
        self.purpose_edit.clear()
        self.notes_edit.clear()

        self.current_remittance_id = None
        self.is_editing = False

    def set_form_enabled(self, enabled):
        """تفعيل/تعطيل النموذج"""
        # المعلومات الأساسية (عدا رقم الحوالة)
        self.remittance_date_edit.setEnabled(enabled)
        self.currency_combo.setEnabled(enabled)
        self.exchange_rate_spin.setEnabled(enabled)

        # معلومات البنوك
        self.sender_bank_edit.setEnabled(enabled)
        self.sender_account_edit.setEnabled(enabled)
        self.receiver_bank_edit.setEnabled(enabled)
        self.receiver_country_edit.setEnabled(enabled)
        self.swift_code_edit.setEnabled(enabled)

        # الرسوم والمعلومات الإضافية
        self.transfer_fees_spin.setEnabled(enabled)
        self.bank_charges_spin.setEnabled(enabled)
        self.purpose_edit.setEnabled(enabled)
        self.notes_edit.setEnabled(enabled)

        # الأزرار
        self.save_btn.setEnabled(enabled)

    def on_selection_changed(self):
        """التعامل مع تغيير التحديد في الجدول"""
        current_row = self.remittances_table.currentRow()
        has_selection = current_row >= 0

        # تفعيل/تعطيل أزرار التحرير والحذف
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
