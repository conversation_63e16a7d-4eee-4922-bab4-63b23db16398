# -*- coding: utf-8 -*-
"""
اختبار الواجهات المحسنة
Test Fixed UI Components
"""

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
import sys

# إضافة مسار المشروع
sys.path.insert(0, 'src')

from src.ui.suppliers.advanced_supplier_search_dialog import AdvancedSupplierSearchDialog
from src.ui.suppliers.multi_currency_selection_dialog import MultiCurrencySelectionDialog
from src.ui.suppliers.suppliers_data import SuppliersDataWindow


class TestWindow(QMainWindow):
    """نافذة اختبار للواجهات المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار الواجهات المحسنة - ProShipment")
        self.setMinimumSize(400, 300)
        self.resize(500, 400)
        
        # تطبيق الخط العربي
        font = QFont("Segoe UI", 12)
        self.setFont(font)
        
        # تطبيق تخطيط RTL
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title_label = QPushButton("🧪 اختبار الواجهات المحسنة")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر اختبار البحث المتقدم
        search_button = QPushButton("🔍 اختبار البحث المتقدم للموردين")
        search_button.clicked.connect(self.test_advanced_search)
        search_button.setMinimumHeight(50)
        search_button.setStyleSheet(self.get_button_style("#27ae60"))
        buttons_layout.addWidget(search_button)
        
        # زر اختبار اختيار العملات
        currency_button = QPushButton("💰 اختبار اختيار العملات المتعددة")
        currency_button.clicked.connect(self.test_currency_selection)
        currency_button.setMinimumHeight(50)
        currency_button.setStyleSheet(self.get_button_style("#e67e22"))
        buttons_layout.addWidget(currency_button)
        
        # زر اختبار النظام الكامل
        full_button = QPushButton("🏢 اختبار النظام الكامل")
        full_button.clicked.connect(self.test_full_system)
        full_button.setMinimumHeight(50)
        full_button.setStyleSheet(self.get_button_style("#9b59b6"))
        buttons_layout.addWidget(full_button)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # معلومات
        info_label = QPushButton("✅ تم إصلاح مشكلة تراكب الحقول والتسميات المقصوصة")
        info_label.setEnabled(False)
        info_label.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        layout.addWidget(info_label)
        
        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)
    
    def get_button_style(self, color):
        """الحصول على نمط الزر"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """
    
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل بسيط للألوان
        color_map = {
            "#27ae60": "#229954",
            "#e67e22": "#d35400", 
            "#9b59b6": "#8e44ad"
        }
        return color_map.get(color, color)
    
    def test_advanced_search(self):
        """اختبار نافذة البحث المتقدم"""
        try:
            dialog = AdvancedSupplierSearchDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"خطأ في اختبار البحث المتقدم: {e}")
    
    def test_currency_selection(self):
        """اختبار نافذة اختيار العملات"""
        try:
            dialog = MultiCurrencySelectionDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"خطأ في اختبار اختيار العملات: {e}")
    
    def test_full_system(self):
        """اختبار النظام الكامل"""
        try:
            window = SuppliersDataWindow()
            window.show()
        except Exception as e:
            print(f"خطأ في اختبار النظام الكامل: {e}")


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق خط عربي على التطبيق كاملاً
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    window = TestWindow()
    window.show()
    
    print("🚀 نافذة اختبار الواجهات المحسنة")
    print("=" * 50)
    print("✅ تم إصلاح مشكلة تراكب الحقول")
    print("✅ تم إصلاح مشكلة التسميات المقصوصة")
    print("✅ تحسين تخطيط النوافذ")
    print("✅ أحجام ثابتة ومناسبة للعناصر")
    print("")
    print("🔍 اختبر البحث المتقدم للموردين")
    print("💰 اختبر اختيار العملات المتعددة")
    print("🏢 اختبر النظام الكامل")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
