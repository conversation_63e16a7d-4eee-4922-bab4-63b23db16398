#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت اختبار إصلاحات تاريخ الشحنة والتواريخ في الأصناف
"""

import sys
import os
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_shipment_date_saving():
    """اختبار حفظ تاريخ الشحنة"""
    try:
        from src.database.models import Shipment
        from src.database.database_manager import DatabaseManager
        
        print("🧪 اختبار حفظ تاريخ الشحنة...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن شحنة موجودة
        shipment = session.query(Shipment).first()
        if shipment:
            print(f"📦 اختبار الشحنة: {shipment.shipment_number}")
            print(f"   - تاريخ الشحنة: {shipment.shipment_date}")
            print(f"   - تاريخ الإنشاء: {shipment.created_at}")
            
            # إذا لم يكن هناك تاريخ شحنة، استخدم تاريخ الإنشاء
            if not shipment.shipment_date and shipment.created_at:
                shipment.shipment_date = shipment.created_at
                session.commit()
                print("✅ تم تحديث تاريخ الشحنة!")
            
            print("✅ حقل تاريخ الشحنة يعمل بشكل صحيح!")
        else:
            print("ℹ️ لا توجد شحنات للاختبار")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تاريخ الشحنة: {str(e)}")
        return False

def test_shipment_item_dates():
    """اختبار تواريخ أصناف الشحنة"""
    try:
        from src.database.models import ShipmentItem
        from src.database.database_manager import DatabaseManager
        
        print("\n🧪 اختبار تواريخ أصناف الشحنة...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن صنف شحنة موجود
        shipment_item = session.query(ShipmentItem).first()
        if shipment_item:
            print(f"📦 اختبار صنف الشحنة ID: {shipment_item.id}")
            
            # اختبار الوصول لحقول التاريخ
            production_date = getattr(shipment_item, 'production_date', 'غير موجود')
            expiry_date = getattr(shipment_item, 'expiry_date', 'غير موجود')
            
            print(f"   - تاريخ الإنتاج: {production_date}")
            print(f"   - تاريخ الانتهاء: {expiry_date}")
            
            # اختبار إضافة تواريخ جديدة
            if not shipment_item.production_date:
                shipment_item.production_date = datetime.now()
                print("   - تم إضافة تاريخ إنتاج تجريبي")
            
            if not shipment_item.expiry_date:
                from datetime import timedelta
                shipment_item.expiry_date = datetime.now() + timedelta(days=365)
                print("   - تم إضافة تاريخ انتهاء تجريبي")
            
            session.commit()
            print("✅ حقول تواريخ الأصناف تعمل بشكل صحيح!")
        else:
            print("ℹ️ لا توجد أصناف شحنة للاختبار")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تواريخ الأصناف: {str(e)}")
        return False

def test_main_window_date_display():
    """اختبار عرض التاريخ في النافذة الرئيسية"""
    try:
        from src.database.models import Shipment
        from src.database.database_manager import DatabaseManager
        
        print("\n🧪 اختبار عرض التاريخ في النافذة الرئيسية...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # البحث عن شحنات مع تواريخ
        shipments = session.query(Shipment).filter(
            Shipment.shipment_date.isnot(None)
        ).limit(5).all()
        
        if shipments:
            print(f"📦 تم العثور على {len(shipments)} شحنة مع تواريخ:")
            for shipment in shipments:
                print(f"   - {shipment.shipment_number}: {shipment.shipment_date}")
            print("✅ التواريخ متوفرة للعرض في النافذة الرئيسية!")
        else:
            print("ℹ️ لا توجد شحنات مع تواريخ")
            
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عرض التاريخ: {str(e)}")
        return False

def test_database_schema():
    """اختبار مخطط قاعدة البيانات"""
    try:
        from src.database.database_manager import DatabaseManager
        from sqlalchemy import text
        
        print("\n🧪 اختبار مخطط قاعدة البيانات...")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # فحص جدول الشحنات
        result = session.execute(text("PRAGMA table_info(shipments)"))
        shipments_columns = [row[1] for row in result.fetchall()]
        
        print(f"📋 أعمدة جدول الشحنات: {len(shipments_columns)} عمود")
        if 'shipment_date' in shipments_columns:
            print("✅ حقل shipment_date موجود في جدول الشحنات")
        else:
            print("❌ حقل shipment_date مفقود من جدول الشحنات")
        
        # فحص جدول أصناف الشحنة
        result = session.execute(text("PRAGMA table_info(shipment_items)"))
        items_columns = [row[1] for row in result.fetchall()]
        
        print(f"📋 أعمدة جدول أصناف الشحنة: {len(items_columns)} عمود")
        if 'production_date' in items_columns:
            print("✅ حقل production_date موجود في جدول أصناف الشحنة")
        else:
            print("❌ حقل production_date مفقود من جدول أصناف الشحنة")
            
        if 'expiry_date' in items_columns:
            print("✅ حقل expiry_date موجود في جدول أصناف الشحنة")
        else:
            print("❌ حقل expiry_date مفقود من جدول أصناف الشحنة")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مخطط قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 اختبار إصلاحات تاريخ الشحنة والأصناف")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # تشغيل الاختبارات
    if test_database_schema():
        tests_passed += 1
    
    if test_shipment_date_saving():
        tests_passed += 1
    
    if test_shipment_item_dates():
        tests_passed += 1
    
    if test_main_window_date_display():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبار نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("⚠️ بعض الاختبارات فشلت!")
        sys.exit(1)
