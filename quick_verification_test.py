#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتحقق من فصل التعبئة التلقائية
Quick Verification Test for Auto-Fill Separation
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_widget_import():
    """اختبار استيراد الواجهة الذكية"""
    try:
        from src.ui.widgets.smart_shipping_company_widget import SmartShippingCompanyWidget
        print("✅ تم استيراد SmartShippingCompanyWidget بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في استيراد SmartShippingCompanyWidget: {e}")
        return False

def test_widget_functionality():
    """اختبار وظائف الواجهة الذكية"""
    try:
        from src.ui.widgets.smart_shipping_company_widget import SmartShippingCompanyWidget
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        widget = SmartShippingCompanyWidget()
        
        # اختبار الوظائف الجديدة
        tests = [
            ("is_auto_fill_enabled", lambda: widget.is_auto_fill_enabled()),
            ("set_auto_fill_enabled", lambda: widget.set_auto_fill_enabled(False)),
            ("set_company_name_with_auto_validate", lambda: widget.set_company_name("TEST", auto_validate=False)),
            ("get_company_name", lambda: widget.get_company_name())
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                print(f"✅ {test_name}: {result}")
            except Exception as e:
                print(f"❌ {test_name}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار وظائف الواجهة: {e}")
        return False

def test_window_import():
    """اختبار استيراد نافذة الشحنة"""
    try:
        from src.ui.shipments.new_shipment_window import NewShipmentWindow
        print("✅ تم استيراد NewShipmentWindow بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في استيراد NewShipmentWindow: {e}")
        return False

def test_files_exist():
    """اختبار وجود الملفات المعدلة"""
    files_to_check = [
        "src/ui/widgets/smart_shipping_company_widget.py",
        "src/ui/shipments/new_shipment_window.py",
        "AUTO_FILL_SEPARATION_DOCUMENTATION.md",
        "test_auto_fill_separation.py"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} موجود")
        else:
            print(f"❌ {file_path} غير موجود")
            all_exist = False
    
    return all_exist

def main():
    """الدالة الرئيسية للاختبار السريع"""
    print("🔍 اختبار سريع لفصل التعبئة التلقائية")
    print("=" * 50)
    
    tests = [
        ("فحص وجود الملفات", test_files_exist),
        ("استيراد الواجهة الذكية", test_widget_import),
        ("وظائف الواجهة الذكية", test_widget_functionality),
        ("استيراد نافذة الشحنة", test_window_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        try:
            if test_func():
                print(f"✅ {test_name} - نجح")
                passed += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("✅ جميع الاختبارات السريعة نجحت!")
        print("🎯 فصل التعبئة التلقائية تم تطبيقه بنجاح")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
