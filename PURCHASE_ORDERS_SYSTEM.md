# نظام طلبات الشراء - Purchase Orders System

## نظرة عامة
تم تطوير نظام شامل لإدارة طلبات الشراء من الموردين مع التكامل الكامل مع نظام إدارة الشحنات.

## المميزات الرئيسية

### 1. إدارة طلبات الشراء
- إنشاء طلبات شراء جديدة مع ترقيم تلقائي
- تحرير وتعديل الطلبات الموجودة
- حذف الطلبات غير المطلوبة
- تتبع حالة الطلبات (مسودة، مرسل، مؤكد، جزئي، مكتمل، ملغي)

### 2. إدارة الأصناف في الطلبات
- بحث وإضافة الأصناف من قاعدة البيانات
- تحديد الكميات والأسعار لكل صنف
- حساب المبالغ الإجمالية تلقائياً
- تتبع الكميات المسلمة والمتبقية

### 3. التكامل مع الموردين
- ربط كل طلب بمورد محدد
- البحث في الموردين وإضافتهم للطلبات
- عرض معلومات المورد كاملة

### 4. إدارة العملات والأسعار
- دعم العملات المتعددة
- تحديد أسعار الصرف
- حساب الخصومات والضرائب
- عرض المبالغ الإجمالية بالعملة المحددة

### 5. التكامل مع نظام الشحنات
- نقل طلبات الشراء إلى شحنات جديدة
- نقل بيانات المورد والأصناف تلقائياً
- استخدام الكميات المتبقية عند النقل
- ربط الشحنة بطلب الشراء الأصلي

## الملفات المضافة

### 1. نماذج قاعدة البيانات
```python
# src/database/models.py
class PurchaseOrder(Base):
    """جدول طلبات الشراء من الموردين"""
    # حقول الطلب الأساسية
    
class PurchaseOrderItem(Base):
    """جدول أصناف طلبات الشراء"""
    # حقول الأصناف في الطلبات
```

### 2. واجهة طلبات الشراء
```python
# src/ui/suppliers/purchase_orders_window.py
class PurchaseOrdersWindow(QMainWindow):
    """نافذة إدارة طلبات الشراء"""
    # واجهة شاملة لإدارة الطلبات
```

### 3. نافذة البحث عن الأصناف
```python
# src/ui/items/item_search_dialog.py
class ItemSearchDialog(QDialog):
    """نافذة البحث عن الأصناف"""
    # بحث وتصفية الأصناف
```

## كيفية الاستخدام

### 1. الوصول لنظام طلبات الشراء
- من القائمة الرئيسية: إدارة الموردين
- اختر تبويب "طلبات الشراء"
- أو من قائمة البيانات: طلبات الشراء

### 2. إنشاء طلب شراء جديد
1. اضغط على زر "طلب جديد"
2. اختر المورد من قائمة البحث
3. حدد العملة وسعر الصرف
4. أضف الأصناف المطلوبة
5. احفظ الطلب

### 3. إضافة الأصناف
1. في تبويب "الأصناف"، اضغط "إضافة صنف"
2. ابحث عن الصنف المطلوب
3. حدد الكمية والسعر
4. اضغط "إضافة"

### 4. نقل الطلب إلى شحنة
1. اختر الطلب المطلوب
2. اضغط على "إرسال إلى الشحنات"
3. ستفتح نافذة شحنة جديدة مع البيانات

## حالات طلبات الشراء

| الحالة | الوصف |
|--------|-------|
| مسودة | طلب جديد لم يتم إرساله بعد |
| مرسل | تم إرسال الطلب للمورد |
| مؤكد | تم تأكيد الطلب من المورد |
| جزئي | تم استلام جزء من الطلب |
| مكتمل | تم استلام الطلب كاملاً |
| ملغي | تم إلغاء الطلب |

## الحقول المتاحة

### معلومات الطلب الأساسية
- رقم الطلب (تلقائي)
- تاريخ الطلب
- المورد
- حالة الطلب
- العملة وسعر الصرف

### المعلومات المالية
- المبلغ الإجمالي
- مبلغ الخصم
- مبلغ الضريبة
- المبلغ النهائي

### معلومات التسليم
- تاريخ التسليم المتوقع
- تاريخ التسليم الفعلي

### معلومات إضافية
- الملاحظات
- الشروط والأحكام

## التحديثات المطلوبة

### تم إنجازه ✅
- [x] إنشاء نماذج قاعدة البيانات
- [x] تطوير واجهة طلبات الشراء
- [x] إنشاء نافذة البحث عن الأصناف
- [x] التكامل مع نظام الموردين
- [x] التكامل مع نظام الشحنات
- [x] إنشاء جداول قاعدة البيانات

### قيد التطوير 🔄
- [ ] اختبار النظام بالكامل
- [ ] إضافة التقارير الخاصة بطلبات الشراء
- [ ] تحسين واجهة المستخدم

## ملاحظات تقنية

### قاعدة البيانات
- تم إنشاء جدولين جديدين: `purchase_orders` و `purchase_order_items`
- العلاقات مع الجداول الموجودة: `suppliers`, `items`, `currencies`
- دعم كامل للعمليات CRUD

### الواجهة
- تصميم بتبويبات متعددة
- دعم اللغة العربية والاتجاه من اليمين لليسار
- تكامل مع نوافذ البحث الموجودة

### الأداء
- استعلامات محسنة لقاعدة البيانات
- تحميل البيانات عند الطلب
- إدارة ذاكرة فعالة

## الدعم والصيانة
- النظام متكامل مع البنية الموجودة
- سهولة الصيانة والتطوير
- توثيق شامل للكود
