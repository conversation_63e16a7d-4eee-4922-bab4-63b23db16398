#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحديث قاعدة البيانات لإضافة حقول التواريخ
Database Update Script for Date Fields
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from sqlalchemy import text

def update_database():
    """تحديث قاعدة البيانات لإضافة حقول التواريخ"""
    
    print("🔄 بدء تحديث قاعدة البيانات...")
    
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # التحقق من وجود الحقول أولاً
        print("📋 التحقق من بنية الجدول الحالية...")
        
        # الحصول على معلومات الأعمدة
        result = session.execute(text("PRAGMA table_info(purchase_order_items)"))
        columns = [row[1] for row in result.fetchall()]
        
        print(f"✅ الأعمدة الموجودة: {columns}")
        
        # إضافة عمود تاريخ الإنتاج إذا لم يكن موجوداً
        if 'production_date' not in columns:
            print("➕ إضافة عمود تاريخ الإنتاج...")
            session.execute(text("""
                ALTER TABLE purchase_order_items 
                ADD COLUMN production_date DATE
            """))
            print("✅ تم إضافة عمود تاريخ الإنتاج")
        else:
            print("ℹ️ عمود تاريخ الإنتاج موجود بالفعل")
        
        # إضافة عمود تاريخ الانتهاء إذا لم يكن موجوداً
        if 'expiry_date' not in columns:
            print("➕ إضافة عمود تاريخ الانتهاء...")
            session.execute(text("""
                ALTER TABLE purchase_order_items 
                ADD COLUMN expiry_date DATE
            """))
            print("✅ تم إضافة عمود تاريخ الانتهاء")
        else:
            print("ℹ️ عمود تاريخ الانتهاء موجود بالفعل")
        
        # حفظ التغييرات
        session.commit()
        print("💾 تم حفظ التغييرات في قاعدة البيانات")
        
        # التحقق من النتيجة النهائية
        print("🔍 التحقق من البنية المحدثة...")
        result = session.execute(text("PRAGMA table_info(purchase_order_items)"))
        updated_columns = [row[1] for row in result.fetchall()]
        
        print(f"✅ الأعمدة بعد التحديث: {updated_columns}")
        
        session.close()
        
        print("🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("")
        print("📋 الحقول المضافة:")
        print("   - production_date: تاريخ الإنتاج")
        print("   - expiry_date: تاريخ الانتهاء")
        print("")
        print("✅ يمكنك الآن استخدام حقول التواريخ في طلبات الشراء")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False

def test_database_update():
    """اختبار التحديث"""
    print("🧪 اختبار قاعدة البيانات المحدثة...")
    
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # اختبار إدراج بيانات تجريبية
        from datetime import date
        from src.database.models import PurchaseOrderItem
        
        print("📝 اختبار إدراج بيانات تجريبية...")
        
        # لا نقوم بإدراج بيانات فعلية، فقط نتحقق من إمكانية الوصول للحقول
        print("✅ يمكن الوصول لحقول التواريخ الجديدة")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        if 'session' in locals():
            session.close()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 سكريبت تحديث قاعدة البيانات - حقول التواريخ")
    print("=" * 60)
    print("")
    
    # تحديث قاعدة البيانات
    if update_database():
        print("")
        # اختبار التحديث
        if test_database_update():
            print("")
            print("🎯 تم التحديث والاختبار بنجاح!")
            print("🚀 يمكنك الآن تشغيل تطبيق طلبات الشراء")
        else:
            print("⚠️ تم التحديث ولكن فشل الاختبار")
    else:
        print("❌ فشل في تحديث قاعدة البيانات")
    
    print("")
    print("=" * 60)
