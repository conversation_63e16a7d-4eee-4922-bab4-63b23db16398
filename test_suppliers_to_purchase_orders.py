#!/usr/bin/env python3
"""
اختبار فتح نافذة طلبات الشراء من نظام الموردين
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        print('🔍 اختبار فتح نافذة طلبات الشراء من نظام الموردين...')
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QFont
        
        app = QApplication(sys.argv)
        font = QFont('Segoe UI', 10)
        app.setFont(font)
        
        print('✅ تم إنشاء التطبيق')
        
        # استيراد نافذة الموردين
        from src.ui.suppliers.suppliers_window import SuppliersWindow
        print('✅ تم استيراد SuppliersWindow')
        
        # إنشاء نافذة الموردين
        suppliers_window = SuppliersWindow()
        print('✅ تم إنشاء نافذة الموردين')
        
        # عرض نافذة الموردين
        suppliers_window.show()
        print('✅ تم عرض نافذة الموردين')
        
        # محاولة فتح نافذة طلبات الشراء
        print('🔄 محاولة فتح نافذة طلبات الشراء...')
        suppliers_window.open_purchase_orders()
        print('✅ تم استدعاء open_purchase_orders()')
        
        # التحقق من أن النافذة تم إنشاؤها
        if hasattr(suppliers_window, 'purchase_orders_window') and suppliers_window.purchase_orders_window:
            print('✅ تم إنشاء purchase_orders_window')
            print(f'   - مرئية: {suppliers_window.purchase_orders_window.isVisible()}')
            print(f'   - العنوان: {suppliers_window.purchase_orders_window.windowTitle()}')
        else:
            print('❌ لم يتم إنشاء purchase_orders_window')
        
        print('🎉 الاختبار مكتمل!')
        
        # تشغيل التطبيق لمدة قصيرة للتأكد من عمل النافذة
        from PySide6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3 ثوان
        
        app.exec()
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
