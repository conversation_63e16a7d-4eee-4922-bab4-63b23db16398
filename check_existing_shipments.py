#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص الشحنات الموجودة في قاعدة البيانات
"""

import sys
import os
sys.path.append('.')

from src.database.database_manager import DatabaseManager
from src.database.models import Shipment

def check_existing_shipments():
    """فحص الشحنات الموجودة"""
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        print("=== فحص الشحنات الموجودة في قاعدة البيانات ===")
        
        # الحصول على جميع الشحنات
        all_shipments = session.query(Shipment).all()
        print(f"📊 إجمالي عدد الشحنات: {len(all_shipments)}")
        
        if all_shipments:
            print("\n📋 قائمة الشحنات الموجودة:")
            for i, shipment in enumerate(all_shipments, 1):
                print(f"   {i}. {shipment.shipment_number} - {shipment.supplier.name if shipment.supplier else 'بدون مورد'}")
        
        # فحص شحنات 2025
        shipments_2025 = session.query(Shipment).filter(
            Shipment.shipment_number.like('SH-2025-%')
        ).all()
        print(f"\n📅 شحنات سنة 2025: {len(shipments_2025)}")
        
        if shipments_2025:
            for shipment in shipments_2025:
                print(f"   - {shipment.shipment_number}")
        
        # فحص شحنات 2024
        shipments_2024 = session.query(Shipment).filter(
            Shipment.shipment_number.like('SH-2024-%')
        ).all()
        print(f"\n📅 شحنات سنة 2024: {len(shipments_2024)}")
        
        if shipments_2024:
            for shipment in shipments_2024:
                print(f"   - {shipment.shipment_number}")
        
        session.close()
        return len(all_shipments)
        
    except Exception as e:
        print(f"❌ خطأ في فحص الشحنات: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    count = check_existing_shipments()
    print(f"\n{'='*50}")
    print(f"✅ تم فحص {count} شحنة في قاعدة البيانات")
